<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Midjourney风格配色测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #2D1B69 0%, #1E3A8A 50%, #0891B2 100%);
            color: #333;
            min-height: 100vh;
        }
        .container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.2);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        h1 {
            color: #2D1B69;
            text-align: center;
            margin-bottom: 40px;
            font-size: 3em;
            font-weight: 700;
            background: linear-gradient(135deg, #2D1B69, #0891B2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        .color-showcase {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin: 40px 0;
        }
        .color-card {
            background: white;
            border-radius: 16px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            border: 2px solid rgba(45, 27, 105, 0.1);
        }
        .button-demo {
            display: flex;
            gap: 12px;
            align-items: center;
            margin: 20px 0;
            justify-content: center;
        }
        .demo-button {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            opacity: 0.95;
            border: 2px solid rgba(255, 255, 255, 0.1);
            position: relative;
        }
        .mj-button {
            background: linear-gradient(135deg, #2D1B69, #1E3A8A);
            box-shadow: 0 4px 16px rgba(45, 27, 105, 0.5);
        }
        .edit-button {
            background: linear-gradient(135deg, #0891B2, #0E7490);
            box-shadow: 0 4px 16px rgba(8, 145, 178, 0.5);
        }
        .edit-button.editing {
            background: linear-gradient(135deg, #EA580C, #C2410C);
            box-shadow: 0 4px 16px rgba(234, 88, 12, 0.5);
        }
        .demo-button:hover {
            opacity: 1;
            transform: scale(1.1);
        }
        .mj-button:hover {
            background: linear-gradient(135deg, #3730A3, #1E40AF);
            box-shadow: 0 6px 20px rgba(45, 27, 105, 0.7);
        }
        .edit-button:hover {
            background: linear-gradient(135deg, #0EA5E9, #0284C7);
            box-shadow: 0 6px 20px rgba(8, 145, 178, 0.7);
        }
        .edit-button.editing:hover {
            background: linear-gradient(135deg, #F97316, #EA580C);
            box-shadow: 0 6px 20px rgba(234, 88, 12, 0.7);
        }
        .color-info {
            text-align: center;
            margin-top: 15px;
        }
        .color-name {
            font-weight: 600;
            font-size: 18px;
            margin-bottom: 8px;
        }
        .color-code {
            font-family: 'SF Mono', Monaco, monospace;
            font-size: 12px;
            color: #666;
            background: #f8f9fa;
            padding: 4px 8px;
            border-radius: 4px;
            display: inline-block;
        }
        .feature-section {
            background: linear-gradient(135deg, rgba(45, 27, 105, 0.05), rgba(8, 145, 178, 0.05));
            border: 2px solid rgba(45, 27, 105, 0.2);
            padding: 30px;
            margin: 30px 0;
            border-radius: 16px;
        }
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 16px rgba(0,0,0,0.1);
        }
        .comparison-table th,
        .comparison-table td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid rgba(45, 27, 105, 0.1);
        }
        .comparison-table th {
            background: linear-gradient(135deg, #2D1B69, #1E3A8A);
            color: white;
            font-weight: 600;
        }
        .old-color {
            opacity: 0.6;
            text-decoration: line-through;
        }
        .new-color {
            font-weight: 600;
            color: #2D1B69;
        }
        .notification-demo {
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 13px;
            font-weight: 500;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            display: inline-block;
            margin: 5px;
            color: white;
        }
        .notif-save { background: #0891B2; }
        .notif-edit { background: #EA580C; }
        .notif-restore { background: #2D1B69; }
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 16px;
            font-family: 'SF Mono', Monaco, monospace;
            font-size: 14px;
            line-height: 1.5;
            margin: 15px 0;
            position: relative;
        }
        .code-block.editable {
            outline: 2px solid #0891B2;
            background-color: rgba(8, 145, 178, 0.05);
            border: 1px solid rgba(8, 145, 178, 0.2);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎨 Midjourney风格配色</h1>
        
        <div class="feature-section">
            <h2>✨ 全新的Midjourney风格配色方案</h2>
            <p><strong>采用Midjourney官方的深蓝紫色调，搭配青绿色和橙色，营造专业而现代的视觉体验</strong></p>
        </div>

        <div class="color-showcase">
            <!-- M按钮配色 -->
            <div class="color-card">
                <div class="button-demo">
                    <div class="demo-button mj-button">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="white">
                            <path d="M3,21V3H7L12,14L17,3H21V21H17V8L13,18H11L7,8V21H3Z"/>
                        </svg>
                    </div>
                </div>
                <div class="color-info">
                    <div class="color-name">M按钮 (发送)</div>
                    <div class="color-code">#2D1B69 → #1E3A8A</div>
                    <p style="font-size: 14px; color: #666; margin-top: 10px;">
                        深蓝紫色渐变，象征Midjourney的专业性
                    </p>
                </div>
            </div>

            <!-- E按钮配色 -->
            <div class="color-card">
                <div class="button-demo">
                    <div class="demo-button edit-button">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="white">
                            <path d="M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z"/>
                        </svg>
                    </div>
                </div>
                <div class="color-info">
                    <div class="color-name">E按钮 (编辑)</div>
                    <div class="color-code">#0891B2 → #0E7490</div>
                    <p style="font-size: 14px; color: #666; margin-top: 10px;">
                        青蓝色渐变，清新而专业
                    </p>
                </div>
            </div>

            <!-- E按钮编辑状态 -->
            <div class="color-card">
                <div class="button-demo">
                    <div class="demo-button edit-button editing">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="white">
                            <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
                        </svg>
                    </div>
                </div>
                <div class="color-info">
                    <div class="color-name">E按钮 (编辑中)</div>
                    <div class="color-code">#EA580C → #C2410C</div>
                    <p style="font-size: 14px; color: #666; margin-top: 10px;">
                        橙色渐变，表示活跃的编辑状态
                    </p>
                </div>
            </div>
        </div>

        <div class="feature-section">
            <h2>📊 配色对比</h2>
            <table class="comparison-table">
                <thead>
                    <tr>
                        <th>元素</th>
                        <th>旧配色</th>
                        <th>新配色 (Midjourney风格)</th>
                        <th>设计理念</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><strong>M按钮</strong></td>
                        <td class="old-color">#7C3AED → #3B82F6</td>
                        <td class="new-color">#2D1B69 → #1E3A8A</td>
                        <td>深蓝紫色，更加沉稳专业</td>
                    </tr>
                    <tr>
                        <td><strong>E按钮</strong></td>
                        <td class="old-color">#10B981 → #059669</td>
                        <td class="new-color">#0891B2 → #0E7490</td>
                        <td>青蓝色，与M按钮形成和谐对比</td>
                    </tr>
                    <tr>
                        <td><strong>编辑状态</strong></td>
                        <td class="old-color">#F59E0B → #D97706</td>
                        <td class="new-color">#EA580C → #C2410C</td>
                        <td>更深的橙色，增强视觉冲击力</td>
                    </tr>
                    <tr>
                        <td><strong>编辑边框</strong></td>
                        <td class="old-color">#10B981</td>
                        <td class="new-color">#0891B2</td>
                        <td>与E按钮颜色保持一致</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="feature-section">
            <h2>🔔 通知颜色</h2>
            <div style="text-align: center; margin: 20px 0;">
                <div class="notification-demo notif-save">💾 已保存修改</div>
                <div class="notification-demo notif-edit">✏️ 编辑模式</div>
                <div class="notification-demo notif-restore">🔄 已恢复原始内容</div>
            </div>
        </div>

        <div class="feature-section">
            <h2>📝 编辑模式演示</h2>
            
            <h3>普通状态：</h3>
            <div class="code-block">
Northern lights dancing over a snow-covered pine forest, green and purple aurora reflecting on frozen lake, winter night scene, crystalline snow details, magical atmosphere, long exposure photography style
            </div>
            
            <h3>编辑模式（青蓝色边框）：</h3>
            <div class="code-block editable">
Northern lights dancing over a snow-covered pine forest, green and purple aurora reflecting on frozen lake, winter night scene, crystalline snow details, magical atmosphere, long exposure photography style --ar 16:9 --v 6
            </div>
        </div>

        <div class="feature-section">
            <h2>🎯 设计原则</h2>
            <ul style="font-size: 16px; line-height: 1.8;">
                <li><strong>品牌一致性：</strong>采用Midjourney的经典深蓝紫色调</li>
                <li><strong>功能区分：</strong>M按钮（深蓝紫）和E按钮（青蓝）形成清晰对比</li>
                <li><strong>状态指示：</strong>编辑状态使用橙色，提供明确的视觉反馈</li>
                <li><strong>视觉层次：</strong>渐变效果和阴影增强立体感</li>
                <li><strong>专业感：</strong>整体配色更加成熟稳重，符合专业工具的定位</li>
            </ul>
        </div>

        <div class="feature-section">
            <h2>✨ 视觉效果提升</h2>
            <ul style="font-size: 16px; line-height: 1.8;">
                <li>🎨 <strong>更好的品牌识别度：</strong>与Midjourney官方色调保持一致</li>
                <li>👁️ <strong>更清晰的功能区分：</strong>不同颜色代表不同功能</li>
                <li>✨ <strong>更精致的视觉效果：</strong>渐变、阴影、边框的完美结合</li>
                <li>🔄 <strong>更直观的状态反馈：</strong>颜色变化清楚表示当前状态</li>
                <li>🎯 <strong>更专业的整体感：</strong>配色方案更加成熟稳重</li>
            </ul>
        </div>
    </div>
</body>
</html>
