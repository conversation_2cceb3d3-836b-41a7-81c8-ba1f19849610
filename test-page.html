<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Claude Prompt Test Page</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .title {
            color: #333;
            border-bottom: 2px solid #4CAF50;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .prompt-section {
            margin: 20px 0;
            padding: 15px;
            background-color: #f9f9f9;
            border-left: 4px solid #2196F3;
            border-radius: 5px;
        }
        .prompt-title {
            font-weight: bold;
            color: #444;
            margin-bottom: 10px;
        }
        .prompt-content {
            background-color: #2d2d2d;
            color: #f0f0f0;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            margin: 10px 0;
        }
        .instructions {
            background-color: #e3f2fd;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .note {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">Claude to Midjourney Assistant - 测试页面</h1>
        
        <div class="instructions">
            <h3>测试说明：</h3>
            <ol>
                <li>安装Chrome插件后，在此页面测试prompt提取功能</li>
                <li>点击插件图标，然后点击"提取Prompts"</li>
                <li>插件应该能够识别下面的prompts</li>
                <li>然后可以在Discord的Midjourney频道中测试发送功能</li>
            </ol>
        </div>

        <div class="prompt-section">
            <div class="prompt-title">提示方案一：数据迷宫物理化</div>
            <div class="prompt-content">(CINEMATIC STILL), (FILM BY Lana & Lilly Wachowski), (person trapped in towering digital maze walls made of flowing binary code, deep ocean blue-grey atmosphere with electric green data streams), (Matrix-inspired cyberpunk aesthetic), (wide establishing shot)</div>
        </div>

        <div class="prompt-section">
            <div class="prompt-title">提示方案二：意识闪烁</div>
            <div class="prompt-content">(CINEMATIC STILL), (FILM BY Lana & Lilly Wachowski), (transparent human brain suspended in dark void, neural pathways replaced by glowing data circuits, porcelain white highlights with electric green pulses), (Neo-cyberpunk surrealism), (macro close-up shot)</div>
        </div>

        <div class="prompt-section">
            <div class="prompt-title">提示方案三：城市数据溶解</div>
            <div class="prompt-content">(CINEMATIC STILL), (FILM BY Lana & Lilly Wachowski), (modern cityscape dissolving into cascading digital particles, deep sea blue-grey buildings fragmenting into electric green data streams), (dystopian digital transformation), (aerial drone shot)</div>
        </div>

        <div class="prompt-section">
            <div class="prompt-title">提示方案四：时间数据漩涡</div>
            <div class="prompt-content">(CINEMATIC STILL), (FILM BY Lana & Lilly Wachowski), (massive spiral vortex of multicolored data fragments, human silhouettes being pulled into digital maelstrom, deep ocean blue core with electric green energy bursts), (abstract digital nightmare), (dramatic low angle shot)</div>
        </div>

        <div class="prompt-section">
            <div class="prompt-title">提示方案五：数据考古废墟</div>
            <div class="prompt-content">(CINEMATIC STILL), (FILM BY Lana & Lilly Wachowski), (lone figure kneeling in desert of digital debris, servers and screens half buried in data sand dunes, deep blue-grey twilight with porcelain white highlights), (post-apocalyptic digital archaeology), (cinematic medium shot)</div>
        </div>

        <div class="note">
            <strong>注意：</strong>这些prompts包含了插件识别的关键词，如"CINEMATIC STILL"、"FILM BY Lana & Lilly Wachowski"等，应该能被正确提取。
        </div>

        <div class="instructions">
            <h3>如何使用：</h3>
            <ol>
                <li>确保已安装Chrome插件</li>
                <li>在此页面点击插件图标</li>
                <li>点击"提取Prompts"按钮</li>
                <li>应该看到5个prompts被提取</li>
                <li>打开Discord的Midjourney频道</li>
                <li>回到插件，选择要发送的prompts</li>
                <li>点击发送按钮测试自动发送功能</li>
            </ol>
        </div>
    </div>
</body>
</html>
