<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>调试绿色勾号中断问题</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .title {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 3px solid #dc3545;
            padding-bottom: 15px;
        }
        .debug-section {
            margin: 25px 0;
            padding: 20px;
            border: 2px solid #dc3545;
            border-radius: 8px;
            background-color: #f8d7da;
        }
        .suspects-section {
            margin: 25px 0;
            padding: 20px;
            border: 2px solid #ffc107;
            border-radius: 8px;
            background-color: #fff3cd;
        }
        .test-section {
            margin: 25px 0;
            padding: 20px;
            border: 2px solid #17a2b8;
            border-radius: 8px;
            background-color: #d1ecf1;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .status-error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status-warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .code-fix {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            overflow-x: auto;
        }
        .test-block {
            margin: 15px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #f8f9fa;
            font-family: monospace;
            position: relative;
        }
        .step {
            background: #e9ecef;
            border-left: 4px solid #dc3545;
            padding: 15px;
            margin: 10px 0;
        }
        .suspect-item {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            padding: 10px;
            margin: 8px 0;
        }
        .suspect-title {
            font-weight: bold;
            color: #856404;
            margin-bottom: 5px;
        }
        .suspect-timing {
            font-size: 12px;
            color: #6c757d;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">🔍 调试绿色勾号中断问题</h1>
        
        <div class="debug-section">
            <h2>🚨 调试模式已启用</h2>
            <div class="status status-error">
                <strong>现在插件会记录所有按钮移除操作：</strong><br>
                每次调用 removeFloatingButtons() 时都会显示调用堆栈，<br>
                帮助我们找出到底是哪个函数在打断绿色勾号！
            </div>
            
            <h3>🔍 新增的调试信息：</h3>
            <div class="code-fix">
// 在 addFloatingButtons 中添加
console.trace('📍 addFloatingButtons 调用堆栈:');

// 在 removeFloatingButtons 中添加  
console.trace('📍 removeFloatingButtons 调用堆栈:');
console.log(`🗑️ 找到 ${existingButtons.length} 个现有按钮，准备移除`);
existingButtons.forEach((button, index) => {
    const buttonState = button.style.background.includes('#4CAF50') ? '绿色勾号' : 
                       button.style.background.includes('#FF9800') ? '橙色加载' : '普通状态';
    console.log(`🗑️ 移除按钮 ${index + 1}: ${buttonState}`);
});
            </div>
        </div>

        <div class="suspects-section">
            <h2>🕵️ 可疑的函数列表</h2>
            <p>以下函数都会调用 addFloatingButtons()，可能打断绿色勾号：</p>
            
            <div class="suspect-item">
                <div class="suspect-title">1. 定期检查 (startPeriodicCheck)</div>
                <div class="suspect-timing">⏰ 每3秒触发一次</div>
                <div>可能性：⭐⭐⭐⭐⭐ (最高)</div>
            </div>

            <div class="suspect-item">
                <div class="suspect-title">2. 页面内容变化检测 (observePageChanges)</div>
                <div class="suspect-timing">⏰ 1.5秒延迟触发</div>
                <div>可能性：⭐⭐⭐⭐</div>
            </div>

            <div class="suspect-item">
                <div class="suspect-title">3. Claude重新生成检测 (observeClaudeRegeneration)</div>
                <div class="suspect-timing">⏰ 3秒和6秒延迟触发</div>
                <div>可能性：⭐⭐⭐</div>
            </div>

            <div class="suspect-item">
                <div class="suspect-title">4. 键盘快捷键 (Ctrl+R)</div>
                <div class="suspect-timing">⏰ 3秒延迟触发</div>
                <div>可能性：⭐⭐</div>
            </div>

            <div class="suspect-item">
                <div class="suspect-title">5. 新代码块检测</div>
                <div class="suspect-timing">⏰ 立即触发</div>
                <div>可能性：⭐⭐⭐</div>
            </div>

            <div class="suspect-item">
                <div class="suspect-title">6. URL变化检测 (handleUrlChange)</div>
                <div class="suspect-timing">⏰ 每2秒检查</div>
                <div>可能性：⭐</div>
            </div>
        </div>

        <div class="test-section">
            <h2>🧪 调试测试代码块</h2>
            <p>使用这些代码块进行调试测试：</p>
            
            <h4>调试代码块1：</h4>
            <pre class="test-block">Peaceful zen garden with raked sand patterns, carefully placed stones, bamboo fountain, morning mist, minimalist Japanese design --ar 16:9</pre>

            <h4>调试代码块2：</h4>
            <pre class="test-block">Majestic eagle soaring above mountain peaks, wings spread wide, golden sunset light, dramatic sky with clouds, wildlife photography --ar 9:16</pre>

            <h4>调试代码块3：</h4>
            <pre class="test-block">Cozy cabin in snowy forest, warm light glowing from windows, smoke rising from chimney, winter wonderland scene --ar 4:3</pre>
        </div>

        <div class="test-section">
            <h2>🔍 调试步骤</h2>
            
            <div class="step">
                <h4>步骤1：重新加载插件</h4>
                <p>在Chrome扩展页面刷新插件，启用调试模式</p>
            </div>

            <div class="step">
                <h4>步骤2：打开控制台</h4>
                <ol>
                    <li>按F12打开Chrome开发者工具</li>
                    <li>切换到Console标签页</li>
                    <li>清空控制台日志</li>
                </ol>
            </div>

            <div class="step">
                <h4>步骤3：执行测试</h4>
                <ol>
                    <li>点击任意M按钮</li>
                    <li>立即观察控制台输出</li>
                    <li>等待3-6秒，看是否有按钮被移除</li>
                    <li>记录具体的调用堆栈信息</li>
                </ol>
            </div>

            <div class="step">
                <h4>步骤4：分析调用堆栈</h4>
                <p>在控制台中查找以下关键信息：</p>
                <div class="code-fix">
关键日志模式：
✅ "✅ 检测到成功响应，显示绿色勾号"
⚠️ "🗑️ 移除所有现有的浮标按钮"
⚠️ "🗑️ 移除按钮 X: 绿色勾号"
📍 "📍 removeFloatingButtons 调用堆栈:"

重点关注：
- 绿色勾号显示后多久被移除？
- 调用堆栈显示是哪个函数触发的？
- 是否有特定的时间模式？
                </div>
            </div>
        </div>

        <div class="debug-section">
            <h2>📋 预期的调试输出</h2>
            <div class="code-fix">
🔍 正常流程（不应该被打断）：
- "发送prompt到标签页: XXX ..."
- "✅ 检测到成功响应，显示绿色勾号"
- "已后台发送到Midjourney" (绿色通知)
- (3秒后) 按钮自然恢复紫色

❌ 异常流程（被打断）：
- "发送prompt到标签页: XXX ..."
- "✅ 检测到成功响应，显示绿色勾号"
- (1-3秒内) "🗑️ 移除所有现有的浮标按钮"
- "🗑️ 移除按钮 1: 绿色勾号"
- "📍 removeFloatingButtons 调用堆栈:" 
- (堆栈会显示具体是哪个函数调用的)
            </div>
        </div>

        <div class="test-section">
            <h2>🎯 找到罪魁祸首后</h2>
            <p>一旦确定是哪个函数在打断绿色勾号，我们就可以：</p>
            <ol>
                <li><strong>禁用该函数</strong> - 临时注释掉问题函数</li>
                <li><strong>修改触发条件</strong> - 避免在按钮显示状态时触发</li>
                <li><strong>改用智能添加</strong> - 使用 addMissingButtons 替代</li>
                <li><strong>添加状态检查</strong> - 检查按钮状态再决定是否移除</li>
            </ol>
        </div>

        <div class="debug-section">
            <h2>🚀 下一步行动</h2>
            <div class="status status-warning">
                <strong>请执行测试并报告结果：</strong><br>
                1. 点击M按钮后，绿色勾号是否被打断？<br>
                2. 控制台显示的调用堆栈指向哪个函数？<br>
                3. 打断发生在点击后的第几秒？<br>
                4. 是否有特定的时间模式？
            </div>
        </div>
    </div>

    <script>
        // 页面加载时显示状态
        window.onload = function() {
            setTimeout(() => {
                console.log('🔍 调试模式已启用');
                console.log('现在会记录所有按钮移除操作的调用堆栈');
                console.log('请点击M按钮并观察控制台输出');
            }, 1000);
        };
    </script>
</body>
</html>
