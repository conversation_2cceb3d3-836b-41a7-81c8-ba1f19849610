<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Midjourney风格切换功能测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            min-height: 100vh;
        }
        .container {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.15);
            backdrop-filter: blur(10px);
        }
        h1 {
            color: #4a5568;
            text-align: center;
            margin-bottom: 40px;
            font-size: 3em;
            font-weight: 700;
            background: linear-gradient(135deg, #4285f4, #34a853);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        .feature-section {
            background: linear-gradient(135deg, #f0fff4, #e6fffa);
            border: 2px solid #48bb78;
            padding: 30px;
            margin: 30px 0;
            border-radius: 16px;
            position: relative;
            overflow: hidden;
        }
        .feature-section::before {
            content: '';
            position: absolute;
            top: -50%;
            right: -50%;
            width: 100px;
            height: 100px;
            background: rgba(72, 187, 120, 0.1);
            border-radius: 50%;
            animation: pulse 3s infinite;
        }
        .notification-demo {
            background: linear-gradient(135deg, #4285f4, #34a853);
            color: white;
            padding: 0;
            border-radius: 12px;
            margin: 20px 0;
            overflow: hidden;
            box-shadow: 0 8px 32px rgba(0,0,0,0.24);
            max-width: 320px;
        }
        .notification-content {
            padding: 16px 20px;
            position: relative;
            overflow: hidden;
        }
        .notification-bg {
            position: absolute;
            top: -50%;
            right: -50%;
            width: 100px;
            height: 100px;
            background: rgba(255,255,255,0.1);
            border-radius: 50%;
            animation: pulse 2s infinite;
        }
        .notification-body {
            display: flex;
            align-items: center;
            gap: 12px;
            position: relative;
            z-index: 1;
        }
        .notification-icon {
            width: 32px;
            height: 32px;
            background: rgba(255,255,255,0.2);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            backdrop-filter: blur(10px);
        }
        .notification-text {
            flex: 1;
        }
        .notification-title {
            font-weight: 600;
            font-size: 15px;
            margin-bottom: 2px;
            letter-spacing: -0.01em;
        }
        .notification-message {
            font-size: 13px;
            opacity: 0.9;
            font-weight: 400;
        }
        .notification-progress {
            background: rgba(255,255,255,0.1);
            height: 3px;
            width: 100%;
            animation: progress 3s linear;
        }
        .shortcut-key {
            background: linear-gradient(135deg, #2d3748, #4a5568);
            color: white;
            padding: 8px 16px;
            border-radius: 8px;
            font-family: 'SF Mono', Monaco, monospace;
            font-weight: 600;
            font-size: 16px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
            display: inline-block;
            margin: 0 4px;
        }
        .toggle-demo {
            text-align: center;
            padding: 40px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border-radius: 20px;
            margin: 30px 0;
        }
        .toggle-steps {
            display: flex;
            justify-content: space-around;
            margin: 30px 0;
            flex-wrap: wrap;
            gap: 20px;
        }
        .step {
            background: rgba(255,255,255,0.1);
            padding: 20px;
            border-radius: 12px;
            backdrop-filter: blur(10px);
            flex: 1;
            min-width: 200px;
        }
        .step-number {
            width: 40px;
            height: 40px;
            background: rgba(255,255,255,0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 15px;
            font-weight: bold;
            font-size: 18px;
        }
        @keyframes pulse {
            0%, 100% { transform: scale(1); opacity: 0.1; }
            50% { transform: scale(1.2); opacity: 0.2; }
        }
        @keyframes progress {
            from { width: 100%; }
            to { width: 0%; }
        }
        .feature-list {
            list-style: none;
            padding: 0;
        }
        .feature-list li {
            padding: 12px 0;
            border-bottom: 1px solid rgba(72, 187, 120, 0.2);
            position: relative;
            padding-left: 40px;
        }
        .feature-list li:last-child {
            border-bottom: none;
        }
        .feature-list li::before {
            content: "✨";
            position: absolute;
            left: 0;
            font-size: 20px;
            animation: sparkle 2s infinite;
        }
        @keyframes sparkle {
            0%, 100% { transform: scale(1) rotate(0deg); }
            50% { transform: scale(1.2) rotate(180deg); }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>✨ Midjourney风格切换功能</h1>
        
        <div class="feature-section">
            <h2>🎉 新功能完成</h2>
            <p><strong>1. 美观的Midjourney风格通知</strong></p>
            <p><strong>2. 智能切换功能：按一次显示，再按一次隐藏</strong></p>
            
            <div style="display: flex; gap: 20px; margin: 20px 0; flex-wrap: wrap;">
                <!-- 检测通知演示 -->
                <div class="notification-demo">
                    <div class="notification-content">
                        <div class="notification-bg"></div>
                        <div class="notification-body">
                            <div class="notification-icon">🔍</div>
                            <div class="notification-text">
                                <div class="notification-title">手动检测</div>
                                <div class="notification-message">正在检测页面中的prompts...</div>
                            </div>
                        </div>
                    </div>
                    <div class="notification-progress"></div>
                </div>
                
                <!-- 清除通知演示 -->
                <div class="notification-demo" style="background: linear-gradient(135deg, #f44336, #e91e63);">
                    <div class="notification-content">
                        <div class="notification-bg"></div>
                        <div class="notification-body">
                            <div class="notification-icon">🧹</div>
                            <div class="notification-text">
                                <div class="notification-title">清除M按钮</div>
                                <div class="notification-message">已清除所有M按钮</div>
                            </div>
                        </div>
                    </div>
                    <div class="notification-progress"></div>
                </div>
            </div>
        </div>

        <div class="toggle-demo">
            <h2 style="margin-top: 0;">🎮 智能切换功能</h2>
            <div style="font-size: 24px; margin: 20px 0;">
                <span class="shortcut-key">Ctrl + Shift + Z</span>
            </div>
            <p style="font-size: 18px; opacity: 0.9;">一个快捷键，两种状态</p>
            
            <div class="toggle-steps">
                <div class="step">
                    <div class="step-number">1</div>
                    <h3>第一次按下</h3>
                    <p>🔍 检测并显示M按钮</p>
                    <p>显示蓝色检测通知</p>
                </div>
                <div class="step">
                    <div class="step-number">2</div>
                    <h3>第二次按下</h3>
                    <p>🧹 清除所有M按钮</p>
                    <p>显示红色清除通知</p>
                </div>
                <div class="step">
                    <div class="step-number">3</div>
                    <h3>第三次按下</h3>
                    <p>🔍 重新检测显示</p>
                    <p>循环往复...</p>
                </div>
            </div>
        </div>

        <div class="feature-section">
            <h2>✨ 新功能特点</h2>
            <ul class="feature-list">
                <li><strong>Midjourney风格设计：</strong>渐变背景、圆角设计、动画效果</li>
                <li><strong>智能状态管理：</strong>自动跟踪M按钮的显示/隐藏状态</li>
                <li><strong>视觉反馈：</strong>不同颜色通知区分检测和清除操作</li>
                <li><strong>流畅动画：</strong>滑入滑出效果，脉冲动画，进度条</li>
                <li><strong>响应式设计：</strong>适配不同屏幕尺寸</li>
                <li><strong>一键切换：</strong>单个快捷键控制所有操作</li>
            </ul>
        </div>

        <div class="feature-section">
            <h2>🔧 技术实现</h2>
            <h3>📍 状态管理：</h3>
            <pre style="background: #1a202c; color: #e2e8f0; padding: 20px; border-radius: 12px; overflow-x: auto;"><code>class ClaudePromptExtractor {
    constructor() {
        this.buttonsVisible = false; // 跟踪按钮状态
    }
    
    manualDetect() {
        if (this.buttonsVisible) {
            // 清除按钮
            this.removeFloatingButtons();
            this.buttonsVisible = false;
            this.showManualDetectNotification(false);
        } else {
            // 添加按钮
            this.addFloatingButtons();
            this.buttonsVisible = true;
            this.showManualDetectNotification(true);
        }
    }
}</code></pre>

            <h3>🎨 Midjourney风格通知：</h3>
            <ul class="feature-list">
                <li>渐变背景色（蓝绿色检测，红粉色清除）</li>
                <li>毛玻璃效果（backdrop-filter: blur）</li>
                <li>脉冲动画背景装饰</li>
                <li>进度条显示通知时长</li>
                <li>圆角图标容器</li>
                <li>平滑的滑入滑出动画</li>
            </ul>
        </div>

        <div class="feature-section">
            <h2>📝 使用说明</h2>
            <ol style="font-size: 16px; line-height: 1.8;">
                <li><strong>打开Claude页面：</strong>插件自动加载，控制台显示初始化信息</li>
                <li><strong>第一次按 <span class="shortcut-key">Ctrl+Shift+Z</span>：</strong>检测并显示M按钮，显示蓝色通知</li>
                <li><strong>第二次按 <span class="shortcut-key">Ctrl+Shift+Z</span>：</strong>清除所有M按钮，显示红色通知</li>
                <li><strong>继续按：</strong>在显示/隐藏之间切换</li>
            </ol>
            
            <p style="background: rgba(66, 133, 244, 0.1); padding: 15px; border-radius: 8px; margin-top: 20px;">
                <strong>💡 提示：</strong>现在你完全控制M按钮的显示时机，动画永远不会被打断，通知样式更加美观！
            </p>
        </div>
    </div>
</body>
</html>
