<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Set使用修复测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .title {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 3px solid #28a745;
            padding-bottom: 15px;
        }
        .fix-section {
            margin: 25px 0;
            padding: 20px;
            border: 2px solid #28a745;
            border-radius: 8px;
            background-color: #d4edda;
        }
        .error-section {
            margin: 25px 0;
            padding: 20px;
            border: 2px solid #dc3545;
            border-radius: 8px;
            background-color: #f8d7da;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .status-success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status-error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .code-fix {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            overflow-x: auto;
        }
        .test-block {
            margin: 15px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #f8f9fa;
            font-family: monospace;
            position: relative;
        }
        .btn {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background-color: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">✅ Set使用修复</h1>
        
        <div class="error-section">
            <h2>❌ 发现的第二个问题</h2>
            <div class="status status-error">
                <strong>Set/Map混用错误：</strong><br>
                在 <code>createFloatingButton</code> 方法中仍然使用了Map的 <code>set</code> 方法！
            </div>
            
            <h3>🔍 问题代码：</h3>
            <div class="code-fix">
// 在 createFloatingButton 方法中
this.promptElements.set(promptElement, floatingButton); // ❌ Set没有set方法！

// 这会导致JavaScript错误，中断后续代码块的处理
            </div>
        </div>

        <div class="fix-section">
            <h2>✅ 修复方案</h2>
            
            <h3>🛠️ 正确的Set使用：</h3>
            <div class="code-fix">
// 修复前（错误）
this.promptElements.set(promptElement, floatingButton); // ❌ Map方法

// 修复后（正确）
// 在createFloatingButton中不需要添加到Set
// 因为在调用处已经添加了：this.promptElements.add(element);

// Set的正确用法
this.promptElements.add(element);     // ✅ 添加元素
this.promptElements.has(element);     // ✅ 检查是否存在
this.promptElements.clear();          // ✅ 清空所有
this.promptElements.delete(element);  // ✅ 删除特定元素
            </div>

            <h3>🎯 为什么会出现这个问题：</h3>
            <ul>
                <li>❌ <strong>历史遗留</strong> - 原来使用Map存储元素和按钮的对应关系</li>
                <li>❌ <strong>不完整修改</strong> - 只改了构造函数，没有改所有使用的地方</li>
                <li>❌ <strong>功能混淆</strong> - Set只需要记录已处理的元素，不需要存储按钮</li>
            </ul>
        </div>

        <div class="fix-section">
            <h2>🧪 测试代码块</h2>
            <p>现在所有代码块都应该能被正确识别，不会因为Set/Map错误而中断：</p>
            
            <button class="btn" onclick="testSetUsage()">🧪 测试Set使用</button>
            <button class="btn" onclick="simulatePlugin()">🔄 模拟插件逻辑</button>
            
            <h4>测试代码块1：</h4>
            <pre class="test-block">Majestic mountain landscape at sunrise, golden light illuminating snow-capped peaks, misty valleys below, dramatic clouds, landscape photography --ar 16:9</pre>

            <h4>测试代码块2：</h4>
            <pre class="test-block">Ancient temple ruins overgrown with vines, shafts of sunlight through broken stone, mystical atmosphere, archaeological discovery --ar 4:3</pre>

            <h4>测试代码块3：</h4>
            <pre class="test-block">Futuristic space colony on Mars, dome structures, red planet landscape, Earth visible in the sky, sci-fi concept art --ar 21:9</pre>

            <h4>测试代码块4：</h4>
            <pre class="test-block">Underwater coral reef teeming with colorful fish, crystal clear water, tropical paradise, marine life photography --ar 3:4</pre>

            <h4>测试代码块5：</h4>
            <pre class="test-block">Medieval castle on a cliff overlooking the ocean, dramatic storm clouds, waves crashing below, fantasy landscape --ar 16:9</pre>
        </div>

        <div class="fix-section">
            <h2>🔍 调试输出</h2>
            <div id="debug-output" style="background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 5px; padding: 15px; font-family: monospace; font-size: 12px; max-height: 300px; overflow-y: auto;">
                点击测试按钮查看输出...
            </div>
        </div>

        <div class="fix-section">
            <h2>📋 修复清单</h2>
            <div class="status status-success">
                ✅ <strong>已修复的问题：</strong><br>
                1. 修复了Map/Set数据类型不匹配<br>
                2. 移除了createFloatingButton中的set方法调用<br>
                3. 统一使用Set进行元素缓存<br>
                4. 防止JavaScript运行时错误<br>
                5. 确保所有代码块都能被处理
            </div>
        </div>
    </div>

    <script>
        function testSetUsage() {
            const output = document.getElementById('debug-output');
            output.innerHTML = '';
            
            output.innerHTML += '🧪 测试Set的正确使用...\n\n';
            
            try {
                // 模拟插件的Set使用
                const promptElements = new Set();
                
                // 测试添加元素
                const element1 = document.querySelector('pre:nth-child(1)');
                const element2 = document.querySelector('pre:nth-child(2)');
                
                output.innerHTML += '✅ 创建Set成功\n';
                
                // 测试add方法
                promptElements.add(element1);
                output.innerHTML += '✅ add方法成功\n';
                
                // 测试has方法
                const hasElement = promptElements.has(element1);
                output.innerHTML += `✅ has方法成功: ${hasElement}\n`;
                
                // 测试clear方法
                promptElements.clear();
                output.innerHTML += '✅ clear方法成功\n';
                
                output.innerHTML += '\n🎉 所有Set方法都正常工作！\n';
                
            } catch (error) {
                output.innerHTML += `❌ Set使用出错: ${error.message}\n`;
            }
        }

        function simulatePlugin() {
            const output = document.getElementById('debug-output');
            output.innerHTML = '';
            
            output.innerHTML += '🔄 模拟插件处理逻辑...\n\n';
            
            const promptElements = new Set();
            const allPre = document.querySelectorAll('pre');
            let addedCount = 0;
            
            allPre.forEach((element, index) => {
                const text = element.textContent || element.innerText;
                const isValidCodeBlock = true; // 简化检查
                const hasValidText = text && text.trim().length > 10;
                const notProcessed = !promptElements.has(element);
                
                output.innerHTML += `📋 检查代码块${index + 1}:\n`;
                output.innerHTML += `  - 文本: ${text.substring(0, 30)}...\n`;
                output.innerHTML += `  - 有效代码块: ${isValidCodeBlock}\n`;
                output.innerHTML += `  - 有效文本: ${hasValidText}\n`;
                output.innerHTML += `  - 未处理: ${notProcessed}\n`;
                
                if (isValidCodeBlock && hasValidText && notProcessed) {
                    try {
                        // 模拟添加到Set
                        promptElements.add(element);
                        addedCount++;
                        output.innerHTML += `  ✅ 成功添加第${addedCount}个M按钮\n`;
                    } catch (error) {
                        output.innerHTML += `  ❌ 添加失败: ${error.message}\n`;
                    }
                } else {
                    output.innerHTML += `  ❌ 跳过此元素\n`;
                }
                output.innerHTML += `\n`;
            });
            
            output.innerHTML += `📊 总结: 应该添加 ${addedCount} 个M按钮\n`;
            output.innerHTML += `📊 Set大小: ${promptElements.size}\n`;
        }

        // 页面加载时显示状态
        window.onload = function() {
            setTimeout(() => {
                console.log('✅ Set使用修复测试页面已加载');
                console.log('现在插件应该能正确处理所有代码块了！');
            }, 1000);
        };
    </script>
</body>
</html>
