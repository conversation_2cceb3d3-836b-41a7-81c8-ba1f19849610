<!DOCTYPE html>
<html>
<head>
    <title>简单图标生成器</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        canvas { border: 1px solid #ccc; margin: 10px; display: block; }
        .icon-container { margin: 20px 0; }
    </style>
</head>
<body>
    <h2>Chrome插件图标生成器</h2>
    
    <div class="icon-container">
        <h3>16x16 图标</h3>
        <canvas id="icon16" width="16" height="16"></canvas>
        <button onclick="downloadIcon('icon16', 16)">下载 icon16.png</button>
    </div>
    
    <div class="icon-container">
        <h3>48x48 图标</h3>
        <canvas id="icon48" width="48" height="48"></canvas>
        <button onclick="downloadIcon('icon48', 48)">下载 icon48.png</button>
    </div>
    
    <div class="icon-container">
        <h3>128x128 图标</h3>
        <canvas id="icon128" width="128" height="128"></canvas>
        <button onclick="downloadIcon('icon128', 128)">下载 icon128.png</button>
    </div>
    
    <button onclick="downloadAll()" style="background: #4CAF50; color: white; padding: 10px 20px; border: none; border-radius: 5px; font-size: 16px;">下载所有图标</button>

    <script>
        function drawIcon(canvasId, size) {
            const canvas = document.getElementById(canvasId);
            const ctx = canvas.getContext('2d');
            
            // 清空
            ctx.clearRect(0, 0, size, size);
            
            // 背景圆
            const gradient = ctx.createLinearGradient(0, 0, size, size);
            gradient.addColorStop(0, '#4CAF50');
            gradient.addColorStop(1, '#2196F3');
            
            ctx.fillStyle = gradient;
            ctx.beginPath();
            ctx.arc(size/2, size/2, size/2 - 1, 0, 2 * Math.PI);
            ctx.fill();
            
            // 白色文字
            ctx.fillStyle = '#ffffff';
            ctx.font = `bold ${Math.floor(size/4)}px Arial`;
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            
            if (size >= 48) {
                ctx.fillText('C→M', size/2, size/2 - size/8);
                ctx.font = `${Math.floor(size/8)}px Arial`;
                ctx.fillText('Auto', size/2, size/2 + size/6);
            } else {
                ctx.fillText('CM', size/2, size/2);
            }
        }
        
        function downloadIcon(canvasId, size) {
            const canvas = document.getElementById(canvasId);
            const link = document.createElement('a');
            link.download = `icon${size}.png`;
            link.href = canvas.toDataURL('image/png');
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
        }
        
        function downloadAll() {
            downloadIcon('icon16', 16);
            setTimeout(() => downloadIcon('icon48', 48), 100);
            setTimeout(() => downloadIcon('icon128', 128), 200);
        }
        
        // 初始化
        window.onload = function() {
            drawIcon('icon16', 16);
            drawIcon('icon48', 48);
            drawIcon('icon128', 128);
        };
    </script>
</body>
</html>
