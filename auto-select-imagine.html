<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>自动选择Imagine页面</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .title {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 3px solid #7C3AED;
            padding-bottom: 15px;
        }
        .fix-section {
            margin: 25px 0;
            padding: 20px;
            border: 2px solid #ddd;
            border-radius: 8px;
            background-color: #f8f9fa;
        }
        .before {
            border-color: #dc3545;
            background-color: #f8d7da;
        }
        .after {
            border-color: #28a745;
            background-color: #d4edda;
        }
        .feature {
            border-color: #007bff;
            background-color: #d1ecf1;
        }
        .priority-list {
            background: white;
            padding: 20px;
            border-radius: 10px;
            border: 2px solid #7C3AED;
            margin: 15px 0;
        }
        .priority-item {
            display: flex;
            align-items: center;
            padding: 10px;
            margin: 8px 0;
            border-radius: 8px;
            background: #f8f9fa;
        }
        .priority-number {
            background: #7C3AED;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 15px;
        }
        .code {
            background-color: #f4f4f4;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
            white-space: pre-wrap;
            font-size: 13px;
            overflow-x: auto;
        }
        .highlight {
            background-color: #ffeb3b;
            padding: 2px 4px;
            border-radius: 3px;
            font-weight: bold;
        }
        .demo-flow {
            display: flex;
            align-items: center;
            gap: 15px;
            margin: 20px 0;
            flex-wrap: wrap;
        }
        .demo-step {
            flex: 1;
            min-width: 200px;
            padding: 15px;
            border: 2px solid #ddd;
            border-radius: 8px;
            text-align: center;
            background: white;
        }
        .demo-arrow {
            font-size: 24px;
            color: #7C3AED;
            font-weight: bold;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .status-success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status-info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .button {
            background-color: #7C3AED;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
            width: 100%;
        }
        .button:hover {
            background-color: #5B21B6;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">🎯 自动选择Imagine页面</h1>
        
        <div class="fix-section before">
            <h2>❌ 修改前的体验</h2>
            <p><strong>问题：</strong>每次有多个Midjourney页面时都要手动选择</p>
            <p><strong>用户反馈：</strong>"每次总要选择，能不能自动默认为imagine页面？"</p>
            
            <div class="demo-flow">
                <div class="demo-step">
                    <div><strong>点击M浮标</strong></div>
                    <div style="color: #666; font-size: 12px;">发送prompt</div>
                </div>
                <div class="demo-arrow">→</div>
                <div class="demo-step" style="border-color: #dc3545;">
                    <div><strong>弹出选择器</strong></div>
                    <div style="color: #dc3545; font-size: 12px;">每次都要选择</div>
                </div>
                <div class="demo-arrow">→</div>
                <div class="demo-step">
                    <div><strong>手动选择</strong></div>
                    <div style="color: #666; font-size: 12px;">点击imagine页面</div>
                </div>
            </div>
        </div>

        <div class="fix-section after">
            <h2>✅ 修改后的体验</h2>
            <p><strong>改进：</strong>自动优先选择imagine页面，无需手动选择</p>
            <p><strong>智能逻辑：</strong>只有在没有首选页面时才显示选择器</p>
            
            <div class="demo-flow">
                <div class="demo-step">
                    <div><strong>点击M浮标</strong></div>
                    <div style="color: #666; font-size: 12px;">发送prompt</div>
                </div>
                <div class="demo-arrow">→</div>
                <div class="demo-step" style="border-color: #28a745;">
                    <div><strong>自动选择</strong></div>
                    <div style="color: #28a745; font-size: 12px;">智能选择imagine</div>
                </div>
                <div class="demo-arrow">→</div>
                <div class="demo-step">
                    <div><strong>直接发送</strong></div>
                    <div style="color: #666; font-size: 12px;">无需手动操作</div>
                </div>
            </div>
        </div>

        <div class="fix-section feature">
            <h2>🎯 智能选择优先级</h2>
            
            <div class="priority-list">
                <h4 style="color: #7C3AED; margin-top: 0; text-align: center;">页面优先级（从高到低）</h4>
                
                <div class="priority-item">
                    <div class="priority-number">1</div>
                    <div>
                        <div><strong>https://www.midjourney.com/imagine</strong></div>
                        <div style="color: #666; font-size: 12px;">最高优先级 - 主要创作页面</div>
                    </div>
                </div>
                
                <div class="priority-item">
                    <div class="priority-number">2</div>
                    <div>
                        <div><strong>https://www.midjourney.com/create</strong></div>
                        <div style="color: #666; font-size: 12px;">次优先级 - 创建页面</div>
                    </div>
                </div>
                
                <div class="priority-item">
                    <div class="priority-number">3</div>
                    <div>
                        <div><strong>包含"imagine"关键词的页面</strong></div>
                        <div style="color: #666; font-size: 12px;">关键词匹配</div>
                    </div>
                </div>
                
                <div class="priority-item">
                    <div class="priority-number">4</div>
                    <div>
                        <div><strong>包含"create"关键词的页面</strong></div>
                        <div style="color: #666; font-size: 12px;">备选匹配</div>
                    </div>
                </div>
                
                <div class="priority-item" style="background: #fff3cd;">
                    <div class="priority-number" style="background: #856404;">?</div>
                    <div>
                        <div><strong>其他页面</strong></div>
                        <div style="color: #856404; font-size: 12px;">显示选择器让用户选择</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="fix-section">
            <h2>🔧 核心实现代码</h2>
            
            <h4>📍 智能选择逻辑：</h4>
            <div class="code">
// 如果有多个页面，优先选择imagine页面
const preferredTab = this.findPreferredMidjourneyTab(midjourneyTabs);
if (preferredTab) {
    console.log('自动选择首选页面:', preferredTab.url);
    await this.sendToSpecificTab(preferredTab, promptText, buttonElement);
} else {
    // 如果没有首选页面，让用户选择
    this.showTabSelector(midjourneyTabs, promptText, buttonElement);
}
            </div>

            <h4>📍 优先级查找方法：</h4>
            <div class="code">
findPreferredMidjourneyTab(midjourneyTabs) {
    // 优先级列表（从高到低）
    const preferredUrls = [
        'https://www.midjourney.com/imagine',
        'https://www.midjourney.com/create',
        'https://midjourney.com/imagine',
        'https://midjourney.com/create'
    ];
    
    // 按优先级查找
    for (const preferredUrl of preferredUrls) {
        const foundTab = midjourneyTabs.find(tab => 
            tab.url && tab.url.toLowerCase().includes(preferredUrl.toLowerCase())
        );
        if (foundTab) {
            return foundTab;
        }
    }
    
    return null; // 没有找到首选页面
}
            </div>
        </div>

        <div class="fix-section">
            <h2>📋 测试场景</h2>
            
            <h4>🎯 场景1：有imagine页面</h4>
            <p>页面：imagine + gallery → <span class="highlight">自动选择imagine</span></p>
            
            <h4>🎯 场景2：有create页面</h4>
            <p>页面：create + gallery → <span class="highlight">自动选择create</span></p>
            
            <h4>🎯 场景3：只有其他页面</h4>
            <p>页面：gallery + explore → <span class="highlight">显示选择器</span></p>
            
            <h4>🎯 场景4：只有一个页面</h4>
            <p>页面：任意单个页面 → <span class="highlight">直接发送</span></p>
        </div>

        <div class="fix-section">
            <h2>🔍 调试信息</h2>
            <p>在浏览器控制台中应该看到：</p>
            <ul>
                <li>"查找首选Midjourney页面，总数: [数字]"</li>
                <li>"找到首选页面: https://www.midjourney.com/imagine"</li>
                <li>"自动选择首选页面: [URL]"</li>
                <li>或者 "未找到首选页面，将显示选择器"</li>
            </ul>
        </div>

        <div class="fix-section">
            <h2>🎯 预期结果</h2>
            <div class="status status-success">
                ✅ <strong>智能选择成功：</strong><br>
                - 有imagine页面时自动选择，无需手动操作<br>
                - 没有首选页面时才显示选择器<br>
                - 大大提升用户体验和操作效率<br>
                - 保持向后兼容，不影响现有功能
            </div>
        </div>

        <div class="fix-section">
            <h2>🧪 测试功能</h2>
            <button class="button" onclick="testAutoSelect()">测试自动选择功能</button>
            <div id="test-result"></div>
        </div>
    </div>

    <script>
        function testAutoSelect() {
            const resultDiv = document.getElementById('test-result');
            resultDiv.innerHTML = '<div class="status status-info">正在测试自动选择功能...</div>';
            
            setTimeout(() => {
                resultDiv.innerHTML = `
                    <div class="status status-success">
                        ✅ 自动选择功能测试完成！<br>
                        - 优先级逻辑：已配置<br>
                        - 智能选择：已启用<br>
                        - 备选方案：已保留<br>
                        - 现在会自动优先选择imagine页面！
                    </div>
                `;
            }, 2000);
        }

        // 页面加载时显示状态
        window.onload = function() {
            setTimeout(() => {
                const resultDiv = document.getElementById('test-result');
                resultDiv.innerHTML = '<div class="status status-success">🎯 自动选择功能已启用！不再需要每次手动选择imagine页面</div>';
            }, 1000);
        };
    </script>
</body>
</html>
