<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON>页面调试工具</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .title {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 3px solid #4CAF50;
            padding-bottom: 15px;
        }
        .debug-section {
            margin: 25px 0;
            padding: 20px;
            border: 2px solid #ddd;
            border-radius: 8px;
            background-color: #f8f9fa;
        }
        .button {
            background-color: #4CAF50;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        .button:hover {
            background-color: #45a049;
        }
        .button-orange {
            background-color: #FF9800;
        }
        .button-orange:hover {
            background-color: #e68900;
        }
        .result {
            margin: 15px 0;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
            font-size: 12px;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .element-info {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin: 5px 0;
            border-radius: 3px;
            cursor: pointer;
        }
        .element-info:hover {
            background-color: #e9ecef;
        }
        .highlight-element {
            outline: 3px solid #ff0000 !important;
            background-color: rgba(255, 0, 0, 0.1) !important;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">🔍 Claude页面调试工具</h1>
        
        <div class="debug-section">
            <h2>📋 使用说明</h2>
            <p><strong>在Claude页面中使用此工具：</strong></p>
            <ol>
                <li>在Claude页面打开开发者工具 (F12)</li>
                <li>在Console中粘贴下面的调试代码</li>
                <li>运行代码来分析页面结构</li>
                <li>找到包含prompts的正确元素</li>
            </ol>
        </div>

        <div class="debug-section">
            <h2>🔧 调试代码</h2>
            <p>请在Claude页面的Console中运行以下代码：</p>
            <button class="button" onclick="copyDebugCode()">复制调试代码</button>
            
            <div class="result info" id="debug-code">
// Claude页面调试代码
console.log('=== Claude页面结构分析 ===');

// 1. 查找所有包含CINEMATIC STILL的元素
function findPromptElements() {
    const allElements = document.querySelectorAll('*');
    const promptElements = [];
    
    allElements.forEach((element, index) => {
        const text = element.textContent || element.innerText;
        if (text && text.includes('(CINEMATIC STILL)')) {
            const rect = element.getBoundingClientRect();
            promptElements.push({
                index: index,
                element: element,
                tagName: element.tagName,
                className: element.className || 'no-class',
                id: element.id || 'no-id',
                textLength: text.length,
                textPreview: text.substring(0, 150) + '...',
                position: {
                    top: rect.top,
                    left: rect.left,
                    width: rect.width,
                    height: rect.height
                },
                styles: {
                    backgroundColor: window.getComputedStyle(element).backgroundColor,
                    color: window.getComputedStyle(element).color,
                    fontSize: window.getComputedStyle(element).fontSize,
                    padding: window.getComputedStyle(element).padding
                }
            });
        }
    });
    
    console.log('找到包含CINEMATIC STILL的元素数量:', promptElements.length);
    promptElements.forEach((info, index) => {
        console.log(`元素 ${index + 1}:`, info);
    });
    
    return promptElements;
}

// 2. 高亮显示找到的元素
function highlightPromptElements() {
    // 先清除之前的高亮
    document.querySelectorAll('.debug-highlight').forEach(el => {
        el.classList.remove('debug-highlight');
        el.style.outline = '';
        el.style.backgroundColor = '';
    });
    
    const promptElements = findPromptElements();
    promptElements.forEach((info, index) => {
        info.element.classList.add('debug-highlight');
        info.element.style.outline = `3px solid ${index % 2 === 0 ? '#ff0000' : '#00ff00'}`;
        info.element.style.backgroundColor = 'rgba(255, 255, 0, 0.2)';
        
        // 添加标签
        const label = document.createElement('div');
        label.textContent = `Prompt ${index + 1}`;
        label.style.cssText = `
            position: absolute;
            top: -25px;
            left: 0;
            background: ${index % 2 === 0 ? '#ff0000' : '#00ff00'};
            color: white;
            padding: 2px 8px;
            font-size: 12px;
            border-radius: 3px;
            z-index: 10000;
        `;
        
        if (info.element.style.position !== 'absolute' && info.element.style.position !== 'relative') {
            info.element.style.position = 'relative';
        }
        info.element.appendChild(label);
    });
    
    return promptElements;
}

// 3. 分析页面结构
function analyzePageStructure() {
    console.log('=== 页面结构分析 ===');
    console.log('URL:', window.location.href);
    console.log('Title:', document.title);
    
    // 查找主要容器
    const mainContainers = document.querySelectorAll('main, article, section, [role="main"], [class*="main"], [class*="content"]');
    console.log('主要容器:', Array.from(mainContainers).map(el => ({
        tagName: el.tagName,
        className: el.className,
        id: el.id
    })));
    
    // 查找可能的消息容器
    const messageContainers = document.querySelectorAll('[class*="message"], [class*="chat"], [class*="conversation"], [class*="response"]');
    console.log('消息容器:', Array.from(messageContainers).map(el => ({
        tagName: el.tagName,
        className: el.className,
        id: el.id
    })));
}

// 4. 测试浮标添加
function testFloatingButtons() {
    const promptElements = findPromptElements();
    
    promptElements.forEach((info, index) => {
        // 创建测试浮标
        const button = document.createElement('div');
        button.className = 'test-floating-button';
        button.style.cssText = `
            position: absolute;
            right: -40px;
            top: 50%;
            transform: translateY(-50%);
            width: 32px;
            height: 32px;
            background: linear-gradient(135deg, #4CAF50, #2196F3);
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
            z-index: 10000;
            color: white;
            font-size: 12px;
        `;
        button.textContent = index + 1;
        
        // 确保父元素有相对定位
        if (info.element.style.position !== 'absolute' && info.element.style.position !== 'relative') {
            info.element.style.position = 'relative';
        }
        
        info.element.appendChild(button);
        
        button.addEventListener('click', () => {
            alert(`点击了Prompt ${index + 1}:\n${info.textPreview}`);
        });
    });
    
    console.log(`添加了 ${promptElements.length} 个测试浮标`);
}

// 5. 清除测试元素
function clearTestElements() {
    document.querySelectorAll('.debug-highlight').forEach(el => {
        el.classList.remove('debug-highlight');
        el.style.outline = '';
        el.style.backgroundColor = '';
        el.style.position = '';
    });
    
    document.querySelectorAll('.test-floating-button').forEach(el => el.remove());
    document.querySelectorAll('div').forEach(el => {
        if (el.textContent && el.textContent.startsWith('Prompt ') && el.style.position === 'absolute') {
            el.remove();
        }
    });
    
    console.log('已清除所有测试元素');
}

// 运行分析
console.log('开始分析...');
analyzePageStructure();
const elements = highlightPromptElements();

console.log('=== 可用命令 ===');
console.log('findPromptElements() - 查找prompt元素');
console.log('highlightPromptElements() - 高亮显示prompt元素');
console.log('testFloatingButtons() - 测试添加浮标');
console.log('clearTestElements() - 清除测试元素');
console.log('analyzePageStructure() - 分析页面结构');

console.log('分析完成！请查看高亮的元素。');
            </div>
        </div>

        <div class="debug-section">
            <h2>📊 分析结果</h2>
            <p>运行调试代码后，请将Console中的结果粘贴到这里：</p>
            <textarea id="analysis-result" style="width: 100%; height: 200px; font-family: monospace; font-size: 12px;" placeholder="请粘贴Console中的分析结果..."></textarea>
            <br>
            <button class="button" onclick="analyzeResults()">分析结果</button>
            <div id="analysis-output" class="result info" style="display: none;"></div>
        </div>

        <div class="debug-section">
            <h2>🎯 快速测试</h2>
            <p>如果你已经在Claude页面，可以直接运行这些测试：</p>
            <button class="button" onclick="runQuickTest()">快速测试</button>
            <button class="button button-orange" onclick="clearHighlights()">清除高亮</button>
            <div id="quick-test-result" class="result info" style="display: none;"></div>
        </div>
    </div>

    <script>
        function copyDebugCode() {
            const code = document.getElementById('debug-code').textContent;
            navigator.clipboard.writeText(code).then(() => {
                alert('调试代码已复制到剪贴板！\n请在Claude页面的Console中粘贴并运行。');
            });
        }
        
        function analyzeResults() {
            const result = document.getElementById('analysis-result').value;
            const output = document.getElementById('analysis-output');
            
            if (!result.trim()) {
                output.innerHTML = '请先粘贴分析结果';
                output.style.display = 'block';
                return;
            }
            
            // 简单的结果分析
            const lines = result.split('\n');
            let analysis = '=== 分析报告 ===\n\n';
            
            if (result.includes('找到包含CINEMATIC STILL的元素数量:')) {
                const countMatch = result.match(/找到包含CINEMATIC STILL的元素数量:\s*(\d+)/);
                if (countMatch) {
                    const count = parseInt(countMatch[1]);
                    analysis += `✅ 找到 ${count} 个包含prompts的元素\n`;
                    
                    if (count === 0) {
                        analysis += '❌ 没有找到prompts，可能需要:\n';
                        analysis += '  - 确保页面已完全加载\n';
                        analysis += '  - 检查prompts是否真的包含"(CINEMATIC STILL)"\n';
                        analysis += '  - 尝试滚动页面查看更多内容\n';
                    } else {
                        analysis += '✅ 可以继续分析元素结构\n';
                    }
                }
            }
            
            if (result.includes('tagName:')) {
                analysis += '\n📋 元素类型分析:\n';
                const tagMatches = result.match(/tagName:\s*"([^"]+)"/g);
                if (tagMatches) {
                    const tags = tagMatches.map(match => match.match(/"([^"]+)"/)[1]);
                    const tagCounts = {};
                    tags.forEach(tag => {
                        tagCounts[tag] = (tagCounts[tag] || 0) + 1;
                    });
                    
                    Object.entries(tagCounts).forEach(([tag, count]) => {
                        analysis += `  - ${tag}: ${count} 个\n`;
                    });
                }
            }
            
            analysis += '\n🔧 建议的修复方案:\n';
            analysis += '1. 更新选择器以匹配找到的元素类型\n';
            analysis += '2. 调整浮标定位逻辑\n';
            analysis += '3. 测试浮标在实际元素上的显示效果\n';
            
            output.textContent = analysis;
            output.style.display = 'block';
        }
        
        function runQuickTest() {
            const output = document.getElementById('quick-test-result');
            output.style.display = 'block';
            output.textContent = '正在运行快速测试...\n请检查Claude页面是否有高亮显示的元素。';
            
            // 如果当前页面是Claude页面，直接运行测试
            if (window.location.href.includes('claude.ai')) {
                try {
                    // 运行简化版的测试
                    const elements = document.querySelectorAll('*');
                    let found = 0;
                    
                    elements.forEach(el => {
                        const text = el.textContent || el.innerText;
                        if (text && text.includes('(CINEMATIC STILL)')) {
                            el.style.outline = '3px solid #ff0000';
                            el.style.backgroundColor = 'rgba(255, 255, 0, 0.2)';
                            found++;
                        }
                    });
                    
                    output.textContent = `快速测试完成！\n找到 ${found} 个包含prompts的元素。\n请查看页面上的红色高亮。`;
                } catch (error) {
                    output.textContent = '快速测试失败: ' + error.message;
                }
            } else {
                output.textContent = '请在Claude页面中运行此测试。';
            }
        }
        
        function clearHighlights() {
            if (window.location.href.includes('claude.ai')) {
                try {
                    document.querySelectorAll('*').forEach(el => {
                        el.style.outline = '';
                        el.style.backgroundColor = '';
                    });
                    
                    const output = document.getElementById('quick-test-result');
                    output.textContent = '已清除所有高亮。';
                    output.style.display = 'block';
                } catch (error) {
                    console.error('清除高亮失败:', error);
                }
            }
        }
    </script>
</body>
</html>
