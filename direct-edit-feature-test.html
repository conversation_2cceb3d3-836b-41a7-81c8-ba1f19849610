<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>直接编辑功能测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #7C3AED 0%, #EF4444 50%, #3B82F6 100%);
            color: #333;
            min-height: 100vh;
        }
        .container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.2);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }
        h1 {
            color: #7C3AED;
            text-align: center;
            margin-bottom: 40px;
            font-size: 3em;
            font-weight: 700;
            background: linear-gradient(135deg, #7C3AED, #EF4444);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        .feature-section {
            background: linear-gradient(135deg, rgba(124, 58, 237, 0.05), rgba(239, 68, 68, 0.05));
            border: 2px solid rgba(124, 58, 237, 0.2);
            padding: 30px;
            margin: 30px 0;
            border-radius: 16px;
            position: relative;
            overflow: hidden;
        }
        .feature-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(135deg, #7C3AED, #EF4444);
        }
        .demo-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin: 30px 0;
        }
        .demo-block {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 4px 16px rgba(0,0,0,0.1);
            position: relative;
        }
        .button-demo {
            display: flex;
            gap: 12px;
            align-items: center;
            margin: 15px 0;
            justify-content: flex-end;
        }
        .demo-button {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            opacity: 0.9;
        }
        .mj-button {
            background: linear-gradient(135deg, #7C3AED, #3B82F6);
            box-shadow: 0 3px 12px rgba(124, 58, 237, 0.4);
        }
        .restore-button {
            background: linear-gradient(135deg, #EF4444, #DC2626);
            box-shadow: 0 3px 12px rgba(239, 68, 68, 0.4);
        }
        .restore-button.hidden {
            display: none;
        }
        .demo-button:hover {
            opacity: 1;
            transform: scale(1.1);
        }
        .code-block {
            background: #f8f9fa;
            border: 1px solid rgba(124, 58, 237, 0.2);
            border-radius: 6px;
            padding: 16px;
            font-family: 'SF Mono', Monaco, monospace;
            font-size: 14px;
            line-height: 1.5;
            margin: 15px 0;
            position: relative;
            outline: 1px solid rgba(124, 58, 237, 0.2);
            background-color: rgba(124, 58, 237, 0.02);
            transition: all 0.2s ease;
        }
        .code-block:focus {
            outline: 2px solid rgba(124, 58, 237, 0.4);
            background-color: rgba(124, 58, 237, 0.05);
        }
        .code-block.modified {
            outline: 2px solid rgba(239, 68, 68, 0.3);
            background-color: rgba(239, 68, 68, 0.05);
        }
        .workflow-steps {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        .step {
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 4px 16px rgba(0,0,0,0.1);
            text-align: center;
            border-left: 4px solid #7C3AED;
        }
        .step-number {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #7C3AED, #3B82F6);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 15px;
            font-weight: bold;
            font-size: 18px;
        }
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 16px rgba(0,0,0,0.1);
        }
        .comparison-table th,
        .comparison-table td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid rgba(124, 58, 237, 0.1);
        }
        .comparison-table th {
            background: linear-gradient(135deg, #7C3AED, #3B82F6);
            color: white;
            font-weight: 600;
        }
        .old-feature {
            opacity: 0.6;
            text-decoration: line-through;
        }
        .new-feature {
            font-weight: 600;
            color: #7C3AED;
        }
        .highlight-box {
            background: linear-gradient(135deg, rgba(124, 58, 237, 0.1), rgba(239, 68, 68, 0.1));
            border: 2px solid rgba(124, 58, 237, 0.3);
            border-radius: 12px;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
        }
        .icon-demo {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 20px;
            margin: 20px 0;
        }
        .icon-circle {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
        }
        .purple-circle { background: linear-gradient(135deg, #7C3AED, #3B82F6); }
        .red-circle { background: linear-gradient(135deg, #EF4444, #DC2626); }
        .arrow { font-size: 24px; color: #666; }
    </style>
</head>
<body>
    <div class="container">
        <h1>✏️ 直接编辑功能</h1>
        
        <div class="feature-section">
            <h2>🚀 全新的编辑体验</h2>
            <p><strong>无需点击任何按钮，直接编辑代码块内容！修改后自动显示恢复图标。</strong></p>
            
            <div class="icon-demo">
                <div class="icon-circle purple-circle">M</div>
                <div class="arrow">+</div>
                <div class="icon-circle red-circle">🔄</div>
                <div style="margin-left: 15px; font-weight: 600; color: #7C3AED;">
                    M按钮 + 智能恢复图标
                </div>
            </div>
        </div>

        <div class="demo-container">
            <!-- 未修改状态 -->
            <div class="demo-block">
                <h3>📝 未修改状态</h3>
                <div class="code-block" contenteditable="true">
A majestic dragon soaring through stormy clouds, lightning illuminating its scales, fantasy art style, dramatic lighting
                </div>
                <div class="button-demo">
                    <div class="demo-button mj-button">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="white">
                            <path d="M3,21V3H7L12,14L17,3H21V21H17V8L13,18H11L7,8V21H3Z"/>
                        </svg>
                    </div>
                    <div class="demo-button restore-button hidden">
                        <svg width="18" height="18" viewBox="0 0 24 24" fill="white">
                            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                        </svg>
                    </div>
                </div>
                <p style="font-size: 12px; color: #666;">
                    ↑ 默认状态：只显示M按钮，微妙的紫色边框
                </p>
            </div>

            <!-- 已修改状态 -->
            <div class="demo-block">
                <h3>✏️ 已修改状态</h3>
                <div class="code-block modified" contenteditable="true">
A majestic dragon soaring through stormy clouds, lightning illuminating its scales, fantasy art style, dramatic lighting --ar 16:9 --v 6
                </div>
                <div class="button-demo">
                    <div class="demo-button mj-button">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="white">
                            <path d="M3,21V3H7L12,14L17,3H21V21H17V8L13,18H11L7,8V21H3Z"/>
                        </svg>
                    </div>
                    <div class="demo-button restore-button">
                        <svg width="18" height="18" viewBox="0 0 24 24" fill="white">
                            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                        </svg>
                    </div>
                </div>
                <p style="font-size: 12px; color: #666;">
                    ↑ 修改后：显示恢复图标，红色边框提示
                </p>
            </div>
        </div>

        <div class="feature-section">
            <h2>🔄 工作流程</h2>
            <div class="workflow-steps">
                <div class="step">
                    <div class="step-number">1</div>
                    <h4>直接编辑</h4>
                    <p>点击代码块直接开始编辑，无需任何按钮</p>
                </div>
                
                <div class="step">
                    <div class="step-number">2</div>
                    <h4>自动检测</h4>
                    <p>系统自动检测内容变化，显示恢复图标</p>
                </div>
                
                <div class="step">
                    <div class="step-number">3</div>
                    <h4>发送或恢复</h4>
                    <p>点击M发送修改后的内容，或点击恢复图标还原</p>
                </div>
            </div>
        </div>

        <div class="feature-section">
            <h2>📊 功能对比</h2>
            <table class="comparison-table">
                <thead>
                    <tr>
                        <th>功能</th>
                        <th>旧版本</th>
                        <th>新版本</th>
                        <th>优势</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><strong>编辑方式</strong></td>
                        <td class="old-feature">点击E按钮切换编辑模式</td>
                        <td class="new-feature">直接点击代码块编辑</td>
                        <td>更直观，减少操作步骤</td>
                    </tr>
                    <tr>
                        <td><strong>按钮数量</strong></td>
                        <td class="old-feature">M按钮 + E按钮（2个）</td>
                        <td class="new-feature">M按钮 + 智能恢复图标</td>
                        <td>界面更简洁</td>
                    </tr>
                    <tr>
                        <td><strong>状态指示</strong></td>
                        <td class="old-feature">按钮颜色变化</td>
                        <td class="new-feature">边框颜色 + 恢复图标</td>
                        <td>视觉反馈更清晰</td>
                    </tr>
                    <tr>
                        <td><strong>恢复功能</strong></td>
                        <td class="old-feature">双击恢复</td>
                        <td class="new-feature">点击恢复图标</td>
                        <td>操作更明确</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="feature-section">
            <h2>🎨 视觉设计</h2>
            <div class="demo-container">
                <div class="demo-block">
                    <h4>🟣 默认状态</h4>
                    <ul>
                        <li>微妙的紫色边框</li>
                        <li>浅紫色背景</li>
                        <li>只显示M按钮</li>
                        <li>焦点时边框加深</li>
                    </ul>
                </div>
                
                <div class="demo-block">
                    <h4>🔴 修改状态</h4>
                    <ul>
                        <li>红色边框提示</li>
                        <li>浅红色背景</li>
                        <li>显示恢复图标</li>
                        <li>清晰的状态区分</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="highlight-box">
            <h2>✨ 核心优势</h2>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-top: 20px;">
                <div>
                    <h4>🚀 更直观</h4>
                    <p>直接编辑，无需切换模式</p>
                </div>
                <div>
                    <h4>🎯 更智能</h4>
                    <p>自动检测修改，智能显示恢复选项</p>
                </div>
                <div>
                    <h4>🎨 更美观</h4>
                    <p>微妙的视觉提示，不干扰编辑体验</p>
                </div>
                <div>
                    <h4>⚡ 更高效</h4>
                    <p>减少点击次数，提升操作效率</p>
                </div>
            </div>
        </div>

        <div class="feature-section">
            <h2>📝 使用说明</h2>
            <ol style="font-size: 16px; line-height: 1.8;">
                <li><strong>按 <code>Ctrl+Shift+Z</code>：</strong>显示M按钮和可编辑的代码块</li>
                <li><strong>直接编辑：</strong>点击代码块开始编辑，无需额外操作</li>
                <li><strong>自动检测：</strong>修改内容后自动显示红色恢复图标</li>
                <li><strong>发送修改：</strong>点击M按钮发送当前内容到Midjourney</li>
                <li><strong>恢复原始：</strong>点击恢复图标还原到原始内容</li>
            </ol>
            
            <p style="background: rgba(124, 58, 237, 0.1); padding: 15px; border-radius: 8px; margin-top: 20px;">
                <strong>💡 提示：</strong>现在编辑prompt变得前所未有的简单！直接点击就能编辑，修改后一目了然，操作更加流畅自然。
            </p>
        </div>
    </div>
</body>
</html>
