<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>后台发送功能测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .title {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 3px solid #4CAF50;
            padding-bottom: 15px;
        }
        .feature-section {
            margin: 25px 0;
            padding: 20px;
            border: 2px solid #4CAF50;
            border-radius: 8px;
            background-color: #e8f5e8;
        }
        .test-section {
            margin: 25px 0;
            padding: 20px;
            border: 2px solid #17a2b8;
            border-radius: 8px;
            background-color: #d1ecf1;
        }
        .comparison-section {
            margin: 25px 0;
            padding: 20px;
            border: 2px solid #ffc107;
            border-radius: 8px;
            background-color: #fff3cd;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .status-success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status-info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .code-fix {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            overflow-x: auto;
        }
        .test-block {
            margin: 15px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #f8f9fa;
            font-family: monospace;
            position: relative;
        }
        .step {
            background: #e9ecef;
            border-left: 4px solid #4CAF50;
            padding: 15px;
            margin: 10px 0;
        }
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }
        .comparison-table th,
        .comparison-table td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        .comparison-table th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        .old-behavior {
            background-color: #f8d7da;
        }
        .new-behavior {
            background-color: #d4edda;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">🔄 后台发送功能</h1>
        
        <div class="feature-section">
            <h2>✨ 新功能特性</h2>
            <div class="status status-success">
                <strong>后台发送模式：</strong><br>
                点击M按钮后，prompt会在后台发送到Midjourney，<br>
                <strong>不会切换窗口或标签页</strong>，保持当前工作流程不被打断！
            </div>
            
            <h3>🎯 核心改进：</h3>
            <ul>
                <li>✅ <strong>无干扰发送</strong> - 不会跳转到Midjourney页面</li>
                <li>✅ <strong>保持专注</strong> - 继续在Claude页面工作</li>
                <li>✅ <strong>后台处理</strong> - Midjourney在后台接收prompt</li>
                <li>✅ <strong>视觉反馈</strong> - 绿色通知确认发送成功</li>
                <li>✅ <strong>跨窗口支持</strong> - 支持分屏工作模式</li>
            </ul>
        </div>

        <div class="comparison-section">
            <h2>📊 行为对比</h2>
            <table class="comparison-table">
                <thead>
                    <tr>
                        <th>操作</th>
                        <th class="old-behavior">旧行为（跳转模式）</th>
                        <th class="new-behavior">新行为（后台模式）</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><strong>点击M按钮</strong></td>
                        <td class="old-behavior">❌ 自动跳转到Midjourney页面</td>
                        <td class="new-behavior">✅ 保持在当前Claude页面</td>
                    </tr>
                    <tr>
                        <td><strong>窗口切换</strong></td>
                        <td class="old-behavior">❌ 强制切换窗口和标签页</td>
                        <td class="new-behavior">✅ 不切换任何窗口</td>
                    </tr>
                    <tr>
                        <td><strong>工作流程</strong></td>
                        <td class="old-behavior">❌ 打断当前工作</td>
                        <td class="new-behavior">✅ 无缝继续工作</td>
                    </tr>
                    <tr>
                        <td><strong>分屏支持</strong></td>
                        <td class="old-behavior">❌ 需要手动切换回来</td>
                        <td class="new-behavior">✅ 完美支持分屏工作</td>
                    </tr>
                    <tr>
                        <td><strong>反馈方式</strong></td>
                        <td class="old-behavior">⚠️ 依赖页面跳转确认</td>
                        <td class="new-behavior">✅ 绿色通知 + 按钮状态</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="test-section">
            <h2>🧪 测试代码块</h2>
            <p>使用这些代码块测试后台发送功能：</p>
            
            <h4>测试代码块1 - 风景摄影：</h4>
            <pre class="test-block">Majestic mountain landscape at golden hour, snow-capped peaks reflecting in crystal clear alpine lake, dramatic clouds, professional landscape photography --ar 16:9 --v 6</pre>

            <h4>测试代码块2 - 科幻场景：</h4>
            <pre class="test-block">Futuristic space station orbiting Earth, massive rotating rings, docking bays with spacecraft, blue planet visible through observation windows, hard science fiction concept art --ar 21:9</pre>

            <h4>测试代码块3 - 幻想艺术：</h4>
            <pre class="test-block">Ancient dragon sleeping in treasure-filled cave, golden coins and jewels scattered around, mystical glowing crystals, fantasy art style, warm lighting --ar 4:3</pre>
        </div>

        <div class="test-section">
            <h2>🔍 测试步骤</h2>
            
            <div class="step">
                <h4>步骤1：准备环境</h4>
                <ol>
                    <li>重新加载插件（Chrome扩展页面刷新）</li>
                    <li>打开Midjourney页面（www.midjourney.com/imagine）</li>
                    <li>可以将Midjourney拖到新窗口分屏显示</li>
                    <li>确保Claude页面有代码块显示M按钮</li>
                </ol>
            </div>

            <div class="step">
                <h4>步骤2：测试后台发送</h4>
                <ol>
                    <li>在Claude页面点击任意M按钮</li>
                    <li>观察是否<strong>没有</strong>跳转到Midjourney页面</li>
                    <li>查看是否出现绿色"已后台发送到Midjourney"通知</li>
                    <li>M按钮应该显示绿色勾号3秒</li>
                </ol>
            </div>

            <div class="step">
                <h4>步骤3：验证发送结果</h4>
                <ol>
                    <li>手动切换到Midjourney页面</li>
                    <li>检查prompt是否已经填入输入框</li>
                    <li>验证prompt内容是否正确</li>
                    <li>可以直接点击发送按钮生成图片</li>
                </ol>
            </div>
        </div>

        <div class="feature-section">
            <h2>📋 预期体验</h2>
            <div class="status status-info">
                <strong>理想的工作流程：</strong><br>
                1. 在Claude页面浏览和选择prompt<br>
                2. 点击M按钮后台发送<br>
                3. 看到绿色通知确认发送<br>
                4. 继续在Claude页面工作<br>
                5. 需要时切换到Midjourney查看结果<br>
                6. 重复上述流程，高效工作
            </div>
        </div>

        <div class="feature-section">
            <h2>🎯 技术实现</h2>
            <div class="code-fix">
// 核心改进：移除标签页切换逻辑
async sendToMidjourneyTab(tabId, prompt) {
    // 直接发送消息到content script，不切换标签页
    const response = await chrome.tabs.sendMessage(tabId, {
        action: 'sendSinglePrompt',
        prompt: prompt
    });
    
    console.log('Prompt后台发送成功，保持当前页面');
    return response || { success: true, message: '已后台发送到Midjourney' };
}

// 增强的用户反馈
showBackgroundSendNotification() {
    // 显示绿色滑入通知
    // "已后台发送到Midjourney"
}
            </div>
        </div>

        <div class="feature-section">
            <h2>🚀 使用建议</h2>
            <ul>
                <li>🖥️ <strong>分屏工作</strong> - 将Claude和Midjourney放在不同窗口，提高效率</li>
                <li>⚡ <strong>批量发送</strong> - 可以连续点击多个M按钮，批量发送prompt</li>
                <li>👀 <strong>定期检查</strong> - 偶尔切换到Midjourney查看生成进度</li>
                <li>🔄 <strong>无缝切换</strong> - 在Claude和Midjourney之间自由切换工作</li>
            </ul>
        </div>
    </div>

    <script>
        // 页面加载时显示状态
        window.onload = function() {
            setTimeout(() => {
                console.log('🔄 后台发送功能测试页面已加载');
                console.log('现在点击M按钮不会跳转页面，而是后台发送！');
                console.log('享受无干扰的工作流程吧！');
            }, 1000);
        };
    </script>
</body>
</html>
