<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>定期检查修复测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .title {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 3px solid #4CAF50;
            padding-bottom: 15px;
        }
        .fix-section {
            margin: 25px 0;
            padding: 20px;
            border: 2px solid #4CAF50;
            border-radius: 8px;
            background-color: #e8f5e8;
        }
        .problem-section {
            margin: 25px 0;
            padding: 20px;
            border: 2px solid #dc3545;
            border-radius: 8px;
            background-color: #f8d7da;
        }
        .test-section {
            margin: 25px 0;
            padding: 20px;
            border: 2px solid #17a2b8;
            border-radius: 8px;
            background-color: #d1ecf1;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .status-success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status-error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .code-fix {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            overflow-x: auto;
        }
        .test-block {
            margin: 15px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #f8f9fa;
            font-family: monospace;
            position: relative;
        }
        .step {
            background: #e9ecef;
            border-left: 4px solid #4CAF50;
            padding: 15px;
            margin: 10px 0;
        }
        .timeline {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
        }
        .timeline-item {
            margin: 8px 0;
            padding: 5px 0;
            border-bottom: 1px dotted #ddd;
        }
        .timeline-item:last-child {
            border-bottom: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">🔧 定期检查打断修复</h1>
        
        <div class="problem-section">
            <h2>❌ 问题根源</h2>
            <div class="status status-error">
                <strong>定期检查打断绿色勾号：</strong><br>
                每3秒的定期检查会调用 addFloatingButtons()，<br>
                这会移除所有现有按钮并重新创建，打断正在显示的绿色勾号！
            </div>
            
            <h3>🕐 问题时间线：</h3>
            <div class="timeline">
                <div class="timeline-item"><strong>0秒</strong> - 用户点击M按钮</div>
                <div class="timeline-item"><strong>1秒</strong> - 显示橙色加载状态</div>
                <div class="timeline-item"><strong>2秒</strong> - 发送成功，显示绿色勾号</div>
                <div class="timeline-item"><strong>3秒</strong> - ❌ 定期检查触发，移除所有按钮</div>
                <div class="timeline-item"><strong>3.1秒</strong> - 重新创建按钮，绿色勾号消失</div>
                <div class="timeline-item"><strong>结果</strong> - 用户看不到完整的成功反馈</div>
            </div>

            <h3>🔍 问题代码：</h3>
            <div class="code-fix">
// 每3秒执行一次，会打断按钮状态
setInterval(() => {
    if (missingButtons > 0) {
        this.addFloatingButtons(); // ❌ 这会移除所有现有按钮！
    }
}, 3000);

// addFloatingButtons 方法
addFloatingButtons() {
    this.removeFloatingButtons(); // ❌ 移除所有按钮，包括正在显示绿色勾号的
    // 重新创建所有按钮...
}
            </div>
        </div>

        <div class="fix-section">
            <h2>✅ 修复方案</h2>
            
            <h3>🎯 智能按钮管理：</h3>
            <div class="code-fix">
// 修复后：只添加缺失的按钮，不影响现有按钮
setInterval(() => {
    if (missingButtons > 0) {
        this.addMissingButtons(); // ✅ 只添加缺失的，保护现有状态
    }
}, 3000);

// 新的智能添加方法
addMissingButtons() {
    // 检查每个代码块是否已有按钮
    const hasButton = element.querySelector('.claude-mj-floating-btn') || 
                     element.parentElement?.querySelector('.claude-mj-floating-btn');
    
    // 只为没有按钮的代码块添加按钮
    if (!hasButton && isValidCodeBlock) {
        this.createFloatingButton(element, text, index);
    }
}
            </div>

            <h3>🛡️ 保护机制：</h3>
            <ul>
                <li>✅ <strong>状态保护</strong> - 不移除正在显示状态的按钮</li>
                <li>✅ <strong>智能检测</strong> - 只添加真正缺失的按钮</li>
                <li>✅ <strong>非破坏性</strong> - 不影响现有按钮的状态</li>
                <li>✅ <strong>精确定位</strong> - 准确识别哪些代码块缺少按钮</li>
            </ul>
        </div>

        <div class="test-section">
            <h2>🧪 测试代码块</h2>
            <p>使用这些代码块测试修复效果，观察绿色勾号是否能完整显示3秒：</p>
            
            <h4>测试代码块1：</h4>
            <pre class="test-block">Serene mountain lake at dawn, mist rising from crystal clear water, snow-capped peaks reflected perfectly, golden sunrise light, peaceful nature photography --ar 16:9</pre>

            <h4>测试代码块2：</h4>
            <pre class="test-block">Ancient wizard's tower surrounded by floating magical orbs, mystical energy crackling through the air, starry night sky, fantasy concept art --ar 9:16</pre>

            <h4>测试代码块3：</h4>
            <pre class="test-block">Cyberpunk street market in neon-lit alley, vendors selling holographic goods, rain-soaked pavement reflecting colorful lights, futuristic urban scene --ar 21:9</pre>

            <h4>测试代码块4：</h4>
            <pre class="test-block">Underwater coral garden with bioluminescent sea creatures, deep ocean blues and greens, shafts of sunlight filtering down, marine life paradise --ar 4:3</pre>
        </div>

        <div class="test-section">
            <h2>🔍 测试步骤</h2>
            
            <div class="step">
                <h4>步骤1：重新加载插件</h4>
                <p>在Chrome扩展页面刷新插件，确保修复生效</p>
            </div>

            <div class="step">
                <h4>步骤2：准备环境</h4>
                <ol>
                    <li>打开Midjourney页面</li>
                    <li>确保Claude页面有代码块显示M按钮</li>
                    <li>打开Chrome开发者工具的控制台</li>
                </ol>
            </div>

            <div class="step">
                <h4>步骤3：关键测试 - 等待定期检查</h4>
                <ol>
                    <li>点击任意M按钮</li>
                    <li><strong>关键</strong>：等待3秒，观察绿色勾号是否被打断</li>
                    <li>检查控制台是否显示"定期检查"日志</li>
                    <li>验证绿色勾号能否完整显示3秒</li>
                </ol>
            </div>

            <div class="step">
                <h4>步骤4：批量测试</h4>
                <ol>
                    <li>快速连续点击多个M按钮</li>
                    <li>观察每个按钮的绿色勾号是否都能完整显示</li>
                    <li>等待6秒（两个定期检查周期）</li>
                    <li>确认所有按钮状态都正常</li>
                </ol>
            </div>
        </div>

        <div class="fix-section">
            <h2>📋 预期控制台输出</h2>
            <div class="code-fix">
✅ 修复后的正常日志：
- "🔍 定期检查: 发现 4 个代码块, 当前有 4 个M按钮"
- "🎯 智能添加缺失的M按钮，保护现有按钮状态"
- "✅ 智能添加了 0 个缺失的M按钮" (因为都已存在)
- "✅ 检测到成功响应，显示绿色勾号"

❌ 不应该再看到：
- "🔄 清空缓存，强制重新检查所有代码块"
- "移除所有现有的浮标"
- 绿色勾号突然消失的现象
            </div>
        </div>

        <div class="fix-section">
            <h2>🎯 成功标准</h2>
            <div class="status status-success">
                <strong>修复成功的标志：</strong><br>
                ✅ 绿色勾号能完整显示3秒，不被打断<br>
                ✅ 定期检查不会移除现有按钮<br>
                ✅ 只有真正缺失的代码块才会添加新按钮<br>
                ✅ 按钮状态变化流畅，无突然消失<br>
                ✅ 控制台显示"智能添加"而非"强制重新检查"<br>
                ✅ 用户能看到完整的成功反馈
            </div>
        </div>

        <div class="test-section">
            <h2>🚀 验证要点</h2>
            <p>特别注意以下几个关键时刻：</p>
            <ol>
                <li><strong>3秒时刻</strong> - 定期检查触发时，绿色勾号不应消失</li>
                <li><strong>6秒时刻</strong> - 第二次定期检查时，按钮状态应保持稳定</li>
                <li><strong>滚动时</strong> - 滚动页面时，现有按钮状态不应被重置</li>
                <li><strong>新内容</strong> - 动态加载的新代码块应该能正确添加按钮</li>
            </ol>
        </div>
    </div>

    <script>
        // 页面加载时显示状态
        window.onload = function() {
            setTimeout(() => {
                console.log('🔧 定期检查修复测试页面已加载');
                console.log('现在绿色勾号应该能完整显示，不会被定期检查打断了！');
                console.log('请特别注意3秒和6秒时刻的按钮状态');
            }, 1000);
        };
    </script>
</body>
</html>
