// browser-shared-storage.js - 纯浏览器环境的共享存储服务

class BrowserSharedStorage {
    constructor() {
        this.storageKey = 'claude_midjourney_prompts';
        this.lockKey = 'claude_midjourney_lock';
        this.init();
    }

    init() {
        console.log('初始化浏览器共享存储服务');
        this.setupStorageListener();
    }

    async savePrompts(prompts) {
        try {
            const data = {
                prompts: prompts,
                timestamp: Date.now(),
                source: 'claude',
                version: '1.0'
            };

            // 方法1: 使用localStorage
            localStorage.setItem(this.storageKey, JSON.stringify(data));
            console.log('已保存到localStorage:', prompts.length, '个prompts');

            // 方法2: 使用Chrome存储API
            await this.saveToChromeStorage(data);

            // 方法3: 尝试使用IndexedDB作为备用存储
            await this.saveToIndexedDB(data);

            return { success: true, message: '已保存到共享存储' };
        } catch (error) {
            console.error('保存prompts失败:', error);
            return { success: false, error: error.message };
        }
    }

    async loadPrompts() {
        try {
            // 方法1: 从localStorage加载
            const localData = localStorage.getItem(this.storageKey);
            if (localData) {
                const data = JSON.parse(localData);
                console.log('从localStorage加载了', data.prompts.length, '个prompts');
                return {
                    success: true,
                    prompts: data.prompts,
                    timestamp: data.timestamp,
                    source: 'localStorage'
                };
            }

            // 方法2: 从IndexedDB加载
            const indexedData = await this.loadFromIndexedDB();
            if (indexedData) {
                console.log('从IndexedDB加载了', indexedData.prompts.length, '个prompts');
                return {
                    success: true,
                    prompts: indexedData.prompts,
                    timestamp: indexedData.timestamp,
                    source: 'indexedDB'
                };
            }

            // 方法3: 从Chrome存储加载
            const chromeData = await this.loadFromChromeStorage();
            if (chromeData) {
                console.log('从Chrome存储加载了', chromeData.prompts.length, '个prompts');
                return {
                    success: true,
                    prompts: chromeData.prompts,
                    timestamp: chromeData.timestamp,
                    source: 'chromeStorage'
                };
            }

            return { success: false, message: '没有找到共享的prompts' };
        } catch (error) {
            console.error('加载prompts失败:', error);
            return { success: false, error: error.message };
        }
    }

    async saveToChromeStorage(data) {
        try {
            if (typeof chrome !== 'undefined' && chrome.storage) {
                await chrome.storage.local.set({ [this.storageKey]: data });
                console.log('已保存到Chrome存储');
                return true;
            }
            return false;
        } catch (error) {
            console.error('Chrome存储保存失败:', error);
            return false;
        }
    }

    async loadFromChromeStorage() {
        try {
            if (typeof chrome !== 'undefined' && chrome.storage) {
                const result = await chrome.storage.local.get([this.storageKey]);
                return result[this.storageKey] || null;
            }
            return null;
        } catch (error) {
            console.error('Chrome存储加载失败:', error);
            return null;
        }
    }

    async loadFromFile() {
        // 不再使用文件选择器，改为提示用户手动操作
        console.log('文件加载功能已禁用，请使用其他方式加载数据');
        return null;
    }

    async saveToIndexedDB(data) {
        return new Promise((resolve) => {
            try {
                const request = indexedDB.open('ClaudeMidjourneyDB', 1);
                
                request.onerror = () => {
                    console.log('IndexedDB不可用');
                    resolve(false);
                };
                
                request.onupgradeneeded = (event) => {
                    const db = event.target.result;
                    if (!db.objectStoreNames.contains('prompts')) {
                        db.createObjectStore('prompts', { keyPath: 'id' });
                    }
                };
                
                request.onsuccess = (event) => {
                    const db = event.target.result;
                    const transaction = db.transaction(['prompts'], 'readwrite');
                    const store = transaction.objectStore('prompts');
                    
                    const saveData = { ...data, id: 'current' };
                    store.put(saveData);
                    
                    transaction.oncomplete = () => {
                        console.log('已保存到IndexedDB');
                        resolve(true);
                    };
                    
                    transaction.onerror = () => {
                        console.error('IndexedDB保存失败');
                        resolve(false);
                    };
                };
            } catch (error) {
                console.error('IndexedDB操作失败:', error);
                resolve(false);
            }
        });
    }

    async loadFromIndexedDB() {
        return new Promise((resolve) => {
            try {
                const request = indexedDB.open('ClaudeMidjourneyDB', 1);
                
                request.onerror = () => {
                    resolve(null);
                };
                
                request.onsuccess = (event) => {
                    const db = event.target.result;
                    
                    if (!db.objectStoreNames.contains('prompts')) {
                        resolve(null);
                        return;
                    }
                    
                    const transaction = db.transaction(['prompts'], 'readonly');
                    const store = transaction.objectStore('prompts');
                    const getRequest = store.get('current');
                    
                    getRequest.onsuccess = () => {
                        resolve(getRequest.result || null);
                    };
                    
                    getRequest.onerror = () => {
                        resolve(null);
                    };
                };
            } catch (error) {
                console.error('IndexedDB读取失败:', error);
                resolve(null);
            }
        });
    }

    setupStorageListener() {
        // 监听localStorage变化
        window.addEventListener('storage', (event) => {
            if (event.key === this.storageKey) {
                console.log('检测到共享存储更新');
                this.notifyUpdate(event.newValue);
            }
        });
    }

    notifyUpdate(newValue) {
        // 通知UI更新
        const event = new CustomEvent('sharedPromptsUpdated', {
            detail: newValue ? JSON.parse(newValue) : null
        });
        window.dispatchEvent(event);
    }

    async clearSharedData() {
        try {
            localStorage.removeItem(this.storageKey);
            
            // 清除IndexedDB
            const request = indexedDB.deleteDatabase('ClaudeMidjourneyDB');
            request.onsuccess = () => {
                console.log('IndexedDB已清除');
            };
            
            console.log('共享数据已清除');
            return { success: true };
        } catch (error) {
            console.error('清除共享数据失败:', error);
            return { success: false, error: error.message };
        }
    }

    // 监听共享数据变化
    onSharedDataChange(callback) {
        window.addEventListener('sharedPromptsUpdated', (event) => {
            callback(event.detail);
        });
    }
}

// 导出服务
window.BrowserSharedStorage = BrowserSharedStorage;
