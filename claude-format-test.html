<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON>代码块格式测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .title {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 3px solid #7C3AED;
            padding-bottom: 15px;
        }
        .test-section {
            margin: 25px 0;
            padding: 20px;
            border: 2px solid #ddd;
            border-radius: 8px;
            background-color: #f8f9fa;
        }
        .success-section {
            border-color: #28a745;
            background-color: #d4edda;
        }
        .normal-text {
            padding: 15px;
            background: white;
            border: 1px solid #ddd;
            border-radius: 5px;
            margin: 10px 0;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .status-success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">🎯 Claude代码块格式测试</h1>
        
        <div class="test-section success-section">
            <h2>✅ 新的识别策略</h2>
            <p><strong>不再依赖关键词！</strong></p>
            <ul>
                <li>✅ 只识别Claude的代码块格式</li>
                <li>✅ 专门检测 <code>pre.code-block__code</code></li>
                <li>✅ 检测标准的 <code>pre</code> 和 <code>code</code> 标签</li>
                <li>✅ 不管内容是什么，只要在代码块中就显示M浮标</li>
            </ul>
        </div>

        <div class="test-section">
            <h2>🧪 测试用例</h2>
            
            <h3>✅ 应该显示M浮标的代码块：</h3>
            
            <h4>1. Claude标准代码块（赛博朋克城市）：</h4>
            <pre class="code-block__code !my-0 !rounded-lg !text-sm !leading-relaxed" style="background: transparent; color: rgb(56, 58, 66); font-family: var(--font-mono); direction: ltr; text-align: left; white-space: pre; word-spacing: normal; word-break: normal; line-height: 1.5; tab-size: 2; hyphens: none; padding: 1em; margin: 0.5em 0px; overflow: auto; border-radius: 0.3em;"><code style="background: transparent; color: rgb(56, 58, 66); font-family: var(--font-mono); direction: ltr; text-align: left; white-space: pre-wrap; word-spacing: normal; word-break: normal; line-height: 1.5; tab-size: 2; hyphens: none;"><span><span>A futuristic cyberpunk cityscape at night, neon lights reflecting on wet streets, towering skyscrapers with holographic advertisements, flying cars in the distance, rain-soaked pavement, purple and blue neon palette, cinematic lighting, ultra-detailed, 8k resolution --ar 16:9 --v 6</span></span></code></pre>

            <h4>2. Claude标准代码块（梦幻森林精灵）：</h4>
            <pre class="code-block__code !my-0 !rounded-lg !text-sm !leading-relaxed" style="background: transparent; color: rgb(56, 58, 66); font-family: var(--font-mono); direction: ltr; text-align: left; white-space: pre; word-spacing: normal; word-break: normal; line-height: 1.5; tab-size: 2; hyphens: none; padding: 1em; margin: 0.5em 0px; overflow: auto; border-radius: 0.3em;"><code style="background: transparent; color: rgb(56, 58, 66); font-family: var(--font-mono); direction: ltr; text-align: left; white-space: pre-wrap; word-spacing: normal; word-break: normal; line-height: 1.5; tab-size: 2; hyphens: none;"><span><span>An ethereal forest fairy with translucent butterfly wings, sitting on a glowing mushroom, surrounded by fireflies and magical particles, enchanted woodland background, soft golden hour lighting, fantasy art style, delicate features, flowing hair, mystical atmosphere --ar 3:4 --v 6</span></span></code></pre>

            <h4>3. Claude标准代码块（现代简约室内）：</h4>
            <pre class="code-block__code !my-0 !rounded-lg !text-sm !leading-relaxed" style="background: transparent; color: rgb(56, 58, 66); font-family: var(--font-mono); direction: ltr; text-align: left; white-space: pre; word-spacing: normal; word-break: normal; line-height: 1.5; tab-size: 2; hyphens: none; padding: 1em; margin: 0.5em 0px; overflow: auto; border-radius: 0.3em;"><code style="background: transparent; color: rgb(56, 58, 66); font-family: var(--font-mono); direction: ltr; text-align: left; white-space: pre-wrap; word-spacing: normal; word-break: normal; line-height: 1.5; tab-size: 2; hyphens: none;"><span><span>Modern minimalist living room interior, clean lines, neutral color palette with warm wood accents, large floor-to-ceiling windows, natural light streaming in, cozy reading corner, contemporary furniture, indoor plants, scandinavian design aesthetic, professional architectural photography --ar 4:3 --v 6</span></span></code></pre>

            <h4>4. 简化的pre标签：</h4>
            <pre>Even this simple pre tag should show M button now, regardless of content!</pre>

            <h4>5. 代码标签：</h4>
            <code>This code tag should also show M button!</code>

            <h3>❌ 不应该显示M浮标的普通文本：</h3>
            
            <h4>6. 普通段落（包含相同内容）：</h4>
            <div class="normal-text">
                Modern minimalist living room interior, clean lines, neutral color palette with warm wood accents, large floor-to-ceiling windows, natural light streaming in, cozy reading corner, contemporary furniture, indoor plants, scandinavian design aesthetic, professional architectural photography --ar 4:3 --v 6
            </div>

            <h4>7. 普通div（包含相同内容）：</h4>
            <div class="normal-text">
                An ethereal forest fairy with translucent butterfly wings, sitting on a glowing mushroom, surrounded by fireflies and magical particles, enchanted woodland background, soft golden hour lighting, fantasy art style, delicate features, flowing hair, mystical atmosphere --ar 3:4 --v 6
            </div>
        </div>

        <div class="test-section">
            <h2>🔧 技术实现</h2>
            
            <h3>📍 新的识别逻辑：</h3>
            <div style="background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 5px; padding: 15px; margin: 10px 0; font-family: monospace; font-size: 12px;">
isClaudeCodeBlock(element) {
    // 1. 直接是pre或code标签
    if (tagName === 'pre' || tagName === 'code') return true;
    
    // 2. 检查Claude特有的代码块类名
    if (className.includes('code-block__code')) return true;
    
    // 3. 检查是否在代码块容器内
    if (element.closest('pre.code-block__code, pre, code')) return true;
    
    return false;
}

// 主要检查逻辑
if (isClaudeCodeBlock(element) && 
    text && 
    text.trim().length > 10 && 
    text.trim().length < 2000) {
    // 显示M浮标
}
            </div>

            <h3>🎯 关键改进：</h3>
            <ul>
                <li>✅ <strong>不再依赖关键词</strong> - 避免遗漏</li>
                <li>✅ <strong>专注格式识别</strong> - 只看是否是代码块</li>
                <li>✅ <strong>简化逻辑</strong> - 减少误判</li>
                <li>✅ <strong>覆盖所有情况</strong> - 包括第三个代码块</li>
            </ul>
        </div>

        <div class="test-section">
            <h2>📋 预期结果</h2>
            <div class="status status-success">
                ✅ <strong>成功标准：</strong><br>
                - 代码块1、2、3、4、5都应该显示M浮标<br>
                - 普通文本6、7不应该显示M浮标<br>
                - 控制台显示："✅ 识别为Claude代码块"<br>
                - 第三个代码块现在应该能被识别了！
            </div>
        </div>
    </div>

    <script>
        // 页面加载时显示状态
        window.onload = function() {
            setTimeout(() => {
                console.log('🎯 Claude代码块格式测试页面已加载');
                console.log('现在插件应该能识别所有代码块，不管内容是什么！');
            }, 1000);
        };
    </script>
</body>
</html>
