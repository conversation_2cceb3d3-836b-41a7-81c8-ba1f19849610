<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>改进复原功能测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #f0f4f8 0%, #d6e7f0 100%);
            color: #333;
            min-height: 100vh;
        }
        .container {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 10px 40px rgba(0,0,0,0.1);
            border: 1px solid rgba(0,0,0,0.05);
        }
        h1 {
            color: #2563eb;
            text-align: center;
            margin-bottom: 40px;
            font-size: 3em;
            font-weight: 700;
        }
        .feature-section {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            padding: 30px;
            margin: 30px 0;
            border-radius: 12px;
        }
        .demo-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin: 30px 0;
        }
        .demo-block {
            background: white;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 4px 16px rgba(0,0,0,0.05);
            border: 1px solid #e2e8f0;
            position: relative;
            min-height: 150px;
        }
        .button-demo {
            position: absolute;
            right: -40px;
            top: 50%;
            transform: translateY(-50%);
            display: flex;
            gap: 8px;
            align-items: center;
        }
        .button-demo.with-restore {
            right: -84px;
        }
        .demo-button {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            opacity: 0.9;
        }
        .mj-button {
            background: linear-gradient(135deg, #7C3AED, #3B82F6);
            box-shadow: 0 3px 12px rgba(124, 58, 237, 0.4);
        }
        .restore-button {
            background: linear-gradient(135deg, #EF4444, #DC2626);
            box-shadow: 0 3px 12px rgba(239, 68, 68, 0.4);
        }
        .demo-button:hover {
            opacity: 1;
            transform: scale(1.1);
        }
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            padding: 16px;
            font-family: 'SF Mono', Monaco, monospace;
            font-size: 14px;
            line-height: 1.5;
            margin: 15px 0;
            position: relative;
            cursor: text;
            word-wrap: break-word;
            white-space: pre-wrap;
        }
        .code-block.one-line {
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            background: #fee2e2;
            border-color: #ef4444;
        }
        .problem-demo {
            background: linear-gradient(135deg, #fef2f2, #fee2e2);
            border: 2px solid #ef4444;
            border-radius: 12px;
            padding: 20px;
            margin: 20px 0;
        }
        .solution-demo {
            background: linear-gradient(135deg, #f0fdf4, #dcfce7);
            border: 2px solid #22c55e;
            border-radius: 12px;
            padding: 20px;
            margin: 20px 0;
        }
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 16px rgba(0,0,0,0.05);
        }
        .comparison-table th,
        .comparison-table td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid #e2e8f0;
        }
        .comparison-table th {
            background: #2563eb;
            color: white;
            font-weight: 600;
        }
        .problem {
            color: #ef4444;
            font-weight: 600;
        }
        .solution {
            color: #22c55e;
            font-weight: 600;
        }
        .icon-comparison {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 30px;
            margin: 20px 0;
            padding: 20px;
            background: #f8fafc;
            border-radius: 12px;
        }
        .icon-demo {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 10px;
        }
        .icon-circle {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
        }
        .old-icon {
            background: linear-gradient(135deg, #ef4444, #dc2626);
        }
        .new-icon {
            background: linear-gradient(135deg, #22c55e, #16a34a);
        }
        .vs-text {
            font-size: 24px;
            font-weight: bold;
            color: #64748b;
        }
        .highlight-box {
            background: linear-gradient(135deg, #dbeafe, #bfdbfe);
            border: 2px solid #2563eb;
            border-radius: 12px;
            padding: 25px;
            margin: 25px 0;
            text-align: center;
        }
        .interactive-demo {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            padding: 20px;
            margin: 20px 0;
        }
        .demo-text {
            background: #f8f9fa;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            padding: 16px;
            font-family: 'SF Mono', Monaco, monospace;
            font-size: 14px;
            line-height: 1.5;
            margin: 15px 0;
            word-wrap: break-word;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔄 改进复原功能</h1>
        
        <div class="feature-section">
            <h2>🎯 双重改进</h2>
            <p><strong>1. 更换了更美观的复原图标<br>2. 修复了复原后文本变成一行的问题</strong></p>
        </div>

        <div class="problem-demo">
            <h3>❌ 之前的问题</h3>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                <div>
                    <h4>图标问题：</h4>
                    <ul>
                        <li>使用✓符号，不够直观</li>
                        <li>看起来像确认而不是恢复</li>
                        <li>视觉效果不够美观</li>
                    </ul>
                </div>
                <div>
                    <h4>文本格式问题：</h4>
                    <ul>
                        <li>使用textContent恢复内容</li>
                        <li>丢失了原有的HTML结构</li>
                        <li>文本变成一行，失去换行</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="solution-demo">
            <h3>✅ 现在的解决方案</h3>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                <div>
                    <h4>新图标设计：</h4>
                    <ul>
                        <li>使用撤销/恢复箭头图标</li>
                        <li>更直观地表示恢复操作</li>
                        <li>视觉效果更加美观</li>
                    </ul>
                </div>
                <div>
                    <h4>格式保持：</h4>
                    <ul>
                        <li>使用innerHTML保存和恢复</li>
                        <li>保持原有的HTML结构</li>
                        <li>文本换行格式完全保持</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="feature-section">
            <h2>🎨 图标对比</h2>
            <div class="icon-comparison">
                <div class="icon-demo">
                    <div class="icon-circle old-icon">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="white">
                            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                        </svg>
                    </div>
                    <span>旧图标（✓）</span>
                    <span style="color: #ef4444; font-size: 12px;">看起来像确认</span>
                </div>
                
                <div class="vs-text">VS</div>
                
                <div class="icon-demo">
                    <div class="icon-circle new-icon">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="white">
                            <path d="M12.5 8c-2.65 0-5.05.99-6.9 2.6L2 7v9h9l-3.62-3.62c1.39-1.16 3.16-1.88 5.12-1.88 3.54 0 6.55 2.31 7.6 5.5l2.37-.78C21.08 11.03 17.15 8 12.5 8z"/>
                        </svg>
                    </div>
                    <span>新图标（↻）</span>
                    <span style="color: #22c55e; font-size: 12px;">清晰表示恢复</span>
                </div>
            </div>
        </div>

        <div class="demo-container">
            <!-- 文本换行问题演示 -->
            <div class="demo-block">
                <h3>❌ 旧版本问题</h3>
                <div class="code-block one-line">
Lush oasis in vast golden desert, palm trees surrounding crystal clear water, Bedouin tents with intricate patterns, camels resting in shade, warm golden lighting, detailed textures
                </div>
                <p style="font-size: 12px; color: #ef4444; margin-top: 20px;">
                    ↑ 使用textContent恢复，文本变成一行
                </p>
            </div>

            <!-- 正确的文本换行 -->
            <div class="demo-block">
                <h3>✅ 新版本修复</h3>
                <div class="code-block">
Lush oasis in vast golden desert, palm trees surrounding crystal clear water, Bedouin tents with intricate patterns, camels resting in shade, warm golden lighting, detailed textures
                </div>
                <div class="button-demo with-restore">
                    <div class="demo-button mj-button">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="white">
                            <path d="M3,21V3H7L12,14L17,3H21V21H17V8L13,18H11L7,8V21H3Z"/>
                        </svg>
                    </div>
                    <div class="demo-button restore-button">
                        <svg width="18" height="18" viewBox="0 0 24 24" fill="white">
                            <path d="M12.5 8c-2.65 0-5.05.99-6.9 2.6L2 7v9h9l-3.62-3.62c1.39-1.16 3.16-1.88 5.12-1.88 3.54 0 6.55 2.31 7.6 5.5l2.37-.78C21.08 11.03 17.15 8 12.5 8z"/>
                        </svg>
                    </div>
                </div>
                <p style="font-size: 12px; color: #22c55e; margin-top: 50px;">
                    ↑ 使用innerHTML恢复，保持原有格式
                </p>
            </div>
        </div>

        <div class="feature-section">
            <h2>🔧 技术实现</h2>
            <table class="comparison-table">
                <thead>
                    <tr>
                        <th>方面</th>
                        <th>旧实现</th>
                        <th>新实现</th>
                        <th>优势</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><strong>图标设计</strong></td>
                        <td class="problem">✓ 符号（确认感）</td>
                        <td class="solution">↻ 箭头（恢复感）</td>
                        <td>更直观地表示恢复操作</td>
                    </tr>
                    <tr>
                        <td><strong>内容保存</strong></td>
                        <td class="problem">promptText（纯文本）</td>
                        <td class="solution">innerHTML（HTML结构）</td>
                        <td>保持原有格式和换行</td>
                    </tr>
                    <tr>
                        <td><strong>内容检测</strong></td>
                        <td class="problem">textContent比较</td>
                        <td class="solution">innerHTML比较</td>
                        <td>准确检测HTML结构变化</td>
                    </tr>
                    <tr>
                        <td><strong>内容恢复</strong></td>
                        <td class="problem">textContent赋值</td>
                        <td class="solution">innerHTML赋值</td>
                        <td>完整恢复原始格式</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="interactive-demo">
            <h3>🎮 格式保持演示</h3>
            <p>下面展示了不同恢复方式的效果：</p>
            
            <h4>原始内容（有换行）：</h4>
            <div class="demo-text">
Intricate mechanical butterfly with clockwork wings, brass and copper gears visible, perched on a vintage pocket watch, macro photography, steampunk aesthetic, warm golden lighting, detailed textures
            </div>
            
            <h4>使用textContent恢复（❌ 问题）：</h4>
            <div class="demo-text" style="white-space: nowrap; overflow: hidden; text-overflow: ellipsis; background: #fee2e2;">
Intricate mechanical butterfly with clockwork wings, brass and copper gears visible, perched on a vintage pocket watch, macro photography, steampunk aesthetic, warm golden lighting, detailed textures
            </div>
            
            <h4>使用innerHTML恢复（✅ 正确）：</h4>
            <div class="demo-text" style="background: #dcfce7;">
Intricate mechanical butterfly with clockwork wings, brass and copper gears visible, perched on a vintage pocket watch, macro photography, steampunk aesthetic, warm golden lighting, detailed textures
            </div>
        </div>

        <div class="highlight-box">
            <h2>✨ 核心改进</h2>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-top: 20px;">
                <div>
                    <h4>🎨 更美观的图标</h4>
                    <p>撤销箭头图标更直观地表示恢复操作</p>
                </div>
                <div>
                    <h4>📝 格式完整保持</h4>
                    <p>使用innerHTML保持原有的文本换行格式</p>
                </div>
                <div>
                    <h4>🔍 精确检测</h4>
                    <p>比较HTML结构，准确检测内容变化</p>
                </div>
                <div>
                    <h4>🔄 完美恢复</h4>
                    <p>恢复后的内容与原始内容完全一致</p>
                </div>
            </div>
        </div>

        <div class="feature-section">
            <h2>📝 代码关键点</h2>
            <div style="background: #f8fafc; padding: 20px; border-radius: 8px; font-family: monospace; font-size: 14px;">
                <h4>1. 新图标SVG：</h4>
                <code>
                &lt;path d="M12.5 8c-2.65 0-5.05.99-6.9 2.6L2 7v9h9l-3.62-3.62c1.39-1.16 3.16-1.88 5.12-1.88 3.54 0 6.55 2.31 7.6 5.5l2.37-.78C21.08 11.03 17.15 8 12.5 8z"/&gt;
                </code>
                
                <h4 style="margin-top: 20px;">2. 保存HTML结构：</h4>
                <code>
                promptElement.dataset.originalContent = promptElement.innerHTML;
                </code>
                
                <h4 style="margin-top: 20px;">3. 检测HTML变化：</h4>
                <code>
                const currentContent = promptElement.innerHTML;<br>
                const originalContent = promptElement.dataset.originalContent;
                </code>
                
                <h4 style="margin-top: 20px;">4. 恢复HTML结构：</h4>
                <code>
                promptElement.innerHTML = originalContent;
                </code>
            </div>
        </div>

        <div class="feature-section">
            <h2>🎊 最终效果</h2>
            <p style="font-size: 18px; line-height: 1.8;">
                现在的复原功能完美解决了所有问题：
            </p>
            <ul style="font-size: 16px; line-height: 1.8;">
                <li>✅ <strong>更美观的图标：</strong>撤销箭头清晰表示恢复操作</li>
                <li>✅ <strong>格式完整保持：</strong>文本换行和格式完全保持</li>
                <li>✅ <strong>精确内容检测：</strong>准确检测HTML结构变化</li>
                <li>✅ <strong>完美恢复体验：</strong>恢复后与原始内容完全一致</li>
                <li>✅ <strong>视觉体验提升：</strong>图标和功能都更加专业</li>
            </ul>
            
            <p style="background: rgba(37, 99, 235, 0.1); padding: 15px; border-radius: 8px; margin-top: 20px;">
                <strong>💡 完美升级：</strong>复原功能现在既美观又实用！新的撤销图标更直观，文本格式保持功能确保恢复后的内容与原始内容完全一致。
            </p>
        </div>
    </div>
</body>
</html>
