<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chrome双配置文件快速设置</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .title {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 3px solid #4CAF50;
            padding-bottom: 15px;
        }
        .step {
            margin: 25px 0;
            padding: 20px;
            border-left: 4px solid #2196F3;
            background-color: #f8f9fa;
            border-radius: 5px;
        }
        .step-title {
            font-weight: bold;
            color: #2196F3;
            font-size: 18px;
            margin-bottom: 10px;
        }
        .step-content {
            line-height: 1.6;
        }
        .profile-box {
            display: inline-block;
            width: 45%;
            margin: 10px 2%;
            padding: 15px;
            border: 2px solid #ddd;
            border-radius: 8px;
            text-align: center;
            vertical-align: top;
        }
        .profile-a {
            border-color: #4CAF50;
            background-color: #e8f5e8;
        }
        .profile-b {
            border-color: #FF9800;
            background-color: #fff3e0;
        }
        .button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
            text-decoration: none;
            display: inline-block;
        }
        .button:hover {
            background-color: #45a049;
        }
        .button-orange {
            background-color: #FF9800;
        }
        .button-orange:hover {
            background-color: #e68900;
        }
        .warning {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
        .code {
            background-color: #f4f4f4;
            padding: 10px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">Chrome双配置文件快速设置指南</h1>
        
        <div class="warning">
            <strong>⚠️ 重要提示：</strong>这个设置将帮你创建两个独立的Chrome配置文件，分别用于Claude和Midjourney，实现账号完全隔离。
        </div>

        <div class="step">
            <div class="step-title">第1步：创建两个Chrome用户配置文件</div>
            <div class="step-content">
                <div class="profile-box profile-a">
                    <h3>配置文件A - Claude专用</h3>
                    <p>用于登录Claude账号和提取prompts</p>
                    <button class="button" onclick="openChromeProfile('claude')">创建Claude配置文件</button>
                </div>
                
                <div class="profile-box profile-b">
                    <h3>配置文件B - Midjourney专用</h3>
                    <p>用于登录Midjourney账号和发送prompts</p>
                    <button class="button button-orange" onclick="openChromeProfile('midjourney')">创建Midjourney配置文件</button>
                </div>
                
                <div class="code">
                    <strong>手动创建步骤：</strong><br>
                    1. 点击Chrome右上角的用户头像<br>
                    2. 选择"添加"<br>
                    3. 设置配置文件名称<br>
                    4. 完成设置
                </div>
            </div>
        </div>

        <div class="step">
            <div class="step-title">第2步：在两个配置文件中分别安装插件</div>
            <div class="step-content">
                <p><strong>在每个配置文件中都要执行以下步骤：</strong></p>
                <ol>
                    <li>进入 <code>chrome://extensions/</code></li>
                    <li>开启"开发者模式"</li>
                    <li>点击"加载已解压的扩展程序"</li>
                    <li>选择插件文件夹</li>
                </ol>
                
                <button class="button" onclick="openExtensionsPage()">打开扩展程序页面</button>
            </div>
        </div>

        <div class="step">
            <div class="step-title">第3步：登录相应账号</div>
            <div class="step-content">
                <div class="profile-box profile-a">
                    <h3>在Claude配置文件中</h3>
                    <p>登录你的Claude账号</p>
                    <button class="button" onclick="openClaude()">打开Claude.ai</button>
                </div>
                
                <div class="profile-box profile-b">
                    <h3>在Midjourney配置文件中</h3>
                    <p>登录你的Midjourney账号</p>
                    <button class="button button-orange" onclick="openMidjourney()">打开Midjourney</button>
                </div>
            </div>
        </div>

        <div class="step">
            <div class="step-title">第4步：测试跨配置文件通信</div>
            <div class="step-content">
                <p><strong>测试流程：</strong></p>
                <ol>
                    <li><strong>在Claude配置文件中：</strong>
                        <ul>
                            <li>生成一些prompts</li>
                            <li>点击插件图标</li>
                            <li>点击"提取Prompts"</li>
                            <li>点击"保存到共享存储"</li>
                        </ul>
                    </li>
                    <li><strong>切换到Midjourney配置文件：</strong>
                        <ul>
                            <li>打开Midjourney网页版</li>
                            <li>点击插件图标</li>
                            <li>点击"从共享存储加载"</li>
                            <li>选择prompts并发送</li>
                        </ul>
                    </li>
                </ol>
                
                <button class="button" onclick="openTestPage()">打开测试页面</button>
            </div>
        </div>

        <div class="success">
            <strong>✅ 设置完成后的优势：</strong><br>
            • 账号完全隔离，互不影响<br>
            • 自动化prompt传输<br>
            • 支持批量处理<br>
            • 数据本地存储，安全可靠
        </div>

        <div class="step">
            <div class="step-title">故障排除</div>
            <div class="step-content">
                <p><strong>常见问题解决：</strong></p>
                <ul>
                    <li><strong>无法创建配置文件：</strong>确保Chrome版本是最新的</li>
                    <li><strong>插件无法加载：</strong>检查文件路径和权限</li>
                    <li><strong>无法共享prompts：</strong>尝试使用文件下载/上传方式</li>
                    <li><strong>登录状态丢失：</strong>检查是否在正确的配置文件中</li>
                </ul>
            </div>
        </div>
    </div>

    <script>
        function openChromeProfile(type) {
            alert(`请手动创建${type === 'claude' ? 'Claude' : 'Midjourney'}配置文件：\n\n1. 点击Chrome右上角用户头像\n2. 选择"添加"\n3. 命名为"${type === 'claude' ? 'Claude工作' : 'Midjourney工作'}"\n4. 完成设置`);
        }

        function openExtensionsPage() {
            window.open('chrome://extensions/', '_blank');
        }

        function openClaude() {
            window.open('https://claude.ai', '_blank');
        }

        function openMidjourney() {
            window.open('https://www.midjourney.com/imagine', '_blank');
        }

        function openTestPage() {
            window.open('./test-page.html', '_blank');
        }
    </script>
</body>
</html>
