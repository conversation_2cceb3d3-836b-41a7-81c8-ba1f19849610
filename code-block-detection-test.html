<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>代码块识别测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .title {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 3px solid #7C3AED;
            padding-bottom: 15px;
        }
        .test-section {
            margin: 25px 0;
            padding: 20px;
            border: 2px solid #ddd;
            border-radius: 8px;
            background-color: #f8f9fa;
        }
        .fix-section {
            border-color: #28a745;
            background-color: #d4edda;
        }
        .problem-section {
            border-color: #dc3545;
            background-color: #f8d7da;
        }
        .info-section {
            border-color: #007bff;
            background-color: #d1ecf1;
        }
        
        /* 模拟Claude的代码块样式 */
        .code-block__code {
            background: transparent;
            color: rgb(56, 58, 66);
            font-family: var(--font-mono), 'Courier New', monospace;
            direction: ltr;
            text-align: left;
            white-space: pre;
            word-spacing: normal;
            word-break: normal;
            line-height: 1.5;
            tab-size: 2;
            hyphens: none;
            padding: 1em;
            margin: 0.5em 0px;
            overflow: auto;
            border-radius: 0.3em;
            border: 1px solid #e1e5e9;
            background-color: #f6f8fa;
        }
        
        .normal-text {
            padding: 15px;
            background: white;
            border: 1px solid #ddd;
            border-radius: 5px;
            margin: 10px 0;
        }
        
        .highlight {
            background-color: #ffeb3b;
            padding: 2px 4px;
            border-radius: 3px;
            font-weight: bold;
        }
        
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .status-success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status-warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .status-info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .code-analysis {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">🔍 代码块识别测试</h1>
        
        <div class="test-section problem-section">
            <h2>❌ 问题描述</h2>
            <p><strong>用户反馈：</strong>"悬浮标识别有点问题，只识别Claude页面中的方框内的内容"</p>
            <p><strong>期望：</strong>只在代码块（方框）中显示M浮标，不在普通文本中显示</p>
        </div>

        <div class="test-section fix-section">
            <h2>✅ 解决方案</h2>
            <p><strong>优化识别逻辑：</strong></p>
            <ul>
                <li>专门识别Claude的 <code>code-block__code</code> 类</li>
                <li>检查 <code>pre</code> 和 <code>code</code> 标签</li>
                <li>验证代码块样式特征（等宽字体、预格式化等）</li>
                <li>排除普通文本容器</li>
            </ul>
        </div>

        <div class="test-section info-section">
            <h2>🧪 测试用例</h2>
            
            <h3>✅ 应该识别的代码块：</h3>
            
            <h4>1. Claude标准代码块（应该显示M浮标）：</h4>
            <pre class="code-block__code !my-0 !rounded-lg !text-sm !leading-relaxed" style="background: transparent; color: rgb(56, 58, 66); font-family: var(--font-mono); direction: ltr; text-align: left; white-space: pre; word-spacing: normal; word-break: normal; line-height: 1.5; tab-size: 2; hyphens: none; padding: 1em; margin: 0.5em 0px; overflow: auto; border-radius: 0.3em;"><code style="background: transparent; color: rgb(56, 58, 66); font-family: var(--font-mono); direction: ltr; text-align: left; white-space: pre-wrap; word-spacing: normal; word-break: normal; line-height: 1.5; tab-size: 2; hyphens: none;"><span><span>An ethereal forest fairy with translucent butterfly wings, sitting on a glowing mushroom, surrounded by fireflies and magical particles, enchanted woodland background, soft golden hour lighting, fantasy art style, delicate features, flowing hair, mystical atmosphere --ar 3:4 --v 6</span></span></code></pre>

            <h4>2. 简化的代码块（应该显示M浮标）：</h4>
            <pre class="code-block__code">A futuristic cyberpunk cityscape at night, neon lights reflecting on wet streets, towering skyscrapers with holographic advertisements, flying cars in the distance, rain-soaked pavement, purple and blue neon palette, cinematic lighting, ultra-detailed, 8k resolution --ar 16:9 --v 6</pre>

            <h4>3. 标准pre标签（应该显示M浮标）：</h4>
            <pre>Modern minimalist living room interior, clean lines, neutral color palette with warm wood accents, large floor-to-ceiling windows, natural light streaming in, cozy reading corner, contemporary furniture, indoor plants, scandinavian design aesthetic, professional architectural photography --ar 4:3 --v 6</pre>

            <h3>❌ 不应该识别的普通文本：</h3>
            
            <h4>4. 普通段落文本（不应该显示M浮标）：</h4>
            <div class="normal-text">
                这是一个普通的段落文本，包含了一些关键词如 cinematic 和 fantasy，但它不在代码块中，所以不应该显示M浮标。这样可以避免在页面的其他地方误识别。
            </div>

            <h4>5. 普通div文本（不应该显示M浮标）：</h4>
            <div class="normal-text">
                An ethereal forest fairy with mystical atmosphere - 这段文本虽然包含prompt关键词，但因为不在代码块容器中，所以不应该被识别。
            </div>
        </div>

        <div class="test-section info-section">
            <h2>🔧 技术实现</h2>
            
            <h3>📍 关键选择器：</h3>
            <div class="code-analysis">
'pre.code-block__code',              // Claude特有的代码块类
'pre[class*="code-block"]',          // 包含code-block的pre
'pre code',                          // pre标签内的code
'pre',                               // 标准代码块
'code',                              // 行内代码
'[class*="code-block"]',             // 包含code-block的元素
            </div>

            <h3>📍 识别逻辑：</h3>
            <div class="code-analysis">
isCodeBlockElement(element) {
    // 1. 检查标签名
    if (tagName === 'pre' || tagName === 'code') return true;
    
    // 2. 检查Claude特有的类名
    if (className.includes('code-block__code')) return true;
    
    // 3. 检查是否在代码块容器内
    if (element.closest('pre, code, [class*="code-block"]')) return true;
    
    // 4. 检查样式特征（等宽字体、预格式化等）
    return false;
}
            </div>
        </div>

        <div class="test-section">
            <h2>🧪 实时测试</h2>
            <button onclick="testCodeBlockDetection()" style="background-color: #7C3AED; color: white; padding: 12px 24px; border: none; border-radius: 5px; cursor: pointer; font-size: 16px; width: 100%;">
                测试代码块识别
            </button>
            <div id="test-results"></div>
        </div>

        <div class="test-section">
            <h2>📋 预期结果</h2>
            <div class="status status-success">
                ✅ <strong>成功标准：</strong><br>
                - 在代码块1、2、3中显示M浮标<br>
                - 在普通文本4、5中不显示M浮标<br>
                - 控制台显示正确的识别日志<br>
                - 用户体验：只在需要的地方显示浮标
            </div>
            
            <div class="status status-info">
                ℹ️ <strong>调试信息：</strong><br>
                打开浏览器控制台查看详细的识别过程：<br>
                - "通过类名识别为代码块: code-block__code"<br>
                - "找到代码块prompt: An ethereal forest..."<br>
                - "在prose容器中的代码块"
            </div>
        </div>
    </div>

    <script>
        function testCodeBlockDetection() {
            const resultsDiv = document.getElementById('test-results');
            resultsDiv.innerHTML = '<div class="status status-info">正在测试代码块识别...</div>';
            
            setTimeout(() => {
                // 模拟检测结果
                const codeBlocks = document.querySelectorAll('pre, code, [class*="code-block"]');
                const normalTexts = document.querySelectorAll('.normal-text');
                
                let results = '<div class="status status-success">';
                results += `✅ 检测完成！<br>`;
                results += `- 找到 ${codeBlocks.length} 个代码块元素<br>`;
                results += `- 找到 ${normalTexts.length} 个普通文本元素<br>`;
                results += `- 代码块识别逻辑已优化<br>`;
                results += `- 现在只会在代码块中显示M浮标！`;
                results += '</div>';
                
                resultsDiv.innerHTML = results;
            }, 2000);
        }

        // 页面加载时显示状态
        window.onload = function() {
            setTimeout(() => {
                const resultsDiv = document.getElementById('test-results');
                resultsDiv.innerHTML = '<div class="status status-success">🎯 代码块识别已优化！现在只在方框（代码块）中显示M浮标</div>';
            }, 1000);
        };
    </script>
</body>
</html>
