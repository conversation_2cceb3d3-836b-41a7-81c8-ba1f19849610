<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>绿色勾号测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .title {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 3px solid #4CAF50;
            padding-bottom: 15px;
        }
        .fix-section {
            margin: 25px 0;
            padding: 20px;
            border: 2px solid #4CAF50;
            border-radius: 8px;
            background-color: #e8f5e8;
        }
        .test-section {
            margin: 25px 0;
            padding: 20px;
            border: 2px solid #17a2b8;
            border-radius: 8px;
            background-color: #d1ecf1;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .status-success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .code-fix {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            overflow-x: auto;
        }
        .test-block {
            margin: 15px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #f8f9fa;
            font-family: monospace;
            position: relative;
        }
        .step {
            background: #e9ecef;
            border-left: 4px solid #4CAF50;
            padding: 15px;
            margin: 10px 0;
        }
        .demo-button {
            position: relative;
            width: 32px;
            height: 32px;
            background: linear-gradient(135deg, #7C3AED, #3B82F6);
            border-radius: 50%;
            cursor: pointer;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
            z-index: 10000;
            transition: all 0.3s ease;
            opacity: 0.8;
            margin: 10px;
        }
        .demo-button:hover {
            opacity: 1;
            transform: scale(1.1);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">✅ 绿色勾号修复测试</h1>
        
        <div class="fix-section">
            <h2>🔧 修复内容</h2>
            <div class="status status-success">
                <strong>已修复的问题：</strong><br>
                1. ✅ 确保 background.js 返回正确的 success 状态<br>
                2. ✅ 添加详细的调试日志<br>
                3. ✅ 验证 showSuccess 方法正确执行<br>
                4. ✅ 确保绿色勾号和通知都正常显示
            </div>
            
            <h3>🛠️ 技术修复：</h3>
            <div class="code-fix">
// background.js 修复
return { 
    success: true, 
    message: '已后台发送到Midjourney', 
    originalResponse: response 
};

// claude-extractor.js 增强调试
if (response && response.success) {
    console.log('✅ 检测到成功响应，显示绿色勾号');
    this.showSuccess(buttonElement);
    console.log('prompt发送成功');
} else {
    console.log('❌ 响应不成功:', { 
        response, 
        hasResponse: !!response, 
        hasSuccess: response?.success 
    });
}
            </div>
        </div>

        <div class="test-section">
            <h2>🧪 测试代码块</h2>
            <p>使用这些代码块测试绿色勾号功能：</p>
            
            <h4>测试代码块1：</h4>
            <pre class="test-block">Beautiful cherry blossom tree in full bloom, petals falling like snow, soft pink and white colors, peaceful Japanese garden setting, spring atmosphere --ar 16:9</pre>

            <h4>测试代码块2：</h4>
            <pre class="test-block">Majestic lighthouse on rocky cliff during storm, dramatic waves crashing, lightning illuminating the scene, dark stormy sky, cinematic lighting --ar 9:16</pre>

            <h4>演示按钮（点击测试）：</h4>
            <div style="text-align: center; margin: 20px 0;">
                <div class="demo-button" onclick="testSuccess(this)" title="点击测试绿色勾号">
                    <svg width="18" height="18" viewBox="0 0 24 24" fill="white">
                        <path d="M3,21V3H7L12,14L17,3H21V21H17V8L13,18H11L7,8V21H3Z" stroke="white" stroke-width="0.5"/>
                    </svg>
                </div>
                <p>点击上面的M按钮测试绿色勾号效果</p>
            </div>
        </div>

        <div class="test-section">
            <h2>🔍 测试步骤</h2>
            
            <div class="step">
                <h4>步骤1：重新加载插件</h4>
                <p>在Chrome扩展页面刷新插件，确保修复生效</p>
            </div>

            <div class="step">
                <h4>步骤2：准备环境</h4>
                <ol>
                    <li>打开Midjourney页面（www.midjourney.com/imagine）</li>
                    <li>确保Claude页面有代码块显示M按钮</li>
                    <li>打开Chrome开发者工具的控制台</li>
                </ol>
            </div>

            <div class="step">
                <h4>步骤3：测试绿色勾号</h4>
                <ol>
                    <li>点击Claude页面的M按钮</li>
                    <li>观察按钮是否变成绿色背景</li>
                    <li>检查是否显示绿色勾号 ✓</li>
                    <li>查看是否出现绿色通知</li>
                    <li>3秒后按钮应该恢复原状</li>
                </ol>
            </div>

            <div class="step">
                <h4>步骤4：检查控制台</h4>
                <p>应该看到以下日志：</p>
                <div class="code-fix">
✅ 正常日志：
- "后台发送prompt到标签页: XXX ..."
- "Prompt后台发送成功，保持当前页面"
- "发送响应: { success: true, message: '已后台发送到Midjourney' }"
- "✅ 检测到成功响应，显示绿色勾号"
- "prompt发送成功"

❌ 如果看到这些，说明有问题：
- "❌ 响应不成功: ..."
- "发送失败: ..."
- 没有绿色勾号相关的日志
                </div>
            </div>
        </div>

        <div class="fix-section">
            <h2>🎯 预期效果</h2>
            <div class="status status-success">
                <strong>成功发送时应该看到：</strong><br>
                1. ✅ M按钮变成绿色背景<br>
                2. ✅ 显示白色勾号图标 ✓<br>
                3. ✅ 绿色通知"已后台发送到Midjourney"<br>
                4. ✅ 鼠标悬停显示"已后台发送到Midjourney！"<br>
                5. ✅ 3秒后按钮恢复紫色M图标<br>
                6. ✅ 控制台显示成功日志
            </div>
        </div>

        <div class="test-section">
            <h2>🚀 故障排除</h2>
            <p>如果绿色勾号仍然不显示：</p>
            <ol>
                <li><strong>检查控制台错误</strong> - 查看是否有JavaScript错误</li>
                <li><strong>验证响应格式</strong> - 确认 response.success 为 true</li>
                <li><strong>测试演示按钮</strong> - 点击上面的演示按钮看效果</li>
                <li><strong>重启Chrome</strong> - 有时需要重启浏览器</li>
                <li><strong>检查Midjourney页面</strong> - 确保页面正常加载</li>
            </ol>
        </div>
    </div>

    <script>
        function testSuccess(buttonElement) {
            console.log('🧪 测试绿色勾号效果');
            
            // 模拟 showSuccess 方法
            buttonElement.style.background = 'linear-gradient(135deg, #4CAF50, #388E3C)';
            buttonElement.innerHTML = `
                <svg width="16" height="16" viewBox="0 0 24 24" fill="white">
                    <path d="M9,20.42L2.79,14.21L5.62,11.38L9,14.77L18.88,4.88L21.71,7.71L9,20.42Z"/>
                </svg>
            `;
            buttonElement.title = '已后台发送到Midjourney！';
            
            // 显示通知
            showTestNotification();
            
            // 3秒后恢复
            setTimeout(() => {
                buttonElement.style.background = 'linear-gradient(135deg, #7C3AED, #3B82F6)';
                buttonElement.innerHTML = `
                    <svg width="18" height="18" viewBox="0 0 24 24" fill="white">
                        <path d="M3,21V3H7L12,14L17,3H21V21H17V8L13,18H11L7,8V21H3Z" stroke="white" stroke-width="0.5"/>
                    </svg>
                `;
                buttonElement.title = '发送到Midjourney';
            }, 3000);
        }
        
        function showTestNotification() {
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: linear-gradient(135deg, #4CAF50, #388E3C);
                color: white;
                padding: 12px 20px;
                border-radius: 8px;
                z-index: 10000;
                font-family: Arial, sans-serif;
                font-size: 14px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.2);
            `;
            
            notification.innerHTML = `
                <div style="display: flex; align-items: center; gap: 8px;">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="white">
                        <path d="M9,20.42L2.79,14.21L5.62,11.38L9,14.77L18.88,4.88L21.71,7.71L9,20.42Z"/>
                    </svg>
                    <span>已后台发送到Midjourney</span>
                </div>
            `;
            
            document.body.appendChild(notification);
            
            setTimeout(() => {
                notification.remove();
            }, 3000);
        }

        // 页面加载时显示状态
        window.onload = function() {
            setTimeout(() => {
                console.log('✅ 绿色勾号测试页面已加载');
                console.log('现在M按钮应该能正确显示绿色勾号了！');
            }, 1000);
        };
    </script>
</body>
</html>
