# 安装指南

## 快速安装

### 1. 准备文件
确保你有以下文件：
- `manifest.json`
- `popup.html`
- `popup.js`
- `claude-extractor.js`
- `midjourney-sender.js`
- `background.js`
- `icons/` 文件夹（可选）

### 2. 安装到Chrome

1. **打开Chrome扩展程序页面**
   - 在地址栏输入：`chrome://extensions/`
   - 或者：菜单 → 更多工具 → 扩展程序

2. **启用开发者模式**
   - 在页面右上角找到"开发者模式"开关
   - 点击开启

3. **加载插件**
   - 点击"加载已解压的扩展程序"按钮
   - 选择包含所有插件文件的文件夹
   - 点击"选择文件夹"

4. **确认安装**
   - 插件应该出现在扩展程序列表中
   - 状态显示为"已启用"

### 3. 验证安装

1. **检查插件图标**
   - 在Chrome工具栏中应该看到插件图标
   - 如果没有，点击拼图图标查看所有扩展

2. **测试基本功能**
   - 打开 `test-page.html` 文件
   - 点击插件图标
   - 应该看到弹窗界面

## 使用前准备

### 1. 权限确认
插件需要以下权限：
- 访问 claude.ai
- 访问 discord.com
- 本地存储权限

### 2. 浏览器要求
- Chrome 88+ (支持 Manifest V3)
- 已登录Claude和Discord账户

## 测试步骤

### 1. 测试prompt提取
1. 打开 `test-page.html`
2. 点击插件图标
3. 点击"提取Prompts"
4. 应该看到5个prompts被识别

### 2. 测试Midjourney发送
1. 打开Discord并进入Midjourney频道
2. 回到插件弹窗
3. 选择要发送的prompts
4. 点击"发送到Midjourney"
5. 观察Discord中是否自动输入命令

## 常见问题

### Q: 插件无法加载
**A:** 检查以下项目：
- 确保所有文件都在同一个文件夹中
- 检查 `manifest.json` 语法是否正确
- 确保开启了开发者模式

### Q: 无法提取prompts
**A:** 可能的原因：
- 页面未完全加载，等待几秒后重试
- prompts格式不符合识别规则
- 需要刷新页面

### Q: 无法发送到Midjourney
**A:** 检查以下项目：
- 确保在正确的Discord频道中
- 检查Discord输入框是否可用
- 确保已登录Discord账户

### Q: 插件图标不显示
**A:** 解决方法：
- 点击Chrome工具栏的拼图图标
- 找到插件并点击固定图标
- 或者创建PNG格式的图标文件

## 高级配置

### 自定义prompt识别规则
编辑 `claude-extractor.js` 文件中的 `isPromptText` 函数：

```javascript
isPromptText(text) {
    const promptIndicators = [
        'CINEMATIC STILL',
        'FILM BY',
        // 添加你的关键词
        'YOUR_KEYWORD'
    ];
    // 修改识别逻辑
}
```

### 调整发送间隔
编辑 `midjourney-sender.js` 文件中的延迟设置：

```javascript
// 修改这个值来调整发送间隔（毫秒）
await this.delay(2000); // 2秒间隔
```

## 卸载插件

1. 进入 `chrome://extensions/`
2. 找到插件
3. 点击"移除"按钮
4. 确认删除

## 更新插件

1. 修改代码文件
2. 在扩展程序页面点击刷新图标
3. 或者重新加载整个插件

## 技术支持

如果遇到问题：
1. 检查浏览器控制台错误信息
2. 查看插件的后台页面日志
3. 确保网络连接正常
4. 尝试重新安装插件
