# 跨Chrome配置文件使用指南

## 概述

这个增强版插件支持在两个不同的Chrome用户配置文件之间共享prompts：
- **配置文件A**: 登录Claude账号，用于提取prompts
- **配置文件B**: 登录Midjourney账号，用于发送prompts

## 设置步骤

### 1. 创建两个Chrome用户配置文件

#### 创建配置文件A (<PERSON>专用)
1. 打开Chrome
2. 点击右上角的用户头像
3. 选择"添加"创建新的配置文件
4. 命名为"Claude工作"
5. 登录你的Claude账号

#### 创建配置文件B (Midjourney专用)
1. 重复上述步骤
2. 命名为"Midjourney工作"
3. 登录你的Midjourney账号

### 2. 在两个配置文件中安装插件

#### 在配置文件A中安装
1. 切换到"Claude工作"配置文件
2. 进入 `chrome://extensions/`
3. 开启开发者模式
4. 加载插件文件夹

#### 在配置文件B中安装
1. 切换到"Midjourney工作"配置文件
2. 重复相同的安装步骤

## 使用流程

### 第一步：在配置文件A中提取prompts
1. **切换到"Claude工作"配置文件**
2. **打开Claude.ai并生成prompts**
3. **点击插件图标**
4. **点击"提取Prompts"**
5. **点击"保存到共享存储"** ⭐ 关键步骤
6. 看到"已保存到共享存储"的提示

### 第二步：在配置文件B中使用prompts
1. **切换到"Midjourney工作"配置文件**
2. **打开 https://www.midjourney.com/imagine**
3. **点击插件图标**
4. **点击"从共享存储加载"** ⭐ 关键步骤
5. **选择要发送的prompts**
6. **点击"发送到Midjourney"**

## 共享存储方式

插件提供三种共享存储方式：

### 方式1：浏览器本地存储 (推荐)
- **优点**: 简单快速，无需网络
- **缺点**: 仅限同一台电脑
- **使用**: 自动启用，无需配置

### 方式2：文件存储
- **优点**: 可以手动备份和分享
- **缺点**: 需要用户手动选择文件位置
- **使用**: 
  1. 首次保存时会提示选择文件位置
  2. 建议保存到桌面或文档文件夹
  3. 加载时需要选择相同的文件

### 方式3：云存储 (可选)
- **优点**: 可以跨设备使用
- **缺点**: 需要网络连接和API配置
- **配置**: 需要在 `shared-storage.js` 中配置API密钥

## 故障排除

### Q: 无法在配置文件B中看到prompts
**解决方案**:
1. 确保在配置文件A中点击了"保存到共享存储"
2. 在配置文件B中点击"从共享存储加载"
3. 检查两个配置文件都安装了相同版本的插件

### Q: 共享存储显示失败
**解决方案**:
1. 尝试使用文件存储方式
2. 检查浏览器是否支持localStorage
3. 确保有足够的磁盘空间

### Q: 插件在某个配置文件中不工作
**解决方案**:
1. 检查插件是否在该配置文件中正确安装
2. 刷新插件或重新加载
3. 检查网站权限是否正确设置

## 高级技巧

### 1. 自动同步
- 插件会每5秒检查一次共享存储更新
- 当检测到新prompts时会显示提示

### 2. 批量操作
- 可以一次性保存多个prompts
- 支持选择性发送特定prompts

### 3. 备份管理
- 定期将prompts保存为文件进行备份
- 可以在不同设备间手动传输文件

## 安全注意事项

1. **账号隔离**: 两个配置文件完全独立，不会互相影响登录状态
2. **数据安全**: 共享存储仅在本地，不会上传到外部服务器
3. **权限控制**: 插件仅访问必要的网站和功能

## 性能优化

1. **及时清理**: 定期清理不需要的prompts
2. **选择性同步**: 只保存和传输需要的prompts
3. **关闭不用的配置文件**: 减少系统资源占用

## 示例工作流程

```
配置文件A (Claude)          共享存储              配置文件B (Midjourney)
     ↓                        ↓                        ↓
1. 生成prompts          →  保存prompts         ←  加载prompts
2. 提取prompts          →  localStorage        ←  读取数据
3. 保存到共享存储        →  文件/云存储         ←  发送到MJ
```

这种设置方式可以完美解决账号隔离问题，让你在不同的Chrome配置文件中安全地使用不同的账号，同时实现prompts的无缝传输！
