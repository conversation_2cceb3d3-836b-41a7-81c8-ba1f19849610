// simple-cross-profile.js - 简化的跨配置文件通信

class SimpleCrossProfile {
    constructor() {
        this.storageKey = 'claude_mj_shared_prompts';
        this.init();
    }

    init() {
        // 使用多种方式确保跨配置文件通信
        this.setupLocalStorage();
        this.setupFileStorage();
    }

    setupLocalStorage() {
        // 监听localStorage变化
        window.addEventListener('storage', (event) => {
            if (event.key === this.storageKey) {
                console.log('检测到共享prompts更新');
                this.notifyUpdate();
            }
        });
    }

    setupFileStorage() {
        // 设置文件存储路径
        this.fileName = 'claude_midjourney_prompts.json';
    }

    // 保存prompts (在Claude配置文件中使用)
    async savePrompts(prompts) {
        const data = {
            prompts: prompts,
            timestamp: Date.now(),
            source: 'claude_profile'
        };

        try {
            // 方法1: localStorage
            localStorage.setItem(this.storageKey, JSON.stringify(data));
            console.log('已保存到localStorage');

            // 方法2: 下载JSON文件
            this.downloadAsFile(data);
            
            return { success: true, message: '已保存到共享存储' };
        } catch (error) {
            console.error('保存失败:', error);
            return { success: false, error: error.message };
        }
    }

    // 加载prompts (在Midjourney配置文件中使用)
    async loadPrompts() {
        try {
            // 方法1: 从localStorage加载
            const localData = localStorage.getItem(this.storageKey);
            if (localData) {
                const data = JSON.parse(localData);
                console.log('从localStorage加载了prompts');
                return { success: true, prompts: data.prompts, source: 'localStorage' };
            }

            // 方法2: 提示用户选择文件
            const fileData = await this.loadFromFile();
            if (fileData) {
                console.log('从文件加载了prompts');
                return { success: true, prompts: fileData.prompts, source: 'file' };
            }

            return { success: false, message: '没有找到共享的prompts' };
        } catch (error) {
            console.error('加载失败:', error);
            return { success: false, error: error.message };
        }
    }

    // 下载为文件
    downloadAsFile(data) {
        const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        
        const a = document.createElement('a');
        a.href = url;
        a.download = this.fileName;
        a.style.display = 'none';
        
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        
        URL.revokeObjectURL(url);
        console.log('已下载prompts文件');
    }

    // 从文件加载
    async loadFromFile() {
        return new Promise((resolve) => {
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = '.json';
            input.style.display = 'none';
            
            input.onchange = (event) => {
                const file = event.target.files[0];
                if (file) {
                    const reader = new FileReader();
                    reader.onload = (e) => {
                        try {
                            const data = JSON.parse(e.target.result);
                            resolve(data);
                        } catch (error) {
                            console.error('文件格式错误:', error);
                            resolve(null);
                        }
                    };
                    reader.readAsText(file);
                } else {
                    resolve(null);
                }
                document.body.removeChild(input);
            };
            
            document.body.appendChild(input);
            input.click();
        });
    }

    // 通知更新
    notifyUpdate() {
        // 可以在这里添加UI通知
        if (window.popupManager) {
            window.popupManager.showStatus('检测到新的共享prompts', 'info');
        }
    }

    // 清除共享数据
    clearSharedData() {
        localStorage.removeItem(this.storageKey);
        console.log('已清除共享数据');
    }
}

// 全局实例
window.SimpleCrossProfile = SimpleCrossProfile;
