<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <style>
        body {
            width: 350px;
            padding: 20px;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .header {
            text-align: center;
            margin-bottom: 20px;
        }
        .title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }
        .subtitle {
            font-size: 12px;
            color: #666;
        }
        .section {
            margin-bottom: 15px;
            padding: 15px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            background-color: #f9f9f9;
        }
        .section-title {
            font-weight: bold;
            margin-bottom: 10px;
            color: #444;
        }
        .prompt-item {
            background: white;
            padding: 10px;
            margin: 5px 0;
            border-radius: 5px;
            border-left: 3px solid #4CAF50;
            font-size: 12px;
            max-height: 60px;
            overflow: hidden;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .prompt-item:hover {
            background-color: #f0f8ff;
            transform: translateY(-1px);
        }
        .prompt-item.selected {
            border-left-color: #2196F3;
            background-color: #e3f2fd;
        }
        .button {
            width: 100%;
            padding: 10px;
            margin: 5px 0;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.3s;
        }
        .button-primary {
            background-color: #4CAF50;
            color: white;
        }
        .button-primary:hover {
            background-color: #45a049;
        }
        .button-secondary {
            background-color: #2196F3;
            color: white;
        }
        .button-secondary:hover {
            background-color: #1976D2;
        }
        .status {
            text-align: center;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-size: 12px;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .empty-state {
            text-align: center;
            color: #666;
            font-style: italic;
            padding: 20px;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="title">Claude to Midjourney</div>
        <div class="subtitle">自动提取并发送prompts</div>
    </div>

    <div class="section">
        <div class="section-title">检测到的Prompts</div>
        <div id="prompts-container">
            <div class="empty-state">请在Claude页面中查找prompts...</div>
        </div>
        <button id="extract-prompts" class="button button-primary">提取Prompts</button>
    </div>

    <div class="section">
        <div class="section-title">发送到Midjourney</div>
        <button id="send-to-midjourney" class="button button-secondary" disabled>发送选中的Prompts</button>
        <button id="send-all" class="button button-secondary" disabled>发送所有Prompts</button>
    </div>

    <div id="status" class="status" style="display: none;"></div>

    <script src="simple-storage.js"></script>
    <script src="popup.js"></script>
</body>
</html>
