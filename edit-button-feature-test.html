<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>编辑按钮功能测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            min-height: 100vh;
        }
        .container {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.15);
            backdrop-filter: blur(10px);
        }
        h1 {
            color: #4a5568;
            text-align: center;
            margin-bottom: 40px;
            font-size: 3em;
            font-weight: 700;
            background: linear-gradient(135deg, #7C3AED, #10B981);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        .feature-section {
            background: linear-gradient(135deg, #f0fff4, #e6fffa);
            border: 2px solid #10B981;
            padding: 30px;
            margin: 30px 0;
            border-radius: 16px;
            position: relative;
            overflow: hidden;
        }
        .demo-section {
            background: linear-gradient(135deg, #fef7ff, #f3e8ff);
            border: 2px solid #7C3AED;
            padding: 30px;
            margin: 30px 0;
            border-radius: 16px;
        }
        .button-demo {
            display: flex;
            gap: 8px;
            align-items: center;
            margin: 20px 0;
        }
        .demo-button {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            opacity: 0.9;
        }
        .mj-button {
            background: linear-gradient(135deg, #7C3AED, #3B82F6);
            box-shadow: 0 3px 12px rgba(124, 58, 237, 0.4);
        }
        .edit-button {
            background: linear-gradient(135deg, #10B981, #059669);
            box-shadow: 0 3px 12px rgba(16, 185, 129, 0.4);
        }
        .edit-button.editing {
            background: linear-gradient(135deg, #F59E0B, #D97706);
            box-shadow: 0 3px 12px rgba(245, 158, 11, 0.4);
        }
        .demo-button:hover {
            opacity: 1;
            transform: scale(1.1);
        }
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 16px;
            font-family: 'SF Mono', Monaco, monospace;
            font-size: 14px;
            line-height: 1.5;
            margin: 15px 0;
            position: relative;
        }
        .code-block.editable {
            outline: 2px solid #10B981;
            background-color: rgba(16, 185, 129, 0.05);
            border: 1px solid rgba(16, 185, 129, 0.2);
        }
        .workflow-steps {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        .step {
            background: rgba(255,255,255,0.8);
            padding: 20px;
            border-radius: 12px;
            border-left: 4px solid #10B981;
            backdrop-filter: blur(10px);
        }
        .step-number {
            width: 30px;
            height: 30px;
            background: #10B981;
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 15px;
            font-weight: bold;
        }
        .notification-demo {
            background: #10B981;
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 13px;
            font-weight: 500;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            display: inline-block;
            margin: 10px;
            backdrop-filter: blur(10px);
        }
        .notification-demo.edit {
            background: #F59E0B;
        }
        .feature-list {
            list-style: none;
            padding: 0;
        }
        .feature-list li {
            padding: 12px 0;
            border-bottom: 1px solid rgba(16, 185, 129, 0.2);
            position: relative;
            padding-left: 40px;
        }
        .feature-list li:last-child {
            border-bottom: none;
        }
        .feature-list li::before {
            content: "✨";
            position: absolute;
            left: 0;
            font-size: 20px;
        }
        .tech-details {
            background: #1a202c;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 12px;
            overflow-x: auto;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>✏️ 编辑按钮功能</h1>
        
        <div class="feature-section">
            <h2>🎉 新功能：代码块编辑</h2>
            <p><strong>现在你可以直接编辑代码块内容，然后发送修改后的prompt！</strong></p>
            
            <div class="button-demo">
                <div class="demo-button mj-button">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="white">
                        <path d="M3,21V3H7L12,14L17,3H21V21H17V8L13,18H11L7,8V21H3Z"/>
                    </svg>
                </div>
                <span style="margin: 0 10px;">+</span>
                <div class="demo-button edit-button">
                    <svg width="18" height="18" viewBox="0 0 24 24" fill="white">
                        <path d="M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z"/>
                    </svg>
                </div>
                <span style="margin-left: 15px; font-weight: 600;">M按钮 + E按钮（编辑）</span>
            </div>
        </div>

        <div class="demo-section">
            <h2>🎮 使用演示</h2>
            
            <h3>📝 代码块示例：</h3>
            <div class="code-block">
Northern lights dancing over a snow-covered pine forest, green and purple aurora reflecting on frozen lake, winter night scene, crystalline snow details, magical atmosphere, long exposure photography style
            </div>
            
            <h3>✏️ 编辑模式：</h3>
            <div class="code-block editable">
Northern lights dancing over a snow-covered pine forest, green and purple aurora reflecting on frozen lake, winter night scene, crystalline snow details, magical atmosphere, long exposure photography style --ar 16:9 --v 6
            </div>
            <p style="font-size: 14px; color: #666; margin-top: 10px;">
                ↑ 编辑模式下的代码块（绿色边框，可直接编辑）
            </p>
        </div>

        <div class="feature-section">
            <h2>🔄 工作流程</h2>
            <div class="workflow-steps">
                <div class="step">
                    <div class="step-number">1</div>
                    <h3>点击E按钮</h3>
                    <p>激活编辑模式，代码块变为可编辑状态</p>
                    <div class="notification-demo edit">✏️ 编辑模式</div>
                </div>
                
                <div class="step">
                    <div class="step-number">2</div>
                    <h3>修改内容</h3>
                    <p>直接在代码块中修改prompt内容</p>
                    <p style="font-size: 12px; color: #666;">例如：添加参数、调整描述等</p>
                </div>
                
                <div class="step">
                    <div class="step-number">3</div>
                    <h3>保存修改</h3>
                    <p>再次点击E按钮保存修改</p>
                    <div class="notification-demo">💾 已保存修改</div>
                </div>
                
                <div class="step">
                    <div class="step-number">4</div>
                    <h3>发送到MJ</h3>
                    <p>点击M按钮发送修改后的内容</p>
                    <p style="font-size: 12px; color: #666;">如果没修改则发送原始内容</p>
                </div>
            </div>
        </div>

        <div class="feature-section">
            <h2>✨ 功能特点</h2>
            <ul class="feature-list">
                <li><strong>双按钮设计：</strong>M按钮发送，E按钮编辑，功能分离清晰</li>
                <li><strong>智能状态管理：</strong>自动保存原始内容，支持恢复</li>
                <li><strong>视觉反馈：</strong>编辑模式下绿色边框，按钮颜色变化</li>
                <li><strong>实时编辑：</strong>直接在代码块中修改，所见即所得</li>
                <li><strong>状态通知：</strong>编辑/保存状态的美观通知提示</li>
                <li><strong>发送当前内容：</strong>M按钮始终发送当前显示的内容</li>
            </ul>
        </div>

        <div class="demo-section">
            <h2>🔧 技术实现</h2>
            
            <h3>📍 按钮组结构：</h3>
            <div class="tech-details">
<code>// 创建按钮组
const buttonGroup = document.createElement('div');
buttonGroup.className = 'claude-mj-button-group';

// M按钮（发送）
const mjButton = document.createElement('div');
mjButton.style.background = 'linear-gradient(135deg, #7C3AED, #3B82F6)';

// E按钮（编辑）
const editButton = document.createElement('div');
editButton.style.background = 'linear-gradient(135deg, #10B981, #059669)';

buttonGroup.appendChild(mjButton);
buttonGroup.appendChild(editButton);</code>
            </div>

            <h3>🎨 编辑模式切换：</h3>
            <div class="tech-details">
<code>toggleEditMode(promptElement, editButton) {
    const isEditable = promptElement.contentEditable === 'true';
    
    if (isEditable) {
        // 保存并退出编辑模式
        promptElement.contentEditable = 'false';
        promptElement.style.outline = 'none';
        editButton.style.background = 'linear-gradient(135deg, #10B981, #059669)';
    } else {
        // 进入编辑模式
        promptElement.contentEditable = 'true';
        promptElement.style.outline = '2px solid #10B981';
        editButton.style.background = 'linear-gradient(135deg, #F59E0B, #D97706)';
        promptElement.focus();
    }
}</code>
            </div>

            <h3>📤 智能发送逻辑：</h3>
            <div class="tech-details">
<code>// M按钮点击事件
mjButton.addEventListener('click', async (e) => {
    e.stopPropagation();
    // 获取当前代码块的内容（可能已被修改）
    const currentText = promptElement.textContent || promptElement.innerText;
    await this.sendPromptToMidjourney(currentText, mjButton);
});</code>
            </div>
        </div>

        <div class="feature-section">
            <h2>📝 使用说明</h2>
            <ol style="font-size: 16px; line-height: 1.8;">
                <li><strong>按 <code>Ctrl+Shift+Z</code>：</strong>显示M和E按钮</li>
                <li><strong>点击E按钮：</strong>进入编辑模式（绿色边框）</li>
                <li><strong>修改内容：</strong>直接在代码块中编辑prompt</li>
                <li><strong>再次点击E：</strong>保存修改并退出编辑模式</li>
                <li><strong>点击M按钮：</strong>发送当前内容到Midjourney</li>
            </ol>
            
            <p style="background: rgba(124, 58, 237, 0.1); padding: 15px; border-radius: 8px; margin-top: 20px;">
                <strong>💡 提示：</strong>现在你可以快速调整prompt参数，添加风格描述，或修改任何内容，然后直接发送修改后的版本！
            </p>
        </div>
    </div>
</body>
</html>
