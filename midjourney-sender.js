// midjourney-sender.js - 在Midjourney网页版中发送prompts的content script

class MidjourneyPromptSender {
    constructor() {
        this.isProcessing = false;
        this.currentPromptIndex = 0;
        this.pendingPrompts = [];
        this.init();
    }

    init() {
        // 监听来自popup的消息
        chrome.runtime.onMessage.addListener((request, _sender, sendResponse) => {
            if (request.action === 'sendPrompts') {
                this.handleSendPrompts(request.prompts).then(result => {
                    sendResponse(result);
                });
                return true; // 保持消息通道开放
            } else if (request.action === 'sendSinglePrompt') {
                this.handleSendSinglePrompt(request.prompt).then(result => {
                    sendResponse(result);
                });
                return true; // 保持消息通道开放
            }
        });

        // 监听页面变化
        this.observePageChanges();
    }

    async handleSendPrompts(prompts) {
        try {
            if (this.isProcessing) {
                return { success: false, message: '正在处理中，请稍候' };
            }

            if (!this.isMidjourneyPage()) {
                return { success: false, message: '请确保在Midjourney网页版中' };
            }

            this.pendingPrompts = prompts;
            this.currentPromptIndex = 0;
            this.isProcessing = true;

            await this.sendPromptsSequentially();

            return { success: true, message: `成功发送 ${prompts.length} 个prompts` };
        } catch (error) {
            console.error('发送prompts失败:', error);
            this.isProcessing = false;
            return { success: false, message: '发送失败: ' + error.message };
        }
    }

    async handleSendSinglePrompt(prompt) {
        try {
            if (!this.isMidjourneyPage()) {
                return { success: false, message: '请确保在Midjourney网页版中' };
            }

            console.log('处理单个prompt发送:', prompt.substring(0, 100) + '...');

            // 直接发送单个prompt并自动执行
            await this.sendSinglePromptAndExecute(prompt);

            return { success: true, message: '成功发送并执行prompt' };
        } catch (error) {
            console.error('发送单个prompt失败:', error);
            return { success: false, message: '发送失败: ' + error.message };
        }
    }

    async sendPromptsSequentially() {
        for (let i = 0; i < this.pendingPrompts.length; i++) {
            const prompt = this.pendingPrompts[i];
            
            try {
                await this.sendSinglePrompt(prompt);
                console.log(`成功发送第 ${i + 1} 个prompt`);
                
                // 等待一段时间再发送下一个，避免被限制
                if (i < this.pendingPrompts.length - 1) {
                    await this.delay(2000); // 等待2秒
                }
            } catch (error) {
                console.error(`发送第 ${i + 1} 个prompt失败:`, error);
                // 继续发送下一个
            }
        }

        this.isProcessing = false;
        console.log('所有prompts发送完成');
    }

    async sendSinglePrompt(prompt) {
        return new Promise((resolve, reject) => {
            try {
                // 查找Midjourney网页版的输入框
                const inputBox = this.findInputBox();
                if (!inputBox) {
                    reject(new Error('未找到Midjourney输入框'));
                    return;
                }

                // 直接输入prompt，不需要/imagine命令
                const cleanPrompt = prompt.trim();

                // 清空输入框并输入prompt
                inputBox.focus();

                // 清空现有内容
                inputBox.value = '';
                inputBox.textContent = '';

                // 模拟用户输入
                this.simulateTyping(inputBox, cleanPrompt);

                // 等待输入完成
                setTimeout(() => {
                    // 尝试点击生成按钮或按回车
                    const generateButton = this.findGenerateButton();
                    if (generateButton && !generateButton.disabled) {
                        generateButton.click();
                    } else {
                        // 如果没有找到按钮，尝试按回车
                        const enterEvent = new KeyboardEvent('keydown', {
                            key: 'Enter',
                            code: 'Enter',
                            keyCode: 13,
                            which: 13,
                            bubbles: true
                        });
                        inputBox.dispatchEvent(enterEvent);
                    }
                    resolve();
                }, 1000);

            } catch (error) {
                reject(error);
            }
        });
    }

    async sendSinglePromptAndExecute(prompt) {
        return new Promise((resolve, reject) => {
            try {
                console.log('开始发送并执行prompt...');

                // 查找特定的输入框
                const inputBox = this.findSpecificInputBox();
                if (!inputBox) {
                    reject(new Error('未找到Midjourney输入框'));
                    return;
                }

                // 直接输入prompt
                const cleanPrompt = prompt.trim();
                console.log('输入prompt到textarea:', cleanPrompt.substring(0, 50) + '...');

                // 聚焦并清空输入框
                inputBox.focus();
                inputBox.value = '';

                // 输入新内容
                inputBox.value = cleanPrompt;

                // 触发输入事件
                const inputEvent = new Event('input', { bubbles: true, cancelable: true });
                inputBox.dispatchEvent(inputEvent);

                // 触发change事件
                const changeEvent = new Event('change', { bubbles: true, cancelable: true });
                inputBox.dispatchEvent(changeEvent);

                console.log('输入完成，准备按回车...');

                // 等待一下然后按回车
                setTimeout(() => {
                    // 确保输入框仍然聚焦
                    inputBox.focus();

                    // 模拟按下回车键
                    const enterEvent = new KeyboardEvent('keydown', {
                        key: 'Enter',
                        code: 'Enter',
                        keyCode: 13,
                        which: 13,
                        bubbles: true,
                        cancelable: true
                    });

                    inputBox.dispatchEvent(enterEvent);

                    // 也尝试keyup事件
                    const enterUpEvent = new KeyboardEvent('keyup', {
                        key: 'Enter',
                        code: 'Enter',
                        keyCode: 13,
                        which: 13,
                        bubbles: true,
                        cancelable: true
                    });

                    inputBox.dispatchEvent(enterUpEvent);

                    console.log('已发送回车键事件');
                    resolve();
                }, 500);

            } catch (error) {
                console.error('发送并执行prompt失败:', error);
                reject(error);
            }
        });
    }

    findSpecificInputBox() {
        // 根据你提供的具体HTML结构查找输入框
        const specificSelectors = [
            'textarea#desktop_input_bar',
            'textarea[placeholder="What will you imagine?"]',
            'textarea.w-full.ml-1.bg-transparent'
        ];

        for (const selector of specificSelectors) {
            const element = document.querySelector(selector);
            if (element && this.isVisible(element)) {
                console.log('找到特定输入框:', selector);
                return element;
            }
        }

        console.log('未找到特定输入框，使用通用方法');
        return this.findInputBox();
    }

    findInputBox() {
        // 根据实际页面结构查找Midjourney输入框
        const selectors = [
            // 主要的prompt输入框
            'textarea[placeholder="What will you imagine?"]',
            'textarea.w-full.ml-1.bg-transparent',
            'textarea[placeholder*="What will you imagine"]',
            'textarea[placeholder*="imagine"]',
            // 备用选择器
            'input[placeholder*="What will you imagine"]',
            'input[placeholder*="imagine"]',
            '[data-testid="prompt-input"]',
            '[role="textbox"]',
            'div[contenteditable="true"]'
        ];

        for (const selector of selectors) {
            const element = document.querySelector(selector);
            if (element && this.isVisible(element)) {
                console.log('找到Midjourney输入框:', selector);
                return element;
            }
        }

        console.log('未找到Midjourney输入框');
        return null;
    }

    findGenerateButton() {
        // Midjourney网页版的生成按钮选择器
        const selectors = [
            'button[type="submit"]',
            'button:contains("Generate")',
            'button:contains("Imagine")',
            '[data-testid="generate-button"]',
            '[data-testid="submit-button"]',
            '.generate-button',
            '.submit-button',
            'button[aria-label*="generate"]',
            'button[aria-label*="submit"]'
        ];

        for (const selector of selectors) {
            const element = document.querySelector(selector);
            if (element && this.isVisible(element)) {
                return element;
            }
        }

        // 如果没有找到特定按钮，查找表单中的提交按钮
        const forms = document.querySelectorAll('form');
        for (const form of forms) {
            const submitBtn = form.querySelector('button[type="submit"], input[type="submit"]');
            if (submitBtn && this.isVisible(submitBtn)) {
                return submitBtn;
            }
        }

        return null;
    }

    simulateTyping(element, text) {
        console.log('开始输入文本到:', element.tagName, text.substring(0, 50) + '...');

        // 对于textarea和input元素
        if (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA') {
            // 先聚焦
            element.focus();

            // 清空现有内容
            element.value = '';

            // 设置新内容
            element.value = text;

            // 触发各种事件以确保页面响应
            const events = [
                'focus',
                'input',
                'change',
                'keyup',
                'blur'
            ];

            events.forEach(eventType => {
                const event = new Event(eventType, { bubbles: true, cancelable: true });
                element.dispatchEvent(event);
            });

            // 再次聚焦确保光标在正确位置
            element.focus();
            element.setSelectionRange(text.length, text.length);

            console.log('文本输入完成，当前值:', element.value.substring(0, 50) + '...');
        } else {
            // 对于contenteditable元素
            element.focus();
            element.innerHTML = '';
            element.textContent = text;

            // 触发输入事件
            const inputEvent = new Event('input', { bubbles: true });
            element.dispatchEvent(inputEvent);

            // 设置光标位置到末尾
            const range = document.createRange();
            const selection = window.getSelection();
            range.selectNodeContents(element);
            range.collapse(false);
            selection.removeAllRanges();
            selection.addRange(range);
        }
    }

    isMidjourneyPage() {
        // 检查是否在Midjourney网页版中
        const url = window.location.href;
        const title = document.title;

        // 检查URL或页面标题是否包含Midjourney相关信息
        const midjourneyIndicators = [
            'midjourney.com',
            'imagine'
        ];

        return midjourneyIndicators.some(indicator =>
            url.toLowerCase().includes(indicator) ||
            title.toLowerCase().includes(indicator)
        );
    }

    isVisible(element) {
        const style = window.getComputedStyle(element);
        return style.display !== 'none' && 
               style.visibility !== 'hidden' && 
               element.offsetWidth > 0 && 
               element.offsetHeight > 0;
    }

    delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }

    observePageChanges() {
        // 监听页面变化
        const observer = new MutationObserver(() => {
            // 可以在这里添加逻辑来检测页面变化
            // 例如检测输入框是否出现或消失
        });

        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    }
}

// 初始化发送器
new MidjourneyPromptSender();
