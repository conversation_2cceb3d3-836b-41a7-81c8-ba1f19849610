<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>页面选择器演示</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 30px;
            border-radius: 20px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }
        .title {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 3px solid #7C3AED;
            padding-bottom: 15px;
        }
        .demo-section {
            margin: 25px 0;
            padding: 25px;
            border: 2px solid #7C3AED;
            border-radius: 15px;
            background: linear-gradient(135deg, rgba(124, 58, 237, 0.05), rgba(59, 130, 246, 0.05));
        }
        .floating-button {
            width: 48px;
            height: 48px;
            background: linear-gradient(135deg, #7C3AED, #3B82F6);
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 8px 25px rgba(124, 58, 237, 0.4);
            transition: all 0.3s ease;
            margin: 10px auto;
        }
        .floating-button:hover {
            transform: scale(1.1);
            box-shadow: 0 12px 35px rgba(124, 58, 237, 0.6);
        }
        .button {
            background: linear-gradient(135deg, #7C3AED, #3B82F6);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
            transition: all 0.3s ease;
        }
        .button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(124, 58, 237, 0.4);
        }
        .button.discord {
            background: linear-gradient(135deg, #5865F2, #7289DA);
        }
        .button.cancel {
            background: #ccc;
            color: #666;
        }
        .modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.7);
            z-index: 10000;
            display: none;
            align-items: center;
            justify-content: center;
        }
        .dialog {
            background: white;
            padding: 30px;
            border-radius: 15px;
            max-width: 500px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            animation: slideIn 0.3s ease;
        }
        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(-50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        .feature-list {
            text-align: left;
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        .prompt-demo {
            background: linear-gradient(135deg, rgba(124, 58, 237, 0.1), rgba(59, 130, 246, 0.1));
            padding: 20px;
            border-radius: 10px;
            margin: 15px 0;
            position: relative;
            border-left: 4px solid #7C3AED;
        }
        .prompt-text {
            font-family: 'Courier New', monospace;
            font-size: 14px;
            color: #333;
            margin-bottom: 10px;
        }
        .status-message {
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            text-align: center;
            font-weight: bold;
        }
        .status-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status-info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">🎯 智能页面选择器演示</h1>
        
        <div class="demo-section">
            <h2>🚀 新功能介绍</h2>
            <p>解决了文件选择器权限问题，现在使用智能页面选择器：</p>
            
            <div class="feature-list">
                <h3>✨ 主要改进：</h3>
                <ul>
                    <li><strong>🎯 智能检测：</strong>自动查找已打开的Midjourney相关页面</li>
                    <li><strong>📋 手动选择：</strong>如果没有找到页面，显示选择对话框</li>
                    <li><strong>🔗 自动打开：</strong>可以自动打开新的Midjourney页面</li>
                    <li><strong>🔄 页面管理：</strong>在插件中管理所有Midjourney页面</li>
                    <li><strong>⚡ 直接发送：</strong>选择页面后立即发送prompt</li>
                </ul>
            </div>
        </div>

        <div class="demo-section">
            <h2>🎮 交互演示</h2>
            <p>点击下面的浮标体验页面选择功能：</p>
            
            <div class="prompt-demo">
                <div class="prompt-text">
                    (CINEMATIC STILL), (FILM BY Christopher Nolan), (vast space station rotating in deep space, Earth visible in background, dramatic lighting), (sci-fi epic), (wide establishing shot)
                </div>
                <div class="floating-button" onclick="showPageSelector()">
                    <svg width="18" height="18" viewBox="0 0 24 24" fill="white">
                        <path d="M3,21V3H7L12,14L17,3H21V21H17V8L13,18H11L7,8V21H3Z" stroke="white" stroke-width="0.5"/>
                    </svg>
                </div>
            </div>
            
            <div id="status-message"></div>
        </div>

        <div class="demo-section">
            <h2>🔧 使用流程</h2>
            <ol>
                <li><strong>点击浮标：</strong>在Claude页面点击prompt旁边的M浮标</li>
                <li><strong>智能检测：</strong>插件自动查找Midjourney页面</li>
                <li><strong>页面选择：</strong>如果没有找到，显示选择对话框</li>
                <li><strong>自动发送：</strong>选择页面后自动发送prompt并执行</li>
            </ol>
        </div>

        <div class="demo-section">
            <h2>📱 页面管理功能</h2>
            <p>在插件popup中新增了"管理Midjourney页面"功能：</p>
            
            <div style="text-align: center; margin: 20px 0;">
                <button class="button" onclick="showTabManagement()">演示页面管理</button>
            </div>
            
            <div class="feature-list">
                <h3>🛠️ 管理功能：</h3>
                <ul>
                    <li><strong>📋 页面列表：</strong>显示所有打开的Midjourney相关页面</li>
                    <li><strong>🚀 快速打开：</strong>一键打开Midjourney网页版或Discord</li>
                    <li><strong>🔗 连接测试：</strong>测试页面是否能正常接收prompt</li>
                    <li><strong>⚡ 快速切换：</strong>点击页面项目直接切换到该页面</li>
                </ul>
            </div>
        </div>

        <div class="demo-section">
            <h2>🎯 支持的页面类型</h2>
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin: 20px 0;">
                <div style="text-align: center; padding: 20px; background: rgba(124, 58, 237, 0.1); border-radius: 10px;">
                    <h4>🌐 Midjourney网页版</h4>
                    <p>www.midjourney.com</p>
                    <button class="button" onclick="simulateWebSend()">模拟发送</button>
                </div>
                <div style="text-align: center; padding: 20px; background: rgba(88, 101, 242, 0.1); border-radius: 10px;">
                    <h4>💬 Discord Midjourney</h4>
                    <p>discord.com</p>
                    <button class="button discord" onclick="simulateDiscordSend()">模拟发送</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 页面选择对话框 -->
    <div id="page-selector-modal" class="modal">
        <div class="dialog">
            <h3 style="color: #7C3AED; margin-bottom: 20px;">选择Midjourney页面</h3>
            <p style="margin-bottom: 25px; color: #666;">请选择要发送prompt的目标页面：</p>
            <div style="margin-bottom: 25px;">
                <button class="button" onclick="selectWeb()">Midjourney网页版</button>
                <button class="button discord" onclick="selectDiscord()">Discord Midjourney</button>
            </div>
            <p style="font-size: 14px; color: #999; margin-bottom: 20px;">
                请确保目标页面已经打开并且可以接收prompt
            </p>
            <button class="button cancel" onclick="cancelSelect()">取消</button>
        </div>
    </div>

    <!-- 页面管理对话框 -->
    <div id="tab-management-modal" class="modal">
        <div class="dialog" style="max-width: 600px;">
            <h3 style="color: #7C3AED; margin-bottom: 20px;">Midjourney页面管理</h3>
            
            <div style="text-align: left; margin-bottom: 20px;">
                <h4>当前打开的页面：</h4>
                <div id="tab-list" style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 10px 0;">
                    <div style="padding: 10px; border: 1px solid #ddd; border-radius: 5px; margin: 5px 0; cursor: pointer;">
                        <div style="font-weight: bold;">Midjourney</div>
                        <div style="font-size: 12px; color: #666;">https://www.midjourney.com/imagine</div>
                    </div>
                    <div style="padding: 10px; border: 1px solid #ddd; border-radius: 5px; margin: 5px 0; cursor: pointer;">
                        <div style="font-weight: bold;">Discord - Midjourney Bot</div>
                        <div style="font-size: 12px; color: #666;">https://discord.com/channels/...</div>
                    </div>
                </div>
            </div>
            
            <div style="margin-bottom: 20px;">
                <h4>快速操作：</h4>
                <button class="button" onclick="showMessage('已打开Midjourney网页版')">打开网页版</button>
                <button class="button discord" onclick="showMessage('已打开Discord')">打开Discord</button>
                <button class="button" onclick="testConnection()">测试连接</button>
            </div>
            
            <div id="test-result" style="margin: 15px 0;"></div>
            
            <button class="button cancel" onclick="closeTabManagement()">关闭</button>
        </div>
    </div>

    <script>
        function showPageSelector() {
            document.getElementById('page-selector-modal').style.display = 'flex';
        }

        function selectWeb() {
            document.getElementById('page-selector-modal').style.display = 'none';
            showMessage('正在发送到Midjourney网页版...', 'info');
            
            setTimeout(() => {
                showMessage('✅ 成功发送到Midjourney网页版！', 'success');
            }, 2000);
        }

        function selectDiscord() {
            document.getElementById('page-selector-modal').style.display = 'none';
            showMessage('正在发送到Discord Midjourney...', 'info');
            
            setTimeout(() => {
                showMessage('✅ 成功发送到Discord！', 'success');
            }, 2000);
        }

        function cancelSelect() {
            document.getElementById('page-selector-modal').style.display = 'none';
            showMessage('❌ 已取消发送', 'error');
        }

        function showTabManagement() {
            document.getElementById('tab-management-modal').style.display = 'flex';
        }

        function closeTabManagement() {
            document.getElementById('tab-management-modal').style.display = 'none';
        }

        function testConnection() {
            const resultDiv = document.getElementById('test-result');
            resultDiv.innerHTML = '<div class="status-message status-info">正在测试连接...</div>';
            
            setTimeout(() => {
                resultDiv.innerHTML = '<div class="status-message status-success">✅ 所有页面连接正常</div>';
            }, 1500);
        }

        function simulateWebSend() {
            showMessage('模拟发送到Midjourney网页版', 'info');
        }

        function simulateDiscordSend() {
            showMessage('模拟发送到Discord', 'info');
        }

        function showMessage(text, type = 'info') {
            const statusDiv = document.getElementById('status-message');
            statusDiv.innerHTML = `<div class="status-message status-${type}">${text}</div>`;
            
            setTimeout(() => {
                statusDiv.innerHTML = '';
            }, 3000);
        }

        // 点击背景关闭对话框
        document.querySelectorAll('.modal').forEach(modal => {
            modal.onclick = (e) => {
                if (e.target === modal) {
                    modal.style.display = 'none';
                }
            };
        });
    </script>
</body>
</html>
