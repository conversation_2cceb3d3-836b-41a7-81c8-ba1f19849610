// simple-storage.js - 极简存储服务，避免所有权限问题

class SimpleStorage {
    constructor() {
        this.storageKey = 'claude_midjourney_prompts';
        this.init();
    }

    init() {
        console.log('初始化简单存储服务');
    }

    async savePrompts(prompts) {
        try {
            const data = {
                prompts: prompts,
                timestamp: Date.now(),
                source: 'claude'
            };

            // 只使用localStorage，最简单可靠
            localStorage.setItem(this.storageKey, JSON.stringify(data));
            console.log('已保存到localStorage:', prompts.length, '个prompts');

            // 尝试Chrome存储作为备份
            this.saveToChromeStorage(data);

            return { success: true, message: '已保存到共享存储' };
        } catch (error) {
            console.error('保存prompts失败:', error);
            return { success: false, error: error.message };
        }
    }

    async loadPrompts() {
        try {
            // 从localStorage加载
            const localData = localStorage.getItem(this.storageKey);
            if (localData) {
                const data = JSON.parse(localData);
                console.log('从localStorage加载了', data.prompts.length, '个prompts');
                return { 
                    success: true, 
                    prompts: data.prompts, 
                    timestamp: data.timestamp,
                    source: 'localStorage'
                };
            }

            // 尝试从Chrome存储加载
            const chromeData = await this.loadFromChromeStorage();
            if (chromeData) {
                console.log('从Chrome存储加载了', chromeData.prompts.length, '个prompts');
                // 同步到localStorage
                localStorage.setItem(this.storageKey, JSON.stringify(chromeData));
                return { 
                    success: true, 
                    prompts: chromeData.prompts, 
                    timestamp: chromeData.timestamp,
                    source: 'chromeStorage'
                };
            }

            return { success: false, message: '没有找到共享的prompts' };
        } catch (error) {
            console.error('加载prompts失败:', error);
            return { success: false, error: error.message };
        }
    }

    async saveToChromeStorage(data) {
        try {
            if (typeof chrome !== 'undefined' && chrome.storage && chrome.storage.local) {
                await chrome.storage.local.set({ [this.storageKey]: data });
                console.log('已保存到Chrome存储');
                return true;
            }
            return false;
        } catch (error) {
            console.error('Chrome存储保存失败:', error);
            return false;
        }
    }

    async loadFromChromeStorage() {
        try {
            if (typeof chrome !== 'undefined' && chrome.storage && chrome.storage.local) {
                const result = await chrome.storage.local.get([this.storageKey]);
                return result[this.storageKey] || null;
            }
            return null;
        } catch (error) {
            console.error('Chrome存储加载失败:', error);
            return null;
        }
    }

    async clearSharedData() {
        try {
            localStorage.removeItem(this.storageKey);
            
            // 清除Chrome存储
            if (typeof chrome !== 'undefined' && chrome.storage && chrome.storage.local) {
                await chrome.storage.local.remove([this.storageKey]);
            }
            
            console.log('共享数据已清除');
            return { success: true };
        } catch (error) {
            console.error('清除共享数据失败:', error);
            return { success: false, error: error.message };
        }
    }

    // 监听共享数据变化
    onSharedDataChange(callback) {
        // 监听localStorage变化
        window.addEventListener('storage', (event) => {
            if (event.key === this.storageKey) {
                console.log('检测到共享存储更新');
                const data = event.newValue ? JSON.parse(event.newValue) : null;
                callback(data);
            }
        });

        // 监听Chrome存储变化
        if (typeof chrome !== 'undefined' && chrome.storage && chrome.storage.onChanged) {
            chrome.storage.onChanged.addListener((changes, namespace) => {
                if (namespace === 'local' && changes[this.storageKey]) {
                    console.log('检测到Chrome存储更新');
                    callback(changes[this.storageKey].newValue);
                }
            });
        }
    }

    // 获取存储状态
    async getStorageStatus() {
        const status = {
            localStorage: false,
            chromeStorage: false,
            dataExists: false
        };

        try {
            // 检查localStorage
            const localData = localStorage.getItem(this.storageKey);
            status.localStorage = true;
            if (localData) {
                status.dataExists = true;
            }
        } catch (error) {
            console.log('localStorage不可用');
        }

        try {
            // 检查Chrome存储
            if (typeof chrome !== 'undefined' && chrome.storage && chrome.storage.local) {
                status.chromeStorage = true;
                const result = await chrome.storage.local.get([this.storageKey]);
                if (result[this.storageKey]) {
                    status.dataExists = true;
                }
            }
        } catch (error) {
            console.log('Chrome存储不可用');
        }

        return status;
    }

    // 手动同步数据
    async syncData() {
        try {
            const localData = localStorage.getItem(this.storageKey);
            if (localData && typeof chrome !== 'undefined' && chrome.storage) {
                const data = JSON.parse(localData);
                await chrome.storage.local.set({ [this.storageKey]: data });
                console.log('数据同步完成');
                return { success: true, message: '数据同步完成' };
            }
            return { success: false, message: '没有数据需要同步' };
        } catch (error) {
            console.error('数据同步失败:', error);
            return { success: false, error: error.message };
        }
    }
}

// 导出服务
window.SimpleStorage = SimpleStorage;
