<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>手动检测模式测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            min-height: 100vh;
        }
        .container {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        h1 {
            color: #4a5568;
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.5em;
        }
        .status-section {
            background: #f7fafc;
            border-left: 4px solid #4299e1;
            padding: 20px;
            margin: 20px 0;
            border-radius: 0 8px 8px 0;
        }
        .feature-section {
            background: #f0fff4;
            border-left: 4px solid #48bb78;
            padding: 20px;
            margin: 20px 0;
            border-radius: 0 8px 8px 0;
        }
        .test-section {
            background: #fffaf0;
            border-left: 4px solid #ed8936;
            padding: 20px;
            margin: 20px 0;
            border-radius: 0 8px 8px 0;
        }
        .shortcut-key {
            background: #2d3748;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-family: monospace;
            font-weight: bold;
        }
        .feature-list {
            list-style: none;
            padding: 0;
        }
        .feature-list li {
            padding: 8px 0;
            border-bottom: 1px solid #e2e8f0;
        }
        .feature-list li:last-child {
            border-bottom: none;
        }
        .feature-list li::before {
            content: "✅ ";
            color: #48bb78;
            font-weight: bold;
        }
        .test-button {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
            transition: transform 0.2s;
        }
        .test-button:hover {
            transform: translateY(-2px);
        }
        .code-example {
            background: #1a202c;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            margin: 15px 0;
            overflow-x: auto;
        }
        .highlight {
            background: #ffd700;
            color: #333;
            padding: 2px 4px;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 手动检测模式测试</h1>
        
        <div class="status-section">
            <h2>📊 功能状态</h2>
            <p><strong>问题解决：</strong>M图标动画被自动监控机制打断</p>
            <p><strong>解决方案：</strong>实现手动快捷键控制的监控模式</p>
            <p><strong>当前状态：</strong><span class="highlight">已完成开发，等待测试</span></p>
        </div>

        <div class="feature-section">
            <h2>✨ 新增功能</h2>
            <ul class="feature-list">
                <li><strong>检测模式切换：</strong>使用 <span class="shortcut-key">Ctrl+Shift+D</span> 在自动/手动模式间切换</li>
                <li><strong>手动检测：</strong>使用 <span class="shortcut-key">Ctrl+Shift+M</span> 手动触发页面监控</li>
                <li><strong>智能停止：</strong>手动模式下自动停止所有定期检查和监听器</li>
                <li><strong>状态通知：</strong>模式切换和手动检测时显示通知</li>
                <li><strong>动画保护：</strong>手动模式下M按钮动画不会被打断</li>
            </ul>
        </div>

        <div class="test-section">
            <h2>🧪 测试步骤</h2>
            <ol>
                <li><strong>安装插件：</strong>确保插件已正确安装并在Claude页面激活</li>
                <li><strong>切换到手动模式：</strong>按 <span class="shortcut-key">Ctrl+Shift+D</span> 切换到手动检测模式</li>
                <li><strong>观察M按钮：</strong>确认M按钮的动画状态不再被自动打断</li>
                <li><strong>手动检测：</strong>按 <span class="shortcut-key">Ctrl+Shift+M</span> 手动触发检测</li>
                <li><strong>验证功能：</strong>确认手动检测能正常添加新的M按钮</li>
            </ol>
        </div>

        <div class="feature-section">
            <h2>🔧 技术实现</h2>
            <h3>📍 主要修改：</h3>
            <div class="code-example">
// 1. 添加检测模式控制
this.detectionMode = 'auto'; // 默认自动模式
this.autoDetectionEnabled = true;

// 2. 修改监控函数，添加模式检查
observePageChanges() {
    if (!this.autoDetectionEnabled) {
        console.log('⏸️ 手动模式，跳过页面变化监听');
        return;
    }
    // ... 原有逻辑
}

// 3. 实现手动检测
manualDetect() {
    console.log('🔍 手动检测触发');
    this.addFloatingButtons();
    this.autoExtractAndCache();
}
            </div>

            <h3>🛡️ 保护机制：</h3>
            <ul class="feature-list">
                <li><strong>定时器管理：</strong>手动模式下清除所有定期检查定时器</li>
                <li><strong>监听器控制：</strong>断开页面变化和重新生成监听器</li>
                <li><strong>状态检查：</strong>在每个监控函数中检查当前模式</li>
                <li><strong>优雅降级：</strong>模式切换时平滑过渡，不影响现有功能</li>
            </ul>
        </div>

        <div class="test-section">
            <h2>🎯 预期效果</h2>
            <p><strong>自动模式（默认）：</strong></p>
            <ul>
                <li>✅ M按钮会自动检测和更新</li>
                <li>❌ 动画可能被定期检查打断</li>
            </ul>
            
            <p><strong>手动模式：</strong></p>
            <ul>
                <li>✅ M按钮动画不会被打断</li>
                <li>✅ 用户完全控制检测时机</li>
                <li>✅ 按需检测，性能更好</li>
            </ul>
        </div>

        <div class="status-section">
            <h2>📝 使用说明</h2>
            <p><strong>推荐工作流程：</strong></p>
            <ol>
                <li>打开Claude页面后，默认为自动模式</li>
                <li>如果发现M按钮动画被打断，按 <span class="shortcut-key">Ctrl+Shift+D</span> 切换到手动模式</li>
                <li>在手动模式下，当需要检测新的prompts时，按 <span class="shortcut-key">Ctrl+Shift+M</span></li>
                <li>如果希望恢复自动检测，再次按 <span class="shortcut-key">Ctrl+Shift+D</span> 切换回自动模式</li>
            </ol>
        </div>

        <div class="test-section">
            <h2>🧪 在线测试</h2>
            <p>点击下面的按钮来测试功能：</p>
            <button class="test-button" onclick="testModeToggle()">测试模式切换</button>
            <button class="test-button" onclick="testManualDetect()">测试手动检测</button>
            <button class="test-button" onclick="checkMButtons()">检查M按钮</button>
            <button class="test-button" onclick="runFullTest()">运行完整测试</button>

            <div id="test-output" style="background: #1a202c; color: #e2e8f0; padding: 15px; border-radius: 8px; margin-top: 15px; font-family: monospace; font-size: 12px; max-height: 300px; overflow-y: auto;">
                测试输出将显示在这里...
            </div>
        </div>
    </div>

    <script src="test-manual-detection.js"></script>
    <script>
        // 重定向console.log到页面显示
        const originalLog = console.log;
        const testOutput = document.getElementById('test-output');

        console.log = function(...args) {
            originalLog.apply(console, args);
            if (testOutput) {
                const message = args.map(arg =>
                    typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
                ).join(' ');
                testOutput.innerHTML += message + '\n';
                testOutput.scrollTop = testOutput.scrollHeight;
            }
        };

        // 测试函数
        function testModeToggle() {
            console.log('🔄 用户触发模式切换测试...');
            if (window.chromeMessageListener) {
                window.chromeMessageListener({
                    action: 'setDetectionMode',
                    mode: 'manual'
                }, (response) => {
                    console.log('✅ 切换到手动模式:', response);
                });
            }
        }

        function testManualDetect() {
            console.log('🔍 用户触发手动检测测试...');
            if (window.chromeMessageListener) {
                window.chromeMessageListener({
                    action: 'manualDetect'
                }, (response) => {
                    console.log('✅ 手动检测完成:', response);
                });
            }
        }

        function checkMButtons() {
            console.log('🔍 检查当前M按钮状态...');
            const buttons = document.querySelectorAll('.claude-mj-floating-btn, .claude-mj-floating-button');
            console.log(`📊 找到 ${buttons.length} 个M按钮`);
            return buttons.length;
        }

        function runFullTest() {
            console.log('🚀 开始运行完整测试...');
            testOutput.innerHTML = '';

            // 重新运行测试
            if (typeof runTests === 'function') {
                runTests();
            } else {
                console.log('❌ 测试函数未找到');
            }
        }

        // 清空输出
        function clearOutput() {
            testOutput.innerHTML = '测试输出将显示在这里...';
        }
    </script>
</body>
</html>
