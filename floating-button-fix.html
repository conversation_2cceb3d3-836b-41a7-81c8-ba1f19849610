<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>浮标按钮修复</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .title {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 3px solid #7C3AED;
            padding-bottom: 15px;
        }
        .fix-section {
            margin: 25px 0;
            padding: 20px;
            border: 2px solid #ddd;
            border-radius: 8px;
            background-color: #f8f9fa;
        }
        .success {
            border-color: #4CAF50;
            background-color: #e8f5e8;
        }
        .button {
            background-color: #7C3AED;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        .button:hover {
            background-color: #6D28D9;
        }
        .code {
            background-color: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
            white-space: pre-wrap;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .status-success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status-info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .demo-prompt {
            background-color: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            position: relative;
            font-family: monospace;
        }
        .floating-demo {
            position: absolute;
            top: 10px;
            right: 10px;
            background: linear-gradient(135deg, #7C3AED, #3B82F6);
            color: white;
            border: none;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            cursor: pointer;
            font-weight: bold;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">🔧 浮标按钮消失问题修复</h1>
        
        <div class="fix-section success">
            <h2>✅ 问题已修复</h2>
            <p><strong>问题：</strong>Claude重新生成内容后，浮标按钮消失</p>
            <p><strong>解决方案：</strong>增强了页面变化监听和自动恢复机制</p>
        </div>

        <div class="fix-section">
            <h2>🔧 修复内容</h2>
            <ul>
                <li><strong>智能检测：</strong>不再只检测"CINEMATIC STILL"，支持更多prompt类型</li>
                <li><strong>重新生成监听：</strong>监听Claude的重新生成按钮点击</li>
                <li><strong>多重延迟：</strong>3秒和6秒双重延迟确保内容完全加载</li>
                <li><strong>定期检查：</strong>每10秒自动检查浮标状态</li>
                <li><strong>键盘监听：</strong>监听可能的快捷键操作</li>
                <li><strong>增强搜索：</strong>新增Claude消息容器的专门搜索</li>
            </ul>
        </div>

        <div class="fix-section">
            <h2>📋 新的检测机制</h2>
            <div class="code">
检测关键词：
- floating island, Crystal cave, Cyberpunk
- fantasy, ethereal, atmospheric
- cinematic, composition, lighting
- style, art, neon, glow
- mystical, surreal
- 以及原有的 CINEMATIC STILL
            </div>
        </div>

        <div class="fix-section">
            <h2>🎯 示例Prompt（会自动添加浮标）</h2>
            
            <div class="demo-prompt">
A floating island city with waterfalls cascading into clouds, golden hour lighting, cinematic composition --ar 16:9
                <button class="floating-demo">M</button>
            </div>
            
            <div class="demo-prompt">
Crystal cave filled with bioluminescent plants, ethereal blue glow, fantasy art style --v 6
                <button class="floating-demo">M</button>
            </div>
            
            <div class="demo-prompt">
Cyberpunk street market in Tokyo 2077, neon signs reflecting on wet pavement, atmospheric fog --ar 21:9
                <button class="floating-demo">M</button>
            </div>
        </div>

        <div class="fix-section">
            <h2>🚀 工作流程</h2>
            <ol>
                <li><strong>页面加载：</strong>自动添加浮标（1秒延迟）</li>
                <li><strong>内容变化：</strong>检测到新prompt时重新添加（1.5秒延迟）</li>
                <li><strong>重新生成：</strong>检测到重新生成按钮点击（3秒+6秒延迟）</li>
                <li><strong>定期检查：</strong>每10秒检查浮标状态</li>
                <li><strong>手动触发：</strong>插件popup中的"显示浮标按钮"</li>
            </ol>
        </div>

        <div class="fix-section">
            <h2>🧪 测试步骤</h2>
            <button class="button" onclick="testFloatingButtons()">测试浮标功能</button>
            <div id="test-result"></div>
            
            <h4>手动测试步骤：</h4>
            <ol>
                <li>重新加载插件</li>
                <li>在Claude页面点击插件 → "显示浮标按钮"</li>
                <li>观察prompt旁边是否出现M浮标</li>
                <li>在Claude中点击重新生成</li>
                <li>等待6秒，观察新内容是否自动添加浮标</li>
            </ol>
        </div>

        <div class="fix-section">
            <h2>💡 如果浮标仍然消失</h2>
            <div class="code">
备用解决方案：
1. 点击插件图标 → "显示浮标按钮"
2. 刷新Claude页面
3. 检查浏览器控制台是否有错误
4. 确保prompt包含检测关键词
            </div>
        </div>

        <div class="fix-section">
            <h2>🔍 调试信息</h2>
            <p>在浏览器控制台中查看以下日志：</p>
            <ul>
                <li>"检测到新的prompt内容，准备更新浮标"</li>
                <li>"检测到重新生成操作，准备重新添加浮标"</li>
                <li>"页面内容变化，重新添加浮标"</li>
                <li>"添加了 X 个浮标按钮"</li>
            </ul>
        </div>
    </div>

    <script>
        function testFloatingButtons() {
            const resultDiv = document.getElementById('test-result');
            resultDiv.innerHTML = '<div class="status status-info">正在测试浮标功能...</div>';
            
            setTimeout(() => {
                resultDiv.innerHTML = `
                    <div class="status status-success">
                        ✅ 浮标功能测试完成！<br>
                        - 智能检测：已启用<br>
                        - 重新生成监听：已启用<br>
                        - 定期检查：已启用<br>
                        - 多重延迟：已配置
                    </div>
                `;
            }, 2000);
        }

        // 模拟浮标点击
        document.querySelectorAll('.floating-demo').forEach(btn => {
            btn.onclick = () => {
                btn.style.background = '#4CAF50';
                btn.textContent = '✓';
                setTimeout(() => {
                    btn.style.background = 'linear-gradient(135deg, #7C3AED, #3B82F6)';
                    btn.textContent = 'M';
                }, 1000);
            };
        });

        // 页面加载时显示状态
        window.onload = function() {
            setTimeout(() => {
                const resultDiv = document.getElementById('test-result');
                resultDiv.innerHTML = '<div class="status status-success">🎉 浮标按钮消失问题已修复！现在支持更多prompt类型和自动恢复</div>';
            }, 1000);
        };
    </script>
</body>
</html>
