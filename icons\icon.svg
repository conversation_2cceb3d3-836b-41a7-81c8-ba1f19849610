<svg width="128" height="128" viewBox="0 0 128 128" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#4CAF50;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#2196F3;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- 背景圆形 -->
  <circle cx="64" cy="64" r="60" fill="url(#grad1)" stroke="#fff" stroke-width="4"/>
  
  <!-- Claude图标 (左侧) -->
  <circle cx="40" cy="50" r="12" fill="#fff" opacity="0.9"/>
  <text x="40" y="56" text-anchor="middle" fill="#4CAF50" font-family="Arial" font-size="10" font-weight="bold">C</text>
  
  <!-- 箭头 -->
  <path d="M 52 50 L 76 50 M 70 44 L 76 50 L 70 56" stroke="#fff" stroke-width="3" fill="none" stroke-linecap="round"/>
  
  <!-- Midjourney图标 (右侧) -->
  <rect x="80" y="42" width="16" height="16" rx="3" fill="#fff" opacity="0.9"/>
  <text x="88" y="52" text-anchor="middle" fill="#2196F3" font-family="Arial" font-size="10" font-weight="bold">M</text>
  
  <!-- 底部文字 -->
  <text x="64" y="85" text-anchor="middle" fill="#fff" font-family="Arial" font-size="12" font-weight="bold">Auto</text>
  <text x="64" y="100" text-anchor="middle" fill="#fff" font-family="Arial" font-size="10">Prompt</text>
</svg>
