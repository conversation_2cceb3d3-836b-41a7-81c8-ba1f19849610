<!DOCTYPE html>
<html>
<head>
    <title>生成插件图标</title>
</head>
<body>
    <canvas id="canvas16" width="16" height="16" style="border: 1px solid #ccc; margin: 5px;"></canvas>
    <canvas id="canvas48" width="48" height="48" style="border: 1px solid #ccc; margin: 5px;"></canvas>
    <canvas id="canvas128" width="128" height="128" style="border: 1px solid #ccc; margin: 5px;"></canvas>
    
    <div>
        <button onclick="downloadIcons()">下载图标</button>
    </div>

    <script>
        function createIcon(canvas, size) {
            const ctx = canvas.getContext('2d');
            
            // 清空画布
            ctx.clearRect(0, 0, size, size);
            
            // 创建渐变背景
            const gradient = ctx.createLinearGradient(0, 0, size, size);
            gradient.addColorStop(0, '#4CAF50');
            gradient.addColorStop(1, '#2196F3');
            
            // 绘制圆形背景
            ctx.fillStyle = gradient;
            ctx.beginPath();
            ctx.arc(size/2, size/2, size/2 - 2, 0, 2 * Math.PI);
            ctx.fill();
            
            // 绘制白色边框
            ctx.strokeStyle = '#fff';
            ctx.lineWidth = Math.max(1, size/32);
            ctx.stroke();
            
            // 计算字体大小
            const fontSize = Math.max(6, size/8);
            ctx.font = `bold ${fontSize}px Arial`;
            ctx.fillStyle = '#fff';
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            
            // 绘制左侧C (Claude)
            const leftX = size * 0.3;
            const topY = size * 0.4;
            
            ctx.fillStyle = '#fff';
            ctx.beginPath();
            ctx.arc(leftX, topY, fontSize * 0.8, 0, 2 * Math.PI);
            ctx.fill();
            
            ctx.fillStyle = '#4CAF50';
            ctx.fillText('C', leftX, topY);
            
            // 绘制箭头
            const arrowY = topY;
            const arrowStartX = leftX + fontSize;
            const arrowEndX = size * 0.7 - fontSize;
            
            ctx.strokeStyle = '#fff';
            ctx.lineWidth = Math.max(1, size/40);
            ctx.beginPath();
            ctx.moveTo(arrowStartX, arrowY);
            ctx.lineTo(arrowEndX, arrowY);
            
            // 箭头头部
            const arrowSize = fontSize * 0.3;
            ctx.moveTo(arrowEndX - arrowSize, arrowY - arrowSize);
            ctx.lineTo(arrowEndX, arrowY);
            ctx.lineTo(arrowEndX - arrowSize, arrowY + arrowSize);
            ctx.stroke();
            
            // 绘制右侧M (Midjourney)
            const rightX = size * 0.7;
            
            ctx.fillStyle = '#fff';
            ctx.fillRect(rightX - fontSize * 0.8, topY - fontSize * 0.8, fontSize * 1.6, fontSize * 1.6);
            
            ctx.fillStyle = '#2196F3';
            ctx.fillText('M', rightX, topY);
            
            // 绘制底部文字
            if (size >= 48) {
                const bottomFontSize = Math.max(4, size/12);
                ctx.font = `bold ${bottomFontSize}px Arial`;
                ctx.fillStyle = '#fff';
                ctx.fillText('Auto', size/2, size * 0.7);
                
                const smallFontSize = Math.max(3, size/16);
                ctx.font = `${smallFontSize}px Arial`;
                ctx.fillText('Prompt', size/2, size * 0.85);
            }
        }
        
        function downloadIcons() {
            const sizes = [16, 48, 128];
            
            sizes.forEach(size => {
                const canvas = document.getElementById(`canvas${size}`);
                createIcon(canvas, size);
                
                // 创建下载链接
                const link = document.createElement('a');
                link.download = `icon${size}.png`;
                link.href = canvas.toDataURL();
                link.click();
            });
        }
        
        // 页面加载时创建图标
        window.onload = function() {
            createIcon(document.getElementById('canvas16'), 16);
            createIcon(document.getElementById('canvas48'), 48);
            createIcon(document.getElementById('canvas128'), 128);
        };
    </script>
</body>
</html>
