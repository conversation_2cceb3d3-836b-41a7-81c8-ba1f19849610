<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>错误修复测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .title {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 3px solid #dc3545;
            padding-bottom: 15px;
        }
        .fix-section {
            margin: 25px 0;
            padding: 20px;
            border: 2px solid #28a745;
            border-radius: 8px;
            background-color: #d4edda;
        }
        .error-section {
            margin: 25px 0;
            padding: 20px;
            border: 2px solid #dc3545;
            border-radius: 8px;
            background-color: #f8d7da;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .status-success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status-error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .code-fix {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">🔧 Chrome API错误修复</h1>
        
        <div class="error-section">
            <h2>❌ 原始错误</h2>
            <div class="status status-error">
                <strong>错误信息：</strong><br>
                <code>Uncaught (in promise) TypeError: Cannot read properties of undefined (reading 'local')</code>
            </div>
            <p><strong>原因：</strong>在内容脚本中直接使用 <code>chrome.storage.local</code> 可能会有权限或时序问题</p>
        </div>

        <div class="fix-section">
            <h2>✅ 修复方案</h2>
            
            <h3>🛡️ 1. 添加安全检查</h3>
            <div class="code-fix">
// 修复前（可能出错）
chrome.storage.local.set({ cachedPrompts: prompts });

// 修复后（安全）
if (typeof chrome !== 'undefined' && chrome.storage && chrome.storage.local) {
    chrome.storage.local.set({ cachedPrompts: prompts });
} else {
    console.log('Chrome storage不可用，跳过缓存');
}
            </div>

            <h3>🛡️ 2. 统一的安全API调用</h3>
            <div class="code-fix">
// 添加安全的chrome API调用方法
async safeChromeMessage(message) {
    if (typeof chrome === 'undefined' || !chrome.runtime || !chrome.runtime.sendMessage) {
        console.log('Chrome runtime不可用，跳过API调用');
        return { success: false, error: 'Chrome runtime不可用' };
    }
    
    try {
        return await chrome.runtime.sendMessage(message);
    } catch (error) {
        console.error('Chrome API调用失败:', error);
        return { success: false, error: error.message };
    }
}
            </div>

            <h3>🛡️ 3. 错误处理增强</h3>
            <div class="code-fix">
// 修复前
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    // 可能出错的代码
});

// 修复后
if (typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.onMessage) {
    chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
        try {
            // 安全的代码
        } catch (error) {
            console.error('处理消息失败:', error);
            sendResponse({ success: false, error: error.message });
        }
    });
}
            </div>
        </div>

        <div class="fix-section">
            <h2>🎯 测试代码块</h2>
            <p>这些代码块应该正常显示M浮标，不会出现Chrome API错误：</p>
            
            <h4>测试代码块1：</h4>
            <pre class="code-block__code">A beautiful sunset over mountains with golden light streaming through clouds, peaceful landscape, warm colors, cinematic composition --ar 16:9 --v 6</pre>

            <h4>测试代码块2：</h4>
            <pre>Modern architecture building with glass facade, minimalist design, urban environment, professional photography --ar 4:3 --v 6</pre>
        </div>

        <div class="fix-section">
            <h2>📋 修复清单</h2>
            <div class="status status-success">
                ✅ <strong>已修复的问题：</strong><br>
                - Chrome storage API安全检查<br>
                - Chrome runtime消息传递安全检查<br>
                - 统一的错误处理机制<br>
                - 防止undefined访问错误<br>
                - 增强的调试日志<br>
                - 优雅的降级处理
            </div>
        </div>

        <div class="fix-section">
            <h2>🔍 验证步骤</h2>
            <ol>
                <li><strong>重新加载插件</strong> - 在Chrome扩展页面刷新插件</li>
                <li><strong>打开控制台</strong> - 检查是否还有错误信息</li>
                <li><strong>测试功能</strong> - 验证M浮标是否正常显示</li>
                <li><strong>检查日志</strong> - 应该看到安全的调试信息</li>
            </ol>
        </div>
    </div>

    <script>
        // 页面加载时显示状态
        window.onload = function() {
            setTimeout(() => {
                console.log('🔧 Chrome API错误修复测试页面已加载');
                console.log('现在插件应该不会出现 "Cannot read properties of undefined" 错误了！');
            }, 1000);
        };
    </script>
</body>
</html>
