<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>存储系统测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .title {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 3px solid #7C3AED;
            padding-bottom: 15px;
        }
        .test-section {
            margin: 25px 0;
            padding: 20px;
            border: 2px solid #ddd;
            border-radius: 8px;
            background-color: #f8f9fa;
        }
        .button {
            background-color: #7C3AED;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        .button:hover {
            background-color: #6D28D9;
        }
        .button.success {
            background-color: #4CAF50;
        }
        .button.error {
            background-color: #f44336;
        }
        .result {
            margin: 15px 0;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 200px;
            overflow-y: auto;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .status-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin: 20px 0;
        }
        .status-card {
            padding: 15px;
            border-radius: 8px;
            text-align: center;
        }
        .status-available {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
        }
        .status-unavailable {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">🔧 存储系统测试</h1>
        
        <div class="test-section">
            <h2>📊 存储状态检查</h2>
            <p>检查各种存储方式的可用性：</p>
            <button class="button" onclick="checkStorageStatus()">检查存储状态</button>
            <div id="storage-status"></div>
        </div>

        <div class="test-section">
            <h2>💾 保存测试</h2>
            <p>测试保存prompts到存储：</p>
            <button class="button" onclick="testSave()">测试保存</button>
            <div id="save-result" class="result info" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h2>📂 加载测试</h2>
            <p>测试从存储加载prompts：</p>
            <button class="button" onclick="testLoad()">测试加载</button>
            <div id="load-result" class="result info" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h2>🔄 同步测试</h2>
            <p>测试数据同步功能：</p>
            <button class="button" onclick="testSync()">测试同步</button>
            <div id="sync-result" class="result info" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h2>🗑️ 清理测试</h2>
            <p>清除所有测试数据：</p>
            <button class="button error" onclick="clearData()">清除数据</button>
            <div id="clear-result" class="result info" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h2>🔍 调试信息</h2>
            <p>显示详细的调试信息：</p>
            <button class="button" onclick="showDebugInfo()">显示调试信息</button>
            <div id="debug-info" class="result info" style="display: none;"></div>
        </div>
    </div>

    <script src="simple-storage.js"></script>
    <script>
        let storage;

        // 页面加载时初始化
        window.onload = function() {
            try {
                storage = new SimpleStorage();
                console.log('存储服务初始化成功');
                checkStorageStatus();
            } catch (error) {
                console.error('存储服务初始化失败:', error);
            }
        };

        async function checkStorageStatus() {
            const statusDiv = document.getElementById('storage-status');
            statusDiv.innerHTML = '<p>正在检查存储状态...</p>';

            try {
                const status = await storage.getStorageStatus();
                
                let html = '<div class="status-grid">';
                
                html += `<div class="status-card ${status.localStorage ? 'status-available' : 'status-unavailable'}">
                    <h4>localStorage</h4>
                    <p>${status.localStorage ? '✅ 可用' : '❌ 不可用'}</p>
                </div>`;
                
                html += `<div class="status-card ${status.chromeStorage ? 'status-available' : 'status-unavailable'}">
                    <h4>Chrome存储</h4>
                    <p>${status.chromeStorage ? '✅ 可用' : '❌ 不可用'}</p>
                </div>`;
                
                html += '</div>';
                
                html += `<p><strong>数据状态:</strong> ${status.dataExists ? '✅ 有数据' : '❌ 无数据'}</p>`;
                
                statusDiv.innerHTML = html;
            } catch (error) {
                statusDiv.innerHTML = `<div class="error">检查失败: ${error.message}</div>`;
            }
        }

        async function testSave() {
            const resultDiv = document.getElementById('save-result');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result info';
            resultDiv.textContent = '正在测试保存...';

            try {
                const testPrompts = [
                    '(CINEMATIC STILL), (FILM BY Christopher Nolan), (测试prompt 1)',
                    '(CINEMATIC STILL), (FILM BY Denis Villeneuve), (测试prompt 2)',
                    '(CINEMATIC STILL), (FILM BY Ridley Scott), (测试prompt 3)'
                ];

                const result = await storage.savePrompts(testPrompts);
                
                if (result.success) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ 保存成功!\n保存了 ${testPrompts.length} 个prompts\n消息: ${result.message}`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ 保存失败: ${result.error || result.message}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 保存异常: ${error.message}`;
            }
        }

        async function testLoad() {
            const resultDiv = document.getElementById('load-result');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result info';
            resultDiv.textContent = '正在测试加载...';

            try {
                const result = await storage.loadPrompts();
                
                if (result.success) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ 加载成功!\n加载了 ${result.prompts.length} 个prompts\n来源: ${result.source}\n时间: ${new Date(result.timestamp).toLocaleString()}\n\n前3个prompts:\n${result.prompts.slice(0, 3).map((p, i) => `${i+1}. ${p.substring(0, 50)}...`).join('\n')}`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ 加载失败: ${result.error || result.message}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 加载异常: ${error.message}`;
            }
        }

        async function testSync() {
            const resultDiv = document.getElementById('sync-result');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result info';
            resultDiv.textContent = '正在测试同步...';

            try {
                const result = await storage.syncData();
                
                if (result.success) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ 同步成功!\n消息: ${result.message}`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ 同步失败: ${result.error || result.message}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 同步异常: ${error.message}`;
            }
        }

        async function clearData() {
            const resultDiv = document.getElementById('clear-result');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result info';
            resultDiv.textContent = '正在清除数据...';

            try {
                const result = await storage.clearSharedData();
                
                if (result.success) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = '✅ 数据清除成功!';
                    // 重新检查状态
                    setTimeout(checkStorageStatus, 1000);
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = `❌ 清除失败: ${result.error || result.message}`;
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = `❌ 清除异常: ${error.message}`;
            }
        }

        function showDebugInfo() {
            const resultDiv = document.getElementById('debug-info');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result info';

            let info = '=== 调试信息 ===\n\n';
            info += `页面URL: ${window.location.href}\n`;
            info += `用户代理: ${navigator.userAgent}\n\n`;
            
            info += '=== 存储检查 ===\n';
            info += `localStorage可用: ${typeof Storage !== 'undefined'}\n`;
            info += `Chrome扩展API: ${typeof chrome !== 'undefined'}\n`;
            info += `Chrome存储API: ${typeof chrome !== 'undefined' && chrome.storage ? '可用' : '不可用'}\n\n`;
            
            info += '=== localStorage内容 ===\n';
            try {
                const data = localStorage.getItem('claude_midjourney_prompts');
                if (data) {
                    const parsed = JSON.parse(data);
                    info += `数据存在: 是\n`;
                    info += `prompts数量: ${parsed.prompts ? parsed.prompts.length : 0}\n`;
                    info += `时间戳: ${new Date(parsed.timestamp).toLocaleString()}\n`;
                } else {
                    info += `数据存在: 否\n`;
                }
            } catch (error) {
                info += `读取错误: ${error.message}\n`;
            }

            resultDiv.textContent = info;
        }

        // 监听存储变化
        if (typeof Storage !== 'undefined') {
            window.addEventListener('storage', (event) => {
                if (event.key === 'claude_midjourney_prompts') {
                    console.log('检测到存储变化:', event);
                    checkStorageStatus();
                }
            });
        }
    </script>
</body>
</html>
