<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>跨窗口功能测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .title {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 3px solid #007bff;
            padding-bottom: 15px;
        }
        .fix-section {
            margin: 25px 0;
            padding: 20px;
            border: 2px solid #28a745;
            border-radius: 8px;
            background-color: #d4edda;
        }
        .problem-section {
            margin: 25px 0;
            padding: 20px;
            border: 2px solid #dc3545;
            border-radius: 8px;
            background-color: #f8d7da;
        }
        .test-section {
            margin: 25px 0;
            padding: 20px;
            border: 2px solid #17a2b8;
            border-radius: 8px;
            background-color: #d1ecf1;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .status-success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status-error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .code-fix {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            overflow-x: auto;
        }
        .test-block {
            margin: 15px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #f8f9fa;
            font-family: monospace;
            position: relative;
        }
        .step {
            background: #e9ecef;
            border-left: 4px solid #007bff;
            padding: 15px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">🪟 跨窗口功能修复</h1>
        
        <div class="problem-section">
            <h2>❌ 问题描述</h2>
            <div class="status status-error">
                <strong>跨窗口通信失败：</strong><br>
                当Midjourney标签页被拖到新窗口时，插件无法正确发送prompt
            </div>
            
            <h3>🔍 问题原因：</h3>
            <ul>
                <li>❌ <strong>窗口焦点问题</strong> - 没有先切换到正确的窗口</li>
                <li>❌ <strong>标签页激活失败</strong> - 在错误的窗口中尝试激活标签页</li>
                <li>❌ <strong>消息发送失败</strong> - 目标标签页不在当前窗口</li>
                <li>❌ <strong>缺少调试信息</strong> - 难以诊断跨窗口问题</li>
            </ul>
        </div>

        <div class="fix-section">
            <h2>✅ 修复方案</h2>
            
            <h3>🛠️ 增强的窗口切换逻辑：</h3>
            <div class="code-fix">
async switchToTabWithRetry(tabId, maxRetries = 3) {
    for (let i = 0; i < maxRetries; i++) {
        try {
            // 1. 首先获取标签页信息，确定它在哪个窗口
            const tab = await chrome.tabs.get(tabId);
            console.log('标签页信息:', { 
                id: tab.id, 
                windowId: tab.windowId, 
                url: tab.url 
            });
            
            // 2. 先切换到正确的窗口
            await chrome.windows.update(tab.windowId, { focused: true });
            console.log('成功切换到窗口:', tab.windowId);
            
            // 3. 等待窗口切换完成
            await new Promise(resolve => setTimeout(resolve, 200));
            
            // 4. 然后激活标签页
            await chrome.tabs.update(tabId, { active: true });
            console.log('成功切换到标签页:', tabId);
            return;
        } catch (error) {
            // 错误处理和重试逻辑...
        }
    }
}
            </div>

            <h3>🔍 增强的调试信息：</h3>
            <div class="code-fix">
// 详细记录每个找到的标签页信息
midjourneyTabs.forEach((tab, index) => {
    console.log(`Midjourney标签页${index + 1}:`, {
        id: tab.id,
        windowId: tab.windowId,
        url: tab.url,
        title: tab.title,
        active: tab.active
    });
});
            </div>
        </div>

        <div class="test-section">
            <h2>🧪 测试代码块</h2>
            <p>使用这些代码块测试跨窗口功能：</p>
            
            <h4>测试代码块1 - 简单prompt：</h4>
            <pre class="test-block">A beautiful sunset over mountains, golden hour lighting, landscape photography --ar 16:9</pre>

            <h4>测试代码块2 - 复杂prompt：</h4>
            <pre class="test-block">Cyberpunk city at night with neon lights reflecting on wet streets, flying cars, holographic advertisements, futuristic architecture, rain, purple and blue color palette --ar 21:9 --v 6</pre>
        </div>

        <div class="test-section">
            <h2>🔍 测试步骤</h2>
            
            <div class="step">
                <h4>步骤1：准备环境</h4>
                <ol>
                    <li>重新加载插件（Chrome扩展页面刷新）</li>
                    <li>打开Midjourney页面（www.midjourney.com/imagine）</li>
                    <li>确保Claude页面有代码块显示M按钮</li>
                </ol>
            </div>

            <div class="step">
                <h4>步骤2：同窗口测试</h4>
                <ol>
                    <li>保持Midjourney和Claude在同一个窗口</li>
                    <li>点击Claude页面的M按钮</li>
                    <li>验证prompt是否成功发送到Midjourney</li>
                </ol>
            </div>

            <div class="step">
                <h4>步骤3：跨窗口测试</h4>
                <ol>
                    <li>将Midjourney标签页拖到新窗口（分屏显示）</li>
                    <li>回到Claude页面，点击M按钮</li>
                    <li>观察是否能正确切换到Midjourney窗口</li>
                    <li>验证prompt是否成功发送</li>
                </ol>
            </div>

            <div class="step">
                <h4>步骤4：检查控制台</h4>
                <ol>
                    <li>打开Chrome开发者工具的控制台</li>
                    <li>查看详细的调试信息</li>
                    <li>确认窗口和标签页切换日志</li>
                </ol>
            </div>
        </div>

        <div class="fix-section">
            <h2>📋 预期控制台输出</h2>
            <div class="code-fix">
✅ 正常的跨窗口操作应该显示：
- "总标签页数量: X"
- "找到Midjourney标签页: 1"
- "Midjourney标签页1: { id: XXX, windowId: YYY, url: ..., active: true }"
- "标签页信息: { id: XXX, windowId: YYY, url: ... }"
- "成功切换到窗口: YYY"
- "成功切换到标签页: XXX"
- "发送prompt到标签页: XXX ..."

❌ 如果出现错误：
- "切换标签页失败 (尝试 1/3): ..."
- "Tabs cannot be edited"
- "No tab with id: XXX"
            </div>
        </div>

        <div class="fix-section">
            <h2>🎯 成功标准</h2>
            <div class="status status-success">
                <strong>跨窗口功能正常的标志：</strong><br>
                ✅ 能够检测到不同窗口中的Midjourney标签页<br>
                ✅ 能够正确切换到目标窗口<br>
                ✅ 能够激活目标标签页<br>
                ✅ 能够成功发送prompt<br>
                ✅ 控制台显示详细的操作日志<br>
                ✅ 无论标签页在哪个窗口都能正常工作
            </div>
        </div>

        <div class="test-section">
            <h2>🚀 故障排除</h2>
            <p>如果跨窗口功能仍然失败：</p>
            <ol>
                <li><strong>检查权限</strong> - 确保扩展有访问所有标签页的权限</li>
                <li><strong>重启Chrome</strong> - 有时需要重启浏览器使权限生效</li>
                <li><strong>检查弹窗阻止</strong> - 确保没有弹窗阻止器干扰</li>
                <li><strong>查看控制台</strong> - 检查具体的错误信息</li>
                <li><strong>手动测试</strong> - 先手动切换窗口，再测试插件</li>
            </ol>
        </div>
    </div>

    <script>
        // 页面加载时显示状态
        window.onload = function() {
            setTimeout(() => {
                console.log('🪟 跨窗口功能测试页面已加载');
                console.log('现在插件应该能正确处理分屏的Midjourney窗口了！');
                console.log('请按照测试步骤验证跨窗口功能');
            }, 1000);
        };
    </script>
</body>
</html>
