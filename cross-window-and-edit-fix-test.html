<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>跨窗口发送和编辑内容修复测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #e0f2fe 0%, #b3e5fc 100%);
            color: #333;
            min-height: 100vh;
        }
        .container {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 10px 40px rgba(0,0,0,0.1);
            border: 1px solid rgba(0,0,0,0.05);
        }
        h1 {
            color: #0277bd;
            text-align: center;
            margin-bottom: 40px;
            font-size: 3em;
            font-weight: 700;
        }
        .feature-section {
            background: #f1f8e9;
            border: 1px solid #c8e6c9;
            padding: 30px;
            margin: 30px 0;
            border-radius: 12px;
        }
        .problem-demo {
            background: linear-gradient(135deg, #fef2f2, #fee2e2);
            border: 2px solid #ef4444;
            border-radius: 12px;
            padding: 20px;
            margin: 20px 0;
        }
        .solution-demo {
            background: linear-gradient(135deg, #f0fdf4, #dcfce7);
            border: 2px solid #22c55e;
            border-radius: 12px;
            padding: 20px;
            margin: 20px 0;
        }
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 16px rgba(0,0,0,0.05);
        }
        .comparison-table th,
        .comparison-table td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid #e2e8f0;
        }
        .comparison-table th {
            background: #0277bd;
            color: white;
            font-weight: 600;
        }
        .problem {
            color: #ef4444;
            font-weight: 600;
        }
        .solution {
            color: #22c55e;
            font-weight: 600;
        }
        .code-block {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 16px;
            font-family: 'SF Mono', Monaco, monospace;
            font-size: 14px;
            line-height: 1.5;
            margin: 15px 0;
            overflow-x: auto;
        }
        .highlight-box {
            background: linear-gradient(135deg, #e1f5fe, #b3e5fc);
            border: 2px solid #0277bd;
            border-radius: 12px;
            padding: 25px;
            margin: 25px 0;
            text-align: center;
        }
        .workflow-steps {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        .step {
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 4px 16px rgba(0,0,0,0.05);
            text-align: center;
            border-left: 4px solid #0277bd;
        }
        .step-number {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #0277bd, #0288d1);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 15px;
            font-weight: bold;
            font-size: 18px;
        }
        .demo-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin: 30px 0;
        }
        .demo-block {
            background: white;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 4px 16px rgba(0,0,0,0.05);
            border: 1px solid #e2e8f0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 跨窗口发送和编辑内容修复</h1>
        
        <div class="feature-section">
            <h2>🎯 双重问题修复</h2>
            <p><strong>1. 修复了Midjourney标签页拖到独立窗口后无法发送的问题<br>2. 修复了修改文本块后发送的还是原始内容的问题</strong></p>
        </div>

        <div class="demo-grid">
            <div class="problem-demo">
                <h3>❌ 问题1：跨窗口发送失败</h3>
                <ul>
                    <li>Midjourney标签页拖到新窗口后无法发送</li>
                    <li>Content script没有在新窗口中加载</li>
                    <li>缺少Discord域名的权限配置</li>
                    <li>跨窗口通信受Chrome安全限制</li>
                </ul>
            </div>

            <div class="problem-demo">
                <h3>❌ 问题2：编辑内容不生效</h3>
                <ul>
                    <li>修改文本块后发送的还是原始内容</li>
                    <li>内容获取方法不正确</li>
                    <li>修改检测逻辑有问题</li>
                    <li>用户编辑没有被正确识别</li>
                </ul>
            </div>
        </div>

        <div class="demo-grid">
            <div class="solution-demo">
                <h3>✅ 解决方案1：动态注入Content Script</h3>
                <ul>
                    <li>添加Discord域名到manifest权限</li>
                    <li>实现动态content script注入</li>
                    <li>发送失败时自动重试注入</li>
                    <li>支持所有窗口的Midjourney页面</li>
                </ul>
            </div>

            <div class="solution-demo">
                <h3>✅ 解决方案2：正确的内容获取</h3>
                <ul>
                    <li>使用innerText获取用户编辑的内容</li>
                    <li>修正内容检测和发送逻辑</li>
                    <li>添加调试日志便于排查</li>
                    <li>确保编辑内容正确发送</li>
                </ul>
            </div>
        </div>

        <div class="feature-section">
            <h2>🔧 技术修复详情</h2>
            <table class="comparison-table">
                <thead>
                    <tr>
                        <th>问题</th>
                        <th>原因</th>
                        <th>解决方案</th>
                        <th>效果</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><strong>跨窗口发送</strong></td>
                        <td class="problem">Content script未加载</td>
                        <td class="solution">动态注入 + 重试机制</td>
                        <td>支持所有窗口</td>
                    </tr>
                    <tr>
                        <td><strong>Discord支持</strong></td>
                        <td class="problem">缺少域名权限</td>
                        <td class="solution">添加discord.com权限</td>
                        <td>支持Discord Midjourney</td>
                    </tr>
                    <tr>
                        <td><strong>内容获取</strong></td>
                        <td class="problem">textContent顺序错误</td>
                        <td class="solution">优先使用innerText</td>
                        <td>获取用户编辑内容</td>
                    </tr>
                    <tr>
                        <td><strong>修改检测</strong></td>
                        <td class="problem">innerHTML比较不准确</td>
                        <td class="solution">使用innerText比较</td>
                        <td>准确检测文本修改</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="feature-section">
            <h2>📝 关键代码修复</h2>
            
            <h3>1. Manifest权限扩展：</h3>
            <div class="code-block">
"host_permissions": [
    "https://claude.ai/*",
    "https://www.midjourney.com/*",
    "https://discord.com/*"  // 新增Discord支持
],
"content_scripts": [
    {
        "matches": [
            "https://www.midjourney.com/*", 
            "https://discord.com/*"  // 新增Discord匹配
        ],
        "js": ["midjourney-sender.js"]
    }
]
            </div>
            
            <h3>2. 动态Content Script注入：</h3>
            <div class="code-block">
async sendToMidjourneyTab(tabId, prompt) {
    try {
        // 首先尝试直接发送
        const response = await chrome.tabs.sendMessage(tabId, {
            action: 'sendSinglePrompt',
            prompt: prompt
        });
        return { success: true, originalResponse: response };
    } catch (messageError) {
        // 发送失败，注入content script后重试
        await this.ensureContentScriptInjected(tabId);
        await new Promise(resolve => setTimeout(resolve, 500));
        
        const response = await chrome.tabs.sendMessage(tabId, {
            action: 'sendSinglePrompt',
            prompt: prompt
        });
        return { success: true, originalResponse: response };
    }
}
            </div>
            
            <h3>3. 正确的内容获取：</h3>
            <div class="code-block">
// M按钮点击事件 - 获取用户编辑的内容
const currentText = promptElement.innerText || promptElement.textContent;

// 修改检测 - 比较用户实际编辑的文本
const currentContent = promptElement.innerText || promptElement.textContent;
const originalContent = promptElement.dataset.originalContent;
            </div>
        </div>

        <div class="feature-section">
            <h2>🔄 修复流程</h2>
            <div class="workflow-steps">
                <div class="step">
                    <div class="step-number">1</div>
                    <h4>权限扩展</h4>
                    <p>添加Discord域名权限和匹配规则</p>
                </div>
                
                <div class="step">
                    <div class="step-number">2</div>
                    <h4>动态注入</h4>
                    <p>发送失败时自动注入content script</p>
                </div>
                
                <div class="step">
                    <div class="step-number">3</div>
                    <h4>内容修正</h4>
                    <p>使用innerText获取用户编辑内容</p>
                </div>
                
                <div class="step">
                    <div class="step-number">4</div>
                    <h4>检测优化</h4>
                    <p>修正修改检测和发送逻辑</p>
                </div>
            </div>
        </div>

        <div class="highlight-box">
            <h2>✨ 修复效果</h2>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-top: 20px;">
                <div>
                    <h4>🌐 跨窗口支持</h4>
                    <p>Midjourney标签页可以拖到任何窗口</p>
                </div>
                <div>
                    <h4>🔄 自动恢复</h4>
                    <p>发送失败时自动注入并重试</p>
                </div>
                <div>
                    <h4>📝 编辑生效</h4>
                    <p>修改的内容会正确发送到Midjourney</p>
                </div>
                <div>
                    <h4>🎯 准确检测</h4>
                    <p>修改检测更加准确和可靠</p>
                </div>
            </div>
        </div>

        <div class="feature-section">
            <h2>🧪 测试建议</h2>
            <p style="font-size: 18px; line-height: 1.8;">
                修复完成后，建议进行以下测试：
            </p>
            <ol style="font-size: 16px; line-height: 1.8;">
                <li><strong>跨窗口测试：</strong>将Midjourney标签页拖到新窗口，测试发送功能</li>
                <li><strong>Discord测试：</strong>在Discord的Midjourney频道中测试发送</li>
                <li><strong>编辑测试：</strong>修改prompt文本后测试是否发送修改后的内容</li>
                <li><strong>修改检测：</strong>验证修改后是否正确显示恢复按钮</li>
                <li><strong>恢复测试：</strong>测试恢复按钮是否能正确还原原始内容</li>
            </ol>
            
            <p style="background: rgba(2, 119, 189, 0.1); padding: 15px; border-radius: 8px; margin-top: 20px;">
                <strong>💡 修复完成：</strong>现在插件支持跨窗口发送，并且能正确发送用户编辑后的内容！记得重新加载插件以应用这些修复。
            </p>
        </div>
    </div>
</body>
</html>
