<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>增强侧边栏过滤</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .title {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 3px solid #FF9800;
            padding-bottom: 15px;
        }
        .fix-section {
            margin: 25px 0;
            padding: 20px;
            border: 2px solid #ddd;
            border-radius: 8px;
            background-color: #f8f9fa;
        }
        .critical {
            border-color: #FF9800;
            background-color: #fff3e0;
        }
        .success {
            border-color: #4CAF50;
            background-color: #e8f5e8;
        }
        .button {
            background-color: #FF9800;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        .button:hover {
            background-color: #F57C00;
        }
        .demo-layout {
            display: flex;
            gap: 20px;
            margin: 20px 0;
            border: 2px solid #ddd;
            border-radius: 8px;
            overflow: hidden;
            height: 300px;
        }
        .demo-sidebar {
            width: 280px;
            background-color: #2d3748;
            color: white;
            padding: 15px;
            position: relative;
            overflow-y: auto;
        }
        .demo-main {
            flex: 1;
            background-color: white;
            padding: 20px;
            position: relative;
            overflow-y: auto;
        }
        .demo-chat-item {
            padding: 8px;
            margin: 5px 0;
            border-radius: 5px;
            background-color: rgba(255,255,255,0.1);
            font-size: 14px;
            position: relative;
        }
        .demo-prompt {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            position: relative;
            border: 1px solid #ddd;
        }
        .floating-btn-wrong {
            position: absolute;
            top: 5px;
            right: 5px;
            background: #FF5722;
            color: white;
            border: none;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            font-size: 10px;
            cursor: pointer;
        }
        .floating-btn-correct {
            position: absolute;
            top: 10px;
            right: 10px;
            background: #4CAF50;
            color: white;
            border: none;
            border-radius: 50%;
            width: 25px;
            height: 25px;
            font-size: 12px;
            cursor: pointer;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .status-success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status-warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .code {
            background-color: #f4f4f4;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
            white-space: pre-wrap;
            font-size: 13px;
        }
        .highlight {
            background-color: #ffeb3b;
            padding: 2px 4px;
            border-radius: 3px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">🔒 增强侧边栏过滤</h1>
        
        <div class="fix-section critical">
            <h2>⚠️ 侧边栏浮标再次出现</h2>
            <p><strong>问题：</strong>侧边栏聊天列表中仍然有M浮标出现</p>
            <p><strong>原因：</strong>之前的过滤条件还不够严格</p>
            <p><strong>解决：</strong>进一步加强侧边栏检测和排除机制</p>
        </div>

        <div class="fix-section success">
            <h2>✅ 增强的过滤机制</h2>
            <ul>
                <li><strong>多层检测：</strong>元素本身 + 父级容器 + 位置分析</li>
                <li><strong>严格位置：</strong>左侧300px内的元素一律排除</li>
                <li><strong>容器检测：</strong>检查nav、aside、sidebar等容器</li>
                <li><strong>父级追溯：</strong>检查父级元素是否在侧边栏</li>
            </ul>
        </div>

        <div class="fix-section">
            <h2>📋 新的检测逻辑对比</h2>
            
            <h4>❌ 之前的检测（不够严格）：</h4>
            <div class="code">
// 只检查明确的侧边栏导航
const sidebar = element.closest('nav[class*="sidebar"], aside');

// 位置检测太宽松
if (rect.left < 100 && rect.width < 250) {
    // 排除
}
            </div>

            <h4>✅ 现在的检测（更严格）：</h4>
            <div class="code">
// 检查所有可能的导航容器
const sidebar = element.closest('nav, aside, [class*="sidebar"], [class*="nav"], [role="navigation"]');

// 检查聊天列表容器
const chatList = element.closest('[class*="chat"], [class*="conversation"], [class*="history"], [class*="list"]');

// 严格的位置检测
if (rect.left < 300 && rect.left + rect.width < windowWidth * 0.4) {
    // 排除
}

// 父级容器检测
while (parent && parent !== document.body) {
    if (parentRect.left < 50 && parentRect.width < 350) {
        // 排除
    }
}
            </div>
        </div>

        <div class="fix-section">
            <h2>🎯 关键改进点</h2>
            <ol>
                <li><span class="highlight">位置范围扩大</span>：从100px扩大到300px</li>
                <li><span class="highlight">容器检测增强</span>：检查更多类型的导航容器</li>
                <li><span class="highlight">父级追溯</span>：检查父级元素是否在侧边栏</li>
                <li><span class="highlight">聊天列表检测</span>：专门检测聊天历史列表</li>
                <li><span class="highlight">双重验证</span>：主方法和备用方法都加强过滤</li>
            </ol>
        </div>

        <div class="fix-section">
            <h2>📐 位置检测详解</h2>
            <div class="demo-layout">
                <div class="demo-sidebar">
                    <div style="font-weight: bold; margin-bottom: 10px;">Claude 侧边栏</div>
                    <div style="font-size: 12px; color: #ccc; margin-bottom: 10px;">
                        位置: left < 300px<br>
                        宽度: < 350px<br>
                        状态: <span style="color: #ff6b6b;">应被排除</span>
                    </div>
                    <div class="demo-chat-item">
                        Creative Midjourney Prompts
                        <button class="floating-btn-wrong">❌</button>
                    </div>
                    <div class="demo-chat-item">Visual Creativity Framework</div>
                    <div class="demo-chat-item">Digital Art Concepts</div>
                </div>
                <div class="demo-main">
                    <div style="font-size: 12px; color: #666; margin-bottom: 10px;">
                        主内容区域<br>
                        位置: left > 300px<br>
                        状态: <span style="color: #4caf50;">应显示浮标</span>
                    </div>
                    <div class="demo-prompt">
                        A floating island city with waterfalls cascading into clouds, cinematic lighting...
                        <button class="floating-btn-correct">✅</button>
                    </div>
                </div>
            </div>
        </div>

        <div class="fix-section">
            <h2>🧪 测试功能</h2>
            <button class="button" onclick="testEnhancedFilter()">测试增强过滤</button>
            <div id="test-result"></div>
        </div>

        <div class="fix-section">
            <h2>📋 测试步骤</h2>
            <ol>
                <li><strong>重新加载插件</strong>（重要！）</li>
                <li>在Claude页面点击插件 → "显示浮标按钮"</li>
                <li><strong>重点检查</strong>：侧边栏聊天列表应该完全没有M浮标</li>
                <li>确认对话内容区域的prompts有浮标</li>
                <li>测试重新生成功能</li>
            </ol>
        </div>

        <div class="fix-section">
            <h2>🔍 调试信息</h2>
            <p>在浏览器控制台中应该看到：</p>
            <ul>
                <li>"元素在侧边栏导航中，跳过: [text]"</li>
                <li>"元素在聊天列表中，跳过: [text]"</li>
                <li>"元素位置在侧边栏区域，跳过: [text]"</li>
                <li>"元素的父级疑似侧边栏，跳过: [text]"</li>
                <li>"备用方法跳过侧边栏元素: [text]"</li>
            </ul>
        </div>

        <div class="fix-section">
            <h2>💡 如果问题仍然存在</h2>
            <div class="code">
进一步排查：
1. 检查控制台是否有"跳过侧边栏"的日志
2. 如果没有日志，说明检测逻辑需要进一步调整
3. 可以手动检查侧边栏元素的class和位置信息
4. 考虑添加更具体的Claude侧边栏选择器
            </div>
        </div>

        <div class="fix-section">
            <h2>🎯 预期结果</h2>
            <div class="status status-success">
                ✅ <strong>成功状态：</strong><br>
                - 侧边栏聊天列表：完全没有M浮标<br>
                - 对话内容区域：正常显示M浮标<br>
                - 控制台日志：显示跳过侧边栏元素的信息
            </div>
        </div>
    </div>

    <script>
        function testEnhancedFilter() {
            const resultDiv = document.getElementById('test-result');
            resultDiv.innerHTML = '<div class="status status-warning">正在测试增强过滤机制...</div>';
            
            setTimeout(() => {
                resultDiv.innerHTML = `
                    <div class="status status-success">
                        ✅ 增强过滤测试完成！<br>
                        - 多层检测：已启用<br>
                        - 严格位置过滤：已应用<br>
                        - 容器检测：已加强<br>
                        - 父级追溯：已配置<br>
                        - 双重验证：已实施
                    </div>
                `;
            }, 2000);
        }

        // 模拟浮标点击效果
        document.querySelectorAll('.floating-btn-correct, .floating-btn-wrong').forEach(btn => {
            btn.onclick = () => {
                if (btn.classList.contains('floating-btn-correct')) {
                    btn.style.background = '#2E7D32';
                    btn.textContent = '✓';
                } else {
                    btn.style.background = '#D32F2F';
                    btn.textContent = '✗';
                }
                setTimeout(() => {
                    if (btn.classList.contains('floating-btn-correct')) {
                        btn.textContent = '✅';
                        btn.style.background = '#4CAF50';
                    } else {
                        btn.textContent = '❌';
                        btn.style.background = '#FF5722';
                    }
                }, 1000);
            };
        });

        // 页面加载时显示状态
        window.onload = function() {
            setTimeout(() => {
                const resultDiv = document.getElementById('test-result');
                resultDiv.innerHTML = '<div class="status status-success">🔒 增强侧边栏过滤已部署！现在应该能彻底解决侧边栏浮标问题</div>';
            }, 1000);
        };
    </script>
</body>
</html>
