// background.js - 插件的后台服务工作者

class BackgroundService {
    constructor() {
        this.init();
    }

    init() {
        // 监听插件安装
        chrome.runtime.onInstalled.addListener((details) => {
            if (details.reason === 'install') {
                console.log('Claude to Midjourney Assistant 插件已安装');
                this.showWelcomeNotification();
            }
        });

        // 监听来自content scripts的消息
        chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
            this.handleMessage(request, sender, sendResponse);
            return true; // 保持消息通道开放
        });

        // 监听标签页更新
        chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
            this.handleTabUpdate(tabId, changeInfo, tab);
        });

        // 监听快捷键命令
        chrome.commands.onCommand.addListener((command) => {
            this.handleCommand(command);
        });
    }

    showWelcomeNotification() {
        // 显示欢迎通知
        if (chrome.notifications) {
            chrome.notifications.create({
                type: 'basic',
                iconUrl: 'icons/icon48.png',
                title: 'Claude to Midjourney Assistant',
                message: '插件安装成功！现在可以自动提取Claude中的prompts并发送到Midjourney了。'
            });
        }
    }

    handleMessage(request, sender, sendResponse) {
        switch (request.action) {
            case 'getStoredPrompts':
                this.getStoredPrompts().then(sendResponse);
                break;
            case 'storePrompts':
                this.storePrompts(request.prompts).then(sendResponse);
                break;
            case 'clearStoredPrompts':
                this.clearStoredPrompts().then(sendResponse);
                break;
            case 'logActivity':
                this.logActivity(request.activity);
                break;
            case 'findMidjourneyTabs':
                this.findMidjourneyTabs().then(tabs => {
                    sendResponse({ success: true, tabs });
                }).catch(error => {
                    sendResponse({ success: false, error: error.message });
                });
                break;
            case 'sendToMidjourneyTab':
                this.sendToMidjourneyTab(request.tabId, request.prompt).then(result => {
                    sendResponse(result);
                }).catch(error => {
                    sendResponse({ success: false, error: error.message });
                });
                break;
            case 'openMidjourneyPage':
                this.openMidjourneyPage(request.url).then(tab => {
                    sendResponse({ success: true, tab });
                }).catch(error => {
                    sendResponse({ success: false, error: error.message });
                });
                break;
            case 'switchToTab':
                this.switchToTab(request.tabId).then(() => {
                    sendResponse({ success: true });
                }).catch(error => {
                    sendResponse({ success: false, error: error.message });
                });
                break;

            default:
                sendResponse({ error: 'Unknown action' });
        }
    }

    async getStoredPrompts() {
        try {
            const result = await chrome.storage.local.get(['cachedPrompts', 'pendingPrompts']);
            return {
                success: true,
                cachedPrompts: result.cachedPrompts || [],
                pendingPrompts: result.pendingPrompts || []
            };
        } catch (error) {
            console.error('获取存储的prompts失败:', error);
            return { success: false, error: error.message };
        }
    }

    async storePrompts(prompts) {
        try {
            await chrome.storage.local.set({
                cachedPrompts: prompts,
                lastUpdated: Date.now()
            });
            return { success: true };
        } catch (error) {
            console.error('存储prompts失败:', error);
            return { success: false, error: error.message };
        }
    }

    async clearStoredPrompts() {
        try {
            await chrome.storage.local.remove(['cachedPrompts', 'pendingPrompts', 'currentPromptIndex']);
            return { success: true };
        } catch (error) {
            console.error('清除存储的prompts失败:', error);
            return { success: false, error: error.message };
        }
    }

    logActivity(activity) {
        // 记录活动日志
        console.log(`[${new Date().toISOString()}] ${activity}`);
        
        // 可以选择存储到chrome.storage.local用于调试
        chrome.storage.local.get(['activityLog']).then(result => {
            const log = result.activityLog || [];
            log.push({
                timestamp: Date.now(),
                activity: activity
            });
            
            // 只保留最近100条记录
            if (log.length > 100) {
                log.splice(0, log.length - 100);
            }
            
            chrome.storage.local.set({ activityLog: log });
        });
    }

    async findMidjourneyTabs() {
        try {
            // 查找所有窗口中的所有标签页，不限制当前窗口
            const allTabs = await chrome.tabs.query({
                // 不指定windowId，这样会查找所有窗口的标签页
            });
            console.log('总标签页数量（所有窗口）:', allTabs.length);

            // 获取所有窗口信息用于调试
            const allWindows = await chrome.windows.getAll();
            console.log('总窗口数量:', allWindows.length);
            allWindows.forEach((window, index) => {
                console.log(`窗口${index + 1}:`, {
                    id: window.id,
                    focused: window.focused,
                    type: window.type,
                    state: window.state
                });
            });

            const midjourneyTabs = allTabs.filter(tab =>
                tab.url && (
                    tab.url.includes('midjourney.com') ||
                    tab.url.includes('discord.com') ||
                    tab.title.toLowerCase().includes('midjourney')
                )
            );

            console.log('找到Midjourney标签页（所有窗口）:', midjourneyTabs.length);

            // 详细记录每个找到的标签页信息
            midjourneyTabs.forEach((tab, index) => {
                console.log(`Midjourney标签页${index + 1}:`, {
                    id: tab.id,
                    windowId: tab.windowId,
                    url: tab.url,
                    title: tab.title,
                    active: tab.active,
                    windowType: 'unknown' // 我们稍后会获取窗口类型
                });
            });

            return midjourneyTabs;
        } catch (error) {
            console.error('查找标签页失败:', error);
            throw error;
        }
    }

    async sendToMidjourneyTab(tabId, prompt) {
        try {
            console.log('后台发送prompt到标签页:', tabId, prompt.substring(0, 50) + '...');

            // 首先尝试直接发送消息
            try {
                const response = await chrome.tabs.sendMessage(tabId, {
                    action: 'sendSinglePrompt',
                    prompt: prompt
                });
                console.log('Prompt后台发送成功，保持当前页面');
                return { success: true, message: '已后台发送到Midjourney', originalResponse: response };
            } catch (messageError) {
                console.log('直接发送失败，尝试注入content script:', messageError.message);

                // 如果发送失败，可能是content script没有加载，尝试注入
                await this.ensureContentScriptInjected(tabId);

                // 等待一下让content script初始化
                await new Promise(resolve => setTimeout(resolve, 500));

                // 重新尝试发送
                const response = await chrome.tabs.sendMessage(tabId, {
                    action: 'sendSinglePrompt',
                    prompt: prompt
                });

                console.log('注入content script后发送成功');
                return { success: true, message: '已后台发送到Midjourney', originalResponse: response };
            }
        } catch (error) {
            console.error('后台发送到标签页失败:', error);

            // 如果是标签页不存在或无法访问，提供更友好的错误信息
            if (error.message && (
                error.message.includes('No tab with id') ||
                error.message.includes('Could not establish connection')
            )) {
                console.log('标签页不可访问，可能需要刷新Midjourney页面');
                throw new Error('无法连接到Midjourney页面，请确保页面已打开并刷新后重试');
            }

            throw new Error('后台发送失败: ' + error.message);
        }
    }

    async ensureContentScriptInjected(tabId) {
        try {
            // 获取标签页信息
            const tab = await chrome.tabs.get(tabId);
            console.log('检查标签页:', tab.url);

            // 检查是否是Midjourney相关页面
            if (tab.url.includes('midjourney.com') || tab.url.includes('discord.com')) {
                console.log('注入content script到标签页:', tabId);
                await chrome.scripting.executeScript({
                    target: { tabId: tabId },
                    files: ['midjourney-sender.js']
                });
                console.log('Content script注入成功');
            }
        } catch (error) {
            console.error('注入content script失败:', error);
            throw error;
        }
    }

    async switchToTabWithRetry(tabId, maxRetries = 3) {
        for (let i = 0; i < maxRetries; i++) {
            try {
                // 首先获取标签页信息，确定它在哪个窗口
                const tab = await chrome.tabs.get(tabId);
                console.log('标签页信息:', { id: tab.id, windowId: tab.windowId, url: tab.url });

                // 先切换到正确的窗口
                await chrome.windows.update(tab.windowId, { focused: true });
                console.log('成功切换到窗口:', tab.windowId);

                // 等待窗口切换完成
                await new Promise(resolve => setTimeout(resolve, 200));

                // 然后激活标签页
                await chrome.tabs.update(tabId, { active: true });
                console.log('成功切换到标签页:', tabId);
                return;
            } catch (error) {
                console.log(`切换标签页失败 (尝试 ${i + 1}/${maxRetries}):`, error.message);

                if (i === maxRetries - 1) {
                    // 最后一次尝试失败，检查是否是标签页编辑错误
                    if (error.message && error.message.includes('Tabs cannot be edited')) {
                        console.log('标签页正在被编辑，跳过切换');
                        return; // 不抛出错误，继续执行
                    }
                    throw error;
                }

                // 等待一段时间后重试
                await new Promise(resolve => setTimeout(resolve, 1000 * (i + 1)));
            }
        }
    }

    async openMidjourneyPage(url = 'https://www.midjourney.com/imagine') {
        try {
            const tab = await chrome.tabs.create({
                url: url,
                active: true
            });

            console.log('已打开新的Midjourney页面:', tab.id);
            return tab;
        } catch (error) {
            console.error('打开页面失败:', error);
            throw error;
        }
    }

    async switchToTab(tabId) {
        try {
            await chrome.tabs.update(tabId, { active: true });
            console.log('已切换到标签页:', tabId);
        } catch (error) {
            console.error('切换标签页失败:', error);
            throw error;
        }
    }

    handleTabUpdate(tabId, changeInfo, tab) {
        // 当标签页完成加载时
        if (changeInfo.status === 'complete' && tab.url) {
            if (tab.url.includes('claude.ai')) {
                // Claude页面加载完成
                this.logActivity('Claude页面加载完成');
            } else if (tab.url.includes('midjourney.com')) {
                // Midjourney网页版加载完成
                this.logActivity('Midjourney网页版加载完成');
            }
        }
    }

    async handleCommand(command) {
        try {
            const [activeTab] = await chrome.tabs.query({ active: true, currentWindow: true });

            if (!activeTab || !activeTab.url.includes('claude.ai')) {
                console.log('快捷键只在Claude页面有效');
                return;
            }

            switch (command) {
                case 'manual-detect':
                    await this.manualDetect(activeTab.id);
                    break;
                default:
                    console.log('未知的快捷键命令:', command);
            }
        } catch (error) {
            console.error('处理快捷键命令失败:', error);
        }
    }



    async manualDetect(tabId) {
        try {
            // 发送手动检测命令到content script
            await chrome.tabs.sendMessage(tabId, {
                action: 'manualDetect'
            });

            console.log('已触发手动检测');
        } catch (error) {
            console.error('手动检测失败:', error);
        }
    }


}

// 初始化后台服务
new BackgroundService();
