<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>稳定性修复测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .title {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 3px solid #FF9800;
            padding-bottom: 15px;
        }
        .fix-section {
            margin: 25px 0;
            padding: 20px;
            border: 2px solid #4CAF50;
            border-radius: 8px;
            background-color: #e8f5e8;
        }
        .problem-section {
            margin: 25px 0;
            padding: 20px;
            border: 2px solid #FF9800;
            border-radius: 8px;
            background-color: #fff3e0;
        }
        .test-section {
            margin: 25px 0;
            padding: 20px;
            border: 2px solid #17a2b8;
            border-radius: 8px;
            background-color: #d1ecf1;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .status-success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status-warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .code-fix {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            overflow-x: auto;
        }
        .test-block {
            margin: 15px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #f8f9fa;
            font-family: monospace;
            position: relative;
        }
        .step {
            background: #e9ecef;
            border-left: 4px solid #FF9800;
            padding: 15px;
            margin: 10px 0;
        }
        .state-demo {
            display: inline-flex;
            align-items: center;
            gap: 10px;
            margin: 10px;
            padding: 10px;
            border-radius: 5px;
            background: #f8f9fa;
        }
        .demo-button {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }
        .normal-state { background: linear-gradient(135deg, #7C3AED, #3B82F6); }
        .loading-state { background: linear-gradient(135deg, #FF9800, #F57C00); }
        .success-state { background: linear-gradient(135deg, #4CAF50, #388E3C); }
        .error-state { background: linear-gradient(135deg, #f44336, #d32f2f); }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">🔧 按钮状态稳定性修复</h1>
        
        <div class="problem-section">
            <h2>⚠️ 原始问题</h2>
            <div class="status status-warning">
                <strong>按钮状态异常：</strong><br>
                有时候按钮会卡在黄色加载状态，没有显示绿色勾号就直接结束了
            </div>
            
            <h3>🔍 问题原因分析：</h3>
            <ul>
                <li>❌ <strong>连接超时</strong> - "Could not establish connection" 错误</li>
                <li>❌ <strong>异步异常</strong> - Promise 没有正确处理错误</li>
                <li>❌ <strong>缺少超时保护</strong> - 按钮可能永远卡在加载状态</li>
                <li>❌ <strong>错误处理不完整</strong> - 某些错误没有被捕获</li>
                <li>❌ <strong>重试机制缺失</strong> - 网络波动时没有重试</li>
            </ul>
        </div>

        <div class="fix-section">
            <h2>✅ 修复方案</h2>
            
            <h3>🛡️ 1. 超时保护机制：</h3>
            <div class="code-fix">
// 设置超时保护，防止按钮卡在加载状态
const timeoutId = setTimeout(() => {
    console.warn('⚠️ 发送超时，恢复按钮状态');
    this.showError(buttonElement, '发送超时，请重试');
}, 10000); // 10秒超时

try {
    const response = await Promise.race([
        this.safeChromeMessage({...}),
        new Promise((_, reject) => 
            setTimeout(() => reject(new Error('发送超时')), 8000)
        )
    ]);
    clearTimeout(timeoutId); // 成功时清除超时
} catch (error) {
    clearTimeout(timeoutId); // 错误时也要清除超时
}
            </div>

            <h3>🔄 2. 重试机制：</h3>
            <div class="code-fix">
async safeChromeMessage(message, maxRetries = 2) {
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
        try {
            const response = await new Promise((resolve, reject) => {
                const timeout = setTimeout(() => {
                    reject(new Error('消息发送超时'));
                }, 5000);
                
                chrome.runtime.sendMessage(message, (response) => {
                    clearTimeout(timeout);
                    if (chrome.runtime.lastError) {
                        reject(new Error(chrome.runtime.lastError.message));
                    } else {
                        resolve(response);
                    }
                });
            });
            return response;
        } catch (error) {
            if (attempt === maxRetries) {
                return { success: false, error: error.message };
            }
            await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
        }
    }
}
            </div>

            <h3>🎯 3. 增强错误处理：</h3>
            <div class="code-fix">
// 针对常见错误的特定处理
if (error.message.includes('Could not establish connection')) {
    errorMessage = 'Midjourney页面需要刷新';
} else if (error.message.includes('发送超时')) {
    errorMessage = '发送超时，请重试';
} else if (error.message.includes('Extension context invalidated')) {
    errorMessage = '扩展需要重新加载';
}
            </div>
        </div>

        <div class="test-section">
            <h2>🎭 按钮状态演示</h2>
            <p>正常的按钮状态变化流程：</p>
            
            <div class="state-demo">
                <div class="demo-button normal-state">
                    <svg width="18" height="18" viewBox="0 0 24 24" fill="white">
                        <path d="M3,21V3H7L12,14L17,3H21V21H17V8L13,18H11L7,8V21H3Z"/>
                    </svg>
                </div>
                <span><strong>1. 初始状态</strong> - 紫色M图标</span>
            </div>

            <div class="state-demo">
                <div class="demo-button loading-state">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="white">
                        <circle cx="12" cy="12" r="10" stroke="white" stroke-width="2" fill="none"/>
                    </svg>
                </div>
                <span><strong>2. 发送中</strong> - 橙色加载动画（最多10秒）</span>
            </div>

            <div class="state-demo">
                <div class="demo-button success-state">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="white">
                        <path d="M9,20.42L2.79,14.21L5.62,11.38L9,14.77L18.88,4.88L21.71,7.71L9,20.42Z"/>
                    </svg>
                </div>
                <span><strong>3. 成功</strong> - 绿色勾号（3秒后恢复）</span>
            </div>

            <div class="state-demo">
                <div class="demo-button error-state">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="white">
                        <path d="M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z"/>
                    </svg>
                </div>
                <span><strong>4. 错误</strong> - 红色X图标（3秒后恢复）</span>
            </div>
        </div>

        <div class="test-section">
            <h2>🧪 测试代码块</h2>
            <p>使用这些代码块进行稳定性测试：</p>
            
            <h4>测试代码块1 - 简单prompt：</h4>
            <pre class="test-block">Beautiful sunset over ocean waves, golden hour lighting, peaceful seascape --ar 16:9</pre>

            <h4>测试代码块2 - 复杂prompt：</h4>
            <pre class="test-block">Futuristic cyberpunk cityscape at night, neon lights reflecting on wet streets, flying cars between towering skyscrapers, holographic advertisements, rain, atmospheric fog, purple and blue color palette, cinematic lighting --ar 21:9 --v 6</pre>

            <h4>测试代码块3 - 中等prompt：</h4>
            <pre class="test-block">Ancient forest with mystical glowing mushrooms, fairy lights dancing between trees, magical atmosphere, fantasy art style --ar 4:3</pre>
        </div>

        <div class="test-section">
            <h2>🔍 稳定性测试步骤</h2>
            
            <div class="step">
                <h4>步骤1：重新加载插件</h4>
                <p>在Chrome扩展页面刷新插件，确保修复生效</p>
            </div>

            <div class="step">
                <h4>步骤2：批量测试</h4>
                <ol>
                    <li>连续点击多个M按钮（快速点击）</li>
                    <li>观察每个按钮是否都能正确显示状态</li>
                    <li>确认没有按钮卡在黄色状态</li>
                    <li>验证成功时显示绿色勾号</li>
                </ol>
            </div>

            <div class="step">
                <h4>步骤3：网络异常测试</h4>
                <ol>
                    <li>暂时关闭Midjourney页面</li>
                    <li>点击M按钮，应该显示错误状态</li>
                    <li>重新打开Midjourney页面</li>
                    <li>再次测试，应该恢复正常</li>
                </ol>
            </div>

            <div class="step">
                <h4>步骤4：超时测试</h4>
                <ol>
                    <li>在网络较慢的情况下测试</li>
                    <li>观察是否在10秒内有响应</li>
                    <li>超时时应该显示错误信息</li>
                    <li>不应该永远卡在加载状态</li>
                </ol>
            </div>
        </div>

        <div class="fix-section">
            <h2>📋 预期控制台输出</h2>
            <div class="code-fix">
✅ 正常流程：
- "🔄 尝试发送消息 (1/2): sendToMidjourneyTab"
- "✅ 消息发送成功 (尝试 1): { success: true, ... }"
- "✅ 检测到成功响应，显示绿色勾号"

⚠️ 重试流程：
- "❌ 消息发送失败 (尝试 1/2): Could not establish connection"
- "🔄 尝试发送消息 (2/2): sendToMidjourneyTab"
- "✅ 消息发送成功 (尝试 2): { success: true, ... }"

❌ 超时流程：
- "⚠️ 发送超时，恢复按钮状态"
- "发送失败: 发送超时"
            </div>
        </div>

        <div class="fix-section">
            <h2>🎯 成功标准</h2>
            <div class="status status-success">
                <strong>修复成功的标志：</strong><br>
                ✅ 按钮状态变化流畅，无卡顿<br>
                ✅ 成功时总是显示绿色勾号<br>
                ✅ 失败时显示红色X和错误信息<br>
                ✅ 超时时自动恢复，不会永远加载<br>
                ✅ 网络问题时有重试机制<br>
                ✅ 控制台显示详细的调试信息
            </div>
        </div>

        <div class="test-section">
            <h2>🚀 故障排除</h2>
            <p>如果仍然出现黄色卡顿问题：</p>
            <ol>
                <li><strong>检查Midjourney页面</strong> - 确保页面正常加载</li>
                <li><strong>刷新Midjourney页面</strong> - 重新加载content script</li>
                <li><strong>重启Chrome</strong> - 清除可能的内存问题</li>
                <li><strong>查看控制台</strong> - 检查具体的错误信息</li>
                <li><strong>测试网络连接</strong> - 确保网络稳定</li>
            </ol>
        </div>
    </div>

    <script>
        // 页面加载时显示状态
        window.onload = function() {
            setTimeout(() => {
                console.log('🔧 稳定性修复测试页面已加载');
                console.log('现在按钮状态应该更加稳定，不会卡在黄色状态了！');
                console.log('请进行批量测试验证修复效果');
            }, 1000);
        };
    </script>
</body>
</html>
