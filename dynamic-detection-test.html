<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>动态代码块检测测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .title {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 3px solid #007bff;
            padding-bottom: 15px;
        }
        .solution-section {
            margin: 25px 0;
            padding: 20px;
            border: 2px solid #28a745;
            border-radius: 8px;
            background-color: #d4edda;
        }
        .test-section {
            margin: 25px 0;
            padding: 20px;
            border: 2px solid #17a2b8;
            border-radius: 8px;
            background-color: #d1ecf1;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .status-success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status-info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .code-fix {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            overflow-x: auto;
        }
        .dynamic-content {
            display: none;
            margin: 15px 0;
            padding: 15px;
            border: 2px dashed #6c757d;
            border-radius: 5px;
            background-color: #f8f9fa;
        }
        .btn {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background-color: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">🔄 动态代码块检测修复</h1>
        
        <div class="solution-section">
            <h2>✅ 问题分析与解决方案</h2>
            
            <h3>🔍 问题原因：</h3>
            <ul>
                <li>❌ <strong>动态内容加载</strong> - Claude页面的代码块是动态生成的</li>
                <li>❌ <strong>索引变化</strong> - `nth-child(3)` vs `nth-child(7)` 说明DOM结构在变化</li>
                <li>❌ <strong>单次检查</strong> - 插件只在页面加载时检查一次</li>
                <li>❌ <strong>滚动加载</strong> - 用户滚动时新内容才出现</li>
            </ul>

            <h3>🛠️ 解决方案：</h3>
            <div class="status status-success">
                ✅ <strong>多重检测机制：</strong><br>
                1. 定期检查 (每3秒)<br>
                2. 滚动检查 (滚动1秒后)<br>
                3. DOM变化监听<br>
                4. 重新生成监听<br>
                5. 智能对比检查
            </div>
        </div>

        <div class="solution-section">
            <h2>🔧 技术实现</h2>
            
            <h3>📍 新增的定期检查逻辑：</h3>
            <div class="code-fix">
startPeriodicCheck() {
    // 每3秒检查一次新的代码块
    setInterval(() => {
        const allCodeBlocks = this.findAllCodeBlocks();
        const currentButtonCount = document.querySelectorAll('.claude-mj-floating-btn').length;
        
        // 如果代码块数量大于按钮数量，说明有新的代码块
        if (allCodeBlocks.length > currentButtonCount) {
            console.log('🆕 发现新的代码块，重新添加浮标');
            this.addFloatingButtons();
        }
    }, 3000);
    
    // 监听滚动事件
    window.addEventListener('scroll', () => {
        setTimeout(() => this.addFloatingButtons(), 1000);
    });
}
            </div>

            <h3>🎯 智能检测机制：</h3>
            <div class="code-fix">
findAllCodeBlocks() {
    const selectors = [
        'pre.code-block__code',  // Claude特有格式
        'pre',                   // 标准pre标签
        'code',                  // 标准code标签
        '[class*="code-block"]', // 包含code-block的类
        '[class*="highlight"]'   // 高亮代码块
    ];
    
    // 检查每个元素是否是真正的代码块
    // 去重并验证内容长度
}
            </div>
        </div>

        <div class="test-section">
            <h2>🧪 动态测试</h2>
            <p>点击按钮动态添加代码块，测试插件是否能自动检测：</p>
            
            <button class="btn" onclick="addCodeBlock()">➕ 添加新代码块</button>
            <button class="btn" onclick="addMultipleBlocks()">➕ 添加多个代码块</button>
            <button class="btn" onclick="simulateScroll()">📜 模拟滚动</button>
            
            <div id="dynamic-container"></div>
        </div>

        <div class="test-section">
            <h2>📋 现有代码块</h2>
            <p>这些代码块应该立即被检测到：</p>
            
            <h4>代码块1 - 数据迷宫物理化：</h4>
            <pre class="code-block__code">(CINEMATIC STILL), (FILM BY Lana & Lilly Wachowski), (person trapped in towering digital maze walls made of flowing binary code, deep ocean blue-grey atmosphere with electric green data streams), (Matrix-inspired cyberpunk aesthetic), (wide establishing shot)</pre>

            <h4>代码块2 - 意识囚笼：</h4>
            <pre class="code-block__code">(CINEMATIC STILL), (FILM BY Lana & Lilly Wachowski), (transparent human brain suspended in dark void, neural pathways replaced by glowing data circuits, porcelain white highlights with electric green pulses), (Neo-cyberpunk surrealism), (macro close-up shot)</pre>

            <h4>代码块3 - 城市数据溶解：</h4>
            <pre class="code-block__code">(CINEMATIC STILL), (FILM BY Lana & Lilly Wachowski), (modern cityscape dissolving into cascading digital particles, deep sea blue-grey buildings fragmenting into electric green data streams), (dystopian digital transformation), (aerial drone shot)</pre>
        </div>

        <div class="solution-section">
            <h2>🔍 验证步骤</h2>
            <div class="status status-info">
                <strong>测试流程：</strong><br>
                1. 重新加载插件<br>
                2. 检查现有3个代码块是否有M按钮<br>
                3. 点击"添加新代码块"按钮<br>
                4. 等待3秒，新代码块应该自动获得M按钮<br>
                5. 滚动页面，检查是否触发重新检测<br>
                6. 查看控制台日志确认检测过程
            </div>
        </div>
    </div>

    <script>
        let blockCounter = 4;

        function addCodeBlock() {
            const container = document.getElementById('dynamic-container');
            const newBlock = document.createElement('div');
            newBlock.innerHTML = `
                <h4>动态代码块${blockCounter} - 时间数据漩涡：</h4>
                <pre class="code-block__code">(CINEMATIC STILL), (FILM BY Lana & Lilly Wachowski), (massive spiral vortex of multicolored data fragments, human silhouettes being pulled into digital maelstrom, deep ocean blue core with electric green energy bursts), (abstract digital nightmare), (dramatic low angle shot)</pre>
            `;
            container.appendChild(newBlock);
            blockCounter++;
            
            console.log(`🆕 动态添加了代码块${blockCounter-1}，插件应该在3秒内检测到`);
        }

        function addMultipleBlocks() {
            for (let i = 0; i < 3; i++) {
                setTimeout(() => addCodeBlock(), i * 500);
            }
        }

        function simulateScroll() {
            console.log('📜 模拟滚动事件');
            window.scrollBy(0, 100);
            setTimeout(() => window.scrollBy(0, -100), 500);
        }

        // 页面加载时显示状态
        window.onload = function() {
            setTimeout(() => {
                console.log('🔄 动态代码块检测测试页面已加载');
                console.log('插件现在应该能检测到所有代码块，包括动态添加的！');
            }, 1000);
        };
    </script>
</body>
</html>
