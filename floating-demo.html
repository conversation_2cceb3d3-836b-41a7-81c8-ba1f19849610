<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>浮标功能演示</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .title {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 3px solid #4CAF50;
            padding-bottom: 15px;
        }
        .demo-section {
            margin: 25px 0;
            padding: 20px;
            border: 2px solid #ddd;
            border-radius: 8px;
            position: relative;
        }
        .claude-style {
            border-color: #4CAF50;
            background-color: #f8fff8;
        }
        .prompt-container {
            position: relative;
            margin: 15px 0;
            padding: 15px;
            background-color: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #2196F3;
        }
        .prompt-text {
            font-family: 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.5;
            color: #333;
        }
        .floating-button {
            position: absolute;
            right: -40px;
            top: 50%;
            transform: translateY(-50%);
            width: 32px;
            height: 32px;
            background: linear-gradient(135deg, #7C3AED, #3B82F6);
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
            z-index: 1000;
            transition: all 0.3s ease;
            opacity: 0.8;
        }
        .floating-button:hover {
            opacity: 1;
            transform: translateY(-50%) scale(1.1);
        }
        .floating-button.sending {
            background: linear-gradient(135deg, #FF9800, #F57C00);
        }
        .floating-button.success {
            background: linear-gradient(135deg, #4CAF50, #388E3C);
        }
        .floating-button.error {
            background: linear-gradient(135deg, #f44336, #d32f2f);
        }
        .instructions {
            background-color: #e3f2fd;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .feature-list {
            background-color: #f0f8ff;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .button {
            background-color: #4CAF50;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        .button:hover {
            background-color: #45a049;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
            text-align: center;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">🚀 Grammarly风格浮标功能演示</h1>
        
        <div class="instructions">
            <h2>📋 使用说明</h2>
            <ol>
                <li><strong>在Claude页面中：</strong>安装插件后，点击插件图标，然后点击"显示浮标按钮"</li>
                <li><strong>浮标出现：</strong>每个包含(CINEMATIC STILL)的prompt旁边会出现一个蓝绿色的圆形浮标</li>
                <li><strong>一键发送：</strong>点击浮标，prompt会自动发送到Midjourney页面并执行</li>
                <li><strong>状态反馈：</strong>浮标会显示发送状态（发送中/成功/失败）</li>
            </ol>
        </div>

        <div class="demo-section claude-style">
            <h2>🎨 Claude页面效果预览</h2>
            <p>以下是在Claude页面中prompts的显示效果，每个prompt旁边都有浮标：</p>
            
            <div class="prompt-container">
                <div class="prompt-text">
                    <span class="textContent">(CINEMATIC STILL), (FILM BY Lana & Lilly Wachowski), (person trapped in towering digital maze walls made of flowing binary code, deep ocean blue-grey atmosphere with electric green data streams), (Matrix-inspired cyberpunk aesthetic), (wide establishing shot)</span>
                </div>
                <div class="floating-button" onclick="simulateSend(this, 1)">
                    <svg width="18" height="18" viewBox="0 0 24 24" fill="white">
                        <path d="M3,21V3H7L12,14L17,3H21V21H17V8L13,18H11L7,8V21H3Z" stroke="white" stroke-width="0.5"/>
                    </svg>
                </div>
            </div>

            <div class="prompt-container">
                <div class="prompt-text">
                    <span class="textContent">(CINEMATIC STILL), (FILM BY Lana & Lilly Wachowski), (transparent human brain suspended in dark void, neural pathways replaced by glowing data circuits, porcelain white highlights with electric green pulses), (Neo-cyberpunk surrealism), (macro close-up shot)</span>
                </div>
                <div class="floating-button" onclick="simulateSend(this, 2)">
                    <svg width="18" height="18" viewBox="0 0 24 24" fill="white">
                        <path d="M3,21V3H7L12,14L17,3H21V21H17V8L13,18H11L7,8V21H3Z" stroke="white" stroke-width="0.5"/>
                    </svg>
                </div>
            </div>

            <div class="prompt-container">
                <div class="prompt-text">
                    <span class="textContent">(CINEMATIC STILL), (FILM BY Lana & Lilly Wachowski), (modern cityscape dissolving into cascading digital particles, deep sea blue-grey buildings fragmenting into electric green data streams), (dystopian digital transformation), (aerial drone shot)</span>
                </div>
                <div class="floating-button" onclick="simulateSend(this, 3)">
                    <svg width="18" height="18" viewBox="0 0 24 24" fill="white">
                        <path d="M3,21V3H7L12,14L17,3H21V21H17V8L13,18H11L7,8V21H3Z" stroke="white" stroke-width="0.5"/>
                    </svg>
                </div>
            </div>
        </div>

        <div class="feature-list">
            <h2>✨ 功能特点</h2>
            <ul>
                <li><strong>🎯 精准识别：</strong>只对包含"(CINEMATIC STILL)"的prompts添加浮标</li>
                <li><strong>🚀 一键发送：</strong>点击浮标直接发送到Midjourney并自动执行</li>
                <li><strong>📱 状态反馈：</strong>实时显示发送状态（发送中/成功/失败）</li>
                <li><strong>🔄 动态更新：</strong>页面内容变化时自动添加新的浮标</li>
                <li><strong>🎨 美观设计：</strong>类似Grammarly的悬浮设计，不干扰阅读</li>
                <li><strong>⚡ 跨配置文件：</strong>支持Claude和Midjourney在不同Chrome配置文件中</li>
            </ul>
        </div>

        <div class="demo-section">
            <h2>🧪 测试功能</h2>
            <p>点击下面的按钮测试浮标的不同状态：</p>
            
            <button class="button" onclick="testAllStates()">测试所有状态</button>
            <button class="button" onclick="resetAllButtons()">重置浮标</button>
            
            <div id="test-status"></div>
        </div>

        <div class="instructions">
            <h2>🔧 技术实现</h2>
            <ul>
                <li><strong>DOM监听：</strong>使用MutationObserver监听页面变化</li>
                <li><strong>精确定位：</strong>通过CSS选择器精确找到prompt元素</li>
                <li><strong>跨标签通信：</strong>使用Chrome扩展API在不同标签页间通信</li>
                <li><strong>自动执行：</strong>模拟键盘事件自动按回车执行</li>
                <li><strong>状态管理：</strong>实时更新浮标状态和视觉反馈</li>
            </ul>
        </div>
    </div>

    <script>
        function simulateSend(button, index) {
            const statusDiv = document.getElementById('test-status');
            
            // 显示发送状态
            button.className = 'floating-button sending';
            button.innerHTML = `
                <svg width="16" height="16" viewBox="0 0 24 24" fill="white">
                    <circle cx="12" cy="12" r="10" stroke="white" stroke-width="2" fill="none" stroke-dasharray="31.416" stroke-dashoffset="31.416">
                        <animate attributeName="stroke-dasharray" dur="2s" values="0 31.416;15.708 15.708;0 31.416" repeatCount="indefinite"/>
                        <animate attributeName="stroke-dashoffset" dur="2s" values="0;-15.708;-31.416" repeatCount="indefinite"/>
                    </circle>
                </svg>
            `;
            
            statusDiv.innerHTML = `<div class="status info">正在发送Prompt ${index}到Midjourney...</div>`;
            
            // 模拟发送过程
            setTimeout(() => {
                const success = Math.random() > 0.3; // 70%成功率
                
                if (success) {
                    button.className = 'floating-button success';
                    button.innerHTML = `
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="white">
                            <path d="M9,20.42L2.79,14.21L5.62,11.38L9,14.77L18.88,4.88L21.71,7.71L9,20.42Z"/>
                        </svg>
                    `;
                    statusDiv.innerHTML = `<div class="status success">✅ Prompt ${index} 发送成功！</div>`;
                } else {
                    button.className = 'floating-button error';
                    button.innerHTML = `
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="white">
                            <path d="M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z"/>
                        </svg>
                    `;
                    statusDiv.innerHTML = `<div class="status error">❌ Prompt ${index} 发送失败</div>`;
                }
                
                // 3秒后恢复原状
                setTimeout(() => {
                    resetButton(button);
                }, 3000);
            }, 2000);
        }
        
        function resetButton(button) {
            button.className = 'floating-button';
            button.innerHTML = `
                <svg width="18" height="18" viewBox="0 0 24 24" fill="white">
                    <path d="M3,21V3H7L12,14L17,3H21V21H17V8L13,18H11L7,8V21H3Z" stroke="white" stroke-width="0.5"/>
                </svg>
            `;
        }
        
        function testAllStates() {
            const buttons = document.querySelectorAll('.floating-button');
            buttons.forEach((button, index) => {
                setTimeout(() => {
                    simulateSend(button, index + 1);
                }, index * 1000);
            });
        }
        
        function resetAllButtons() {
            const buttons = document.querySelectorAll('.floating-button');
            buttons.forEach(resetButton);
            
            const statusDiv = document.getElementById('test-status');
            statusDiv.innerHTML = `<div class="status info">所有浮标已重置</div>`;
        }
    </script>
</body>
</html>
