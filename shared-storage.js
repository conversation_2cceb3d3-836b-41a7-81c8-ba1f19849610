// shared-storage.js - 跨Chrome配置文件的共享存储服务

class SharedStorageService {
    constructor() {
        this.storageKey = 'claude_midjourney_prompts';
        this.lockKey = 'claude_midjourney_lock';
        this.init();
    }

    init() {
        // 使用localStorage作为跨配置文件的共享存储
        // 注意：这需要两个Chrome实例访问相同的文件系统位置
        this.setupFileBasedStorage();
    }

    setupFileBasedStorage() {
        // 创建一个基于文件的存储系统
        // 使用用户的临时目录作为共享位置
        this.sharedPath = this.getSharedStoragePath();
    }

    getSharedStoragePath() {
        // 获取用户的临时目录路径（浏览器环境下的简化版本）
        const userProfile = navigator.userAgent.includes('Windows') ?
            'C:\\Users\\<USER>\\AppData\\Local\\Temp\\' :
            '/tmp/';
        return userProfile + 'claude_midjourney_shared.json';
    }

    async savePrompts(prompts) {
        try {
            const data = {
                prompts: prompts,
                timestamp: Date.now(),
                source: 'claude'
            };

            // 方法1: 使用localStorage (同一台机器上的不同Chrome配置文件可能共享)
            localStorage.setItem(this.storageKey, JSON.stringify(data));

            // 方法2: 使用Chrome的本地文件API (需要用户授权)
            await this.saveToFile(data);

            // 方法3: 使用网络存储 (可选)
            await this.saveToNetwork(data);

            console.log('Prompts已保存到共享存储');
            return { success: true };
        } catch (error) {
            console.error('保存prompts到共享存储失败:', error);
            return { success: false, error: error.message };
        }
    }

    async loadPrompts() {
        try {
            // 尝试从多个来源加载
            let data = null;

            // 方法1: 从localStorage加载
            const localData = localStorage.getItem(this.storageKey);
            if (localData) {
                data = JSON.parse(localData);
            }

            // 方法2: 从文件加载
            if (!data) {
                data = await this.loadFromFile();
            }

            // 方法3: 从网络加载
            if (!data) {
                data = await this.loadFromNetwork();
            }

            if (data && data.prompts) {
                console.log('从共享存储加载了', data.prompts.length, '个prompts');
                return { success: true, prompts: data.prompts, timestamp: data.timestamp };
            }

            return { success: false, message: '没有找到共享的prompts' };
        } catch (error) {
            console.error('从共享存储加载prompts失败:', error);
            return { success: false, error: error.message };
        }
    }

    async saveToFile(data) {
        // 使用File System Access API (需要用户授权)
        if ('showSaveFilePicker' in window) {
            try {
                const fileHandle = await window.showSaveFilePicker({
                    suggestedName: 'claude_prompts.json',
                    types: [{
                        description: 'JSON files',
                        accept: { 'application/json': ['.json'] }
                    }]
                });

                const writable = await fileHandle.createWritable();
                await writable.write(JSON.stringify(data, null, 2));
                await writable.close();

                // 保存文件句柄以便后续使用
                localStorage.setItem('shared_file_handle', JSON.stringify({
                    name: fileHandle.name,
                    timestamp: Date.now()
                }));

                return true;
            } catch (error) {
                console.log('用户取消了文件保存或不支持File System Access API');
                return false;
            }
        }
        return false;
    }

    async loadFromFile() {
        // 使用File System Access API加载
        if ('showOpenFilePicker' in window) {
            try {
                const [fileHandle] = await window.showOpenFilePicker({
                    types: [{
                        description: 'JSON files',
                        accept: { 'application/json': ['.json'] }
                    }]
                });

                const file = await fileHandle.getFile();
                const contents = await file.text();
                return JSON.parse(contents);
            } catch (error) {
                console.log('用户取消了文件选择或不支持File System Access API');
                return null;
            }
        }
        return null;
    }

    async saveToNetwork(data) {
        // 可选：使用简单的网络存储服务
        // 这里可以集成一个简单的云存储API
        try {
            // 示例：使用JSONBin.io或类似服务
            const response = await fetch('https://api.jsonbin.io/v3/b', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Master-Key': 'YOUR_API_KEY' // 需要替换为实际的API密钥
                },
                body: JSON.stringify(data)
            });

            if (response.ok) {
                const result = await response.json();
                localStorage.setItem('shared_bin_id', result.metadata.id);
                return true;
            }
        } catch (error) {
            console.log('网络存储不可用:', error);
        }
        return false;
    }

    async loadFromNetwork() {
        try {
            const binId = localStorage.getItem('shared_bin_id');
            if (!binId) return null;

            const response = await fetch(`https://api.jsonbin.io/v3/b/${binId}/latest`, {
                headers: {
                    'X-Master-Key': 'YOUR_API_KEY' // 需要替换为实际的API密钥
                }
            });

            if (response.ok) {
                const result = await response.json();
                return result.record;
            }
        } catch (error) {
            console.log('从网络加载失败:', error);
        }
        return null;
    }

    async clearSharedData() {
        try {
            localStorage.removeItem(this.storageKey);
            localStorage.removeItem('shared_file_handle');
            localStorage.removeItem('shared_bin_id');
            console.log('共享数据已清除');
            return { success: true };
        } catch (error) {
            console.error('清除共享数据失败:', error);
            return { success: false, error: error.message };
        }
    }

    // 监听共享数据变化
    onSharedDataChange(callback) {
        // 监听localStorage变化
        window.addEventListener('storage', (event) => {
            if (event.key === this.storageKey) {
                const data = event.newValue ? JSON.parse(event.newValue) : null;
                callback(data);
            }
        });

        // 定期检查文件变化（如果使用文件存储）
        setInterval(async () => {
            const data = await this.loadPrompts();
            if (data.success) {
                callback(data);
            }
        }, 5000); // 每5秒检查一次
    }
}

// 导出服务
window.SharedStorageService = SharedStorageService;
