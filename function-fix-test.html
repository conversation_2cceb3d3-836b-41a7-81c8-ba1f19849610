<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>函数错误修复测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .title {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 3px solid #dc3545;
            padding-bottom: 15px;
        }
        .fix-section {
            margin: 25px 0;
            padding: 20px;
            border: 2px solid #28a745;
            border-radius: 8px;
            background-color: #d4edda;
        }
        .error-section {
            margin: 25px 0;
            padding: 20px;
            border: 2px solid #dc3545;
            border-radius: 8px;
            background-color: #f8d7da;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .status-success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status-error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .code-fix {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">🔧 函数错误修复</h1>
        
        <div class="error-section">
            <h2>❌ 原始错误</h2>
            <div class="status status-error">
                <strong>错误信息：</strong><br>
                <code>TypeError: this.getElementText is not a function</code>
            </div>
            <p><strong>原因：</strong>在 <code>findAllCodeBlocks</code> 方法中调用了不存在的 <code>getElementText</code> 方法</p>
        </div>

        <div class="fix-section">
            <h2>✅ 修复方案</h2>
            
            <h3>🔍 问题定位：</h3>
            <div class="code-fix">
// 错误的代码（在 findAllCodeBlocks 方法中）
const text = this.getElementText(element);  // ❌ 方法不存在

// 正确的代码（其他地方都在使用）
const text = element.textContent || element.innerText;  // ✅ 标准方法
            </div>

            <h3>🛠️ 修复后的代码：</h3>
            <div class="code-fix">
findAllCodeBlocks() {
    const codeBlocks = [];
    
    const selectors = [
        'pre.code-block__code',
        'pre',
        'code',
        '[class*="code-block"]',
        '[class*="highlight"]'
    ];
    
    selectors.forEach(selector => {
        const elements = document.querySelectorAll(selector);
        elements.forEach(element => {
            if (this.isClaudeCodeBlock(element)) {
                // ✅ 修复：使用标准的文本获取方法
                const text = element.textContent || element.innerText;
                if (text && text.trim().length > 10 && text.trim().length < 2000) {
                    codeBlocks.push(element);
                }
            }
        });
    });
    
    return [...new Set(codeBlocks)];
}
            </div>

            <h3>🎯 一致性保证：</h3>
            <p>现在所有地方都使用相同的文本获取方法：</p>
            <div class="code-fix">
// 统一使用这种方式获取元素文本
const text = element.textContent || element.innerText;
            </div>
        </div>

        <div class="fix-section">
            <h2>🧪 测试代码块</h2>
            <p>这些代码块现在应该能被正确检测，不会出现函数错误：</p>
            
            <h4>测试代码块1：</h4>
            <pre class="code-block__code">A futuristic cyberpunk cityscape at night, neon lights reflecting on wet streets, towering skyscrapers with holographic advertisements, flying cars in the distance, rain-soaked pavement, purple and blue neon palette, cinematic lighting, ultra-detailed, 8k resolution --ar 16:9 --v 6</pre>

            <h4>测试代码块2：</h4>
            <pre>An ethereal forest fairy with translucent butterfly wings, sitting on a glowing mushroom, surrounded by fireflies and magical particles, enchanted woodland background, soft golden hour lighting, fantasy art style, delicate features, flowing hair, mystical atmosphere --ar 3:4 --v 6</pre>

            <h4>测试代码块3：</h4>
            <pre class="code-block__code">Modern minimalist living room interior, clean lines, neutral color palette with warm wood accents, large floor-to-ceiling windows, natural light streaming in, cozy reading corner, contemporary furniture, indoor plants, scandinavian design aesthetic, professional architectural photography --ar 4:3 --v 6</pre>
        </div>

        <div class="fix-section">
            <h2>📋 修复清单</h2>
            <div class="status status-success">
                ✅ <strong>已修复的问题：</strong><br>
                - 修复了不存在的 getElementText 方法调用<br>
                - 统一使用标准的文本获取方法<br>
                - 确保定期检查功能正常工作<br>
                - 保持代码一致性<br>
                - 防止JavaScript运行时错误
            </div>
        </div>

        <div class="fix-section">
            <h2>🔍 验证步骤</h2>
            <ol>
                <li><strong>重新加载插件</strong> - 在Chrome扩展页面刷新插件</li>
                <li><strong>打开控制台</strong> - 检查是否还有函数错误</li>
                <li><strong>等待定期检查</strong> - 应该看到正常的检查日志</li>
                <li><strong>验证M按钮</strong> - 所有代码块都应该显示M按钮</li>
                <li><strong>检查动态检测</strong> - 定期检查应该正常工作</li>
            </ol>
        </div>

        <div class="fix-section">
            <h2>🔍 预期控制台输出</h2>
            <div class="status status-success">
                ✅ <strong>正常日志：</strong><br>
                - "🔄 启动定期检查，解决动态加载的代码块识别问题"<br>
                - "🔍 定期检查: 发现 X 个代码块, 当前有 Y 个M按钮"<br>
                - "✅ 识别为Claude代码块"<br>
                <br>
                ❌ <strong>不应该看到：</strong><br>
                - "TypeError: this.getElementText is not a function"<br>
                - "定期检查过程中出错"
            </div>
        </div>
    </div>

    <script>
        // 页面加载时显示状态
        window.onload = function() {
            setTimeout(() => {
                console.log('🔧 函数错误修复测试页面已加载');
                console.log('现在插件应该不会出现 getElementText 函数错误了！');
                console.log('定期检查功能应该正常工作');
            }, 1000);
        };
    </script>
</body>
</html>
