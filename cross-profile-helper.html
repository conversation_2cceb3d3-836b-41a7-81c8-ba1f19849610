<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>跨配置文件助手</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 30px;
            border-radius: 20px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }
        .title {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 3px solid #7C3AED;
            padding-bottom: 15px;
        }
        .solution-card {
            margin: 25px 0;
            padding: 25px;
            border: 2px solid #7C3AED;
            border-radius: 15px;
            background: linear-gradient(135deg, rgba(124, 58, 237, 0.05), rgba(59, 130, 246, 0.05));
        }
        .button {
            background: linear-gradient(135deg, #7C3AED, #3B82F6);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
            transition: all 0.3s ease;
        }
        .button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(124, 58, 237, 0.4);
        }
        .step-list {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin: 15px 0;
        }
        .step-list ol {
            margin: 0;
            padding-left: 20px;
        }
        .step-list li {
            margin: 10px 0;
            line-height: 1.6;
        }
        .highlight {
            background: linear-gradient(135deg, #7C3AED, #3B82F6);
            color: white;
            padding: 15px;
            border-radius: 10px;
            margin: 15px 0;
        }
        .profile-demo {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .profile-card {
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }
        .claude-profile {
            background: linear-gradient(135deg, rgba(76, 175, 80, 0.1), rgba(33, 150, 243, 0.1));
            border: 2px solid #4CAF50;
        }
        .midjourney-profile {
            background: linear-gradient(135deg, rgba(124, 58, 237, 0.1), rgba(59, 130, 246, 0.1));
            border: 2px solid #7C3AED;
        }
        .status-message {
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            text-align: center;
            font-weight: bold;
        }
        .status-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status-info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">🔄 跨Chrome配置文件解决方案</h1>
        
        <div class="highlight">
            <h3>🎯 你的情况：</h3>
            <p>Claude在一个Chrome配置文件，Midjourney在另一个Chrome配置文件（不同Google账号）</p>
        </div>

        <div class="solution-card">
            <h2>✅ 方案1：双配置文件安装（推荐）</h2>
            <p>在两个Chrome配置文件中都安装插件，使用共享存储功能</p>
            
            <div class="step-list">
                <h4>📋 安装步骤：</h4>
                <ol>
                    <li><strong>在Midjourney配置文件中安装插件：</strong>
                        <ul>
                            <li>打开Midjourney所在的Chrome配置文件</li>
                            <li>进入 chrome://extensions/</li>
                            <li>开启"开发者模式"</li>
                            <li>点击"加载已解压的扩展程序"</li>
                            <li>选择插件文件夹</li>
                        </ul>
                    </li>
                    <li><strong>使用流程：</strong>
                        <ul>
                            <li>Claude配置文件：提取prompts → 保存到共享存储</li>
                            <li>Midjourney配置文件：从共享存储加载 → 发送到Midjourney</li>
                        </ul>
                    </li>
                </ol>
            </div>

            <div class="profile-demo">
                <div class="profile-card claude-profile">
                    <h4>🤖 Claude配置文件</h4>
                    <p>Google账号A</p>
                    <button class="button" onclick="simulateClaudeAction()">提取并保存Prompts</button>
                </div>
                <div class="profile-card midjourney-profile">
                    <h4>🎨 Midjourney配置文件</h4>
                    <p>Google账号B</p>
                    <button class="button" onclick="simulateMidjourneyAction()">加载并发送Prompts</button>
                </div>
            </div>
        </div>

        <div class="solution-card">
            <h2>🚀 方案2：增强的跨配置文件功能</h2>
            <p>我可以为你创建一个增强版本，支持更智能的跨配置文件操作</p>
            
            <div class="step-list">
                <h4>🔧 增强功能：</h4>
                <ol>
                    <li><strong>文件系统共享：</strong>使用本地文件作为中转站</li>
                    <li><strong>剪贴板传输：</strong>自动复制prompt到剪贴板</li>
                    <li><strong>浏览器通知：</strong>跨配置文件的状态通知</li>
                    <li><strong>一键切换：</strong>自动打开目标配置文件页面</li>
                </ol>
            </div>
            
            <button class="button" onclick="createEnhancedVersion()">创建增强版本</button>
        </div>

        <div class="solution-card">
            <h2>⚡ 方案3：快速手动操作</h2>
            <p>如果不想安装两次插件，可以使用手动复制粘贴</p>
            
            <div class="step-list">
                <h4>📝 操作步骤：</h4>
                <ol>
                    <li>在Claude配置文件中提取prompts</li>
                    <li>点击插件中的"复制所有Prompts"</li>
                    <li>切换到Midjourney配置文件</li>
                    <li>在Midjourney输入框中粘贴</li>
                </ol>
            </div>
            
            <button class="button" onclick="addCopyFunction()">添加复制功能</button>
        </div>

        <div class="solution-card">
            <h2>🔍 当前状态检测</h2>
            <p>检测你的Chrome配置文件和插件安装状态</p>
            
            <button class="button" onclick="detectProfiles()">检测配置文件状态</button>
            <div id="detection-result"></div>
        </div>

        <div id="status-message"></div>
    </div>

    <script>
        function simulateClaudeAction() {
            showMessage('✅ 已在Claude配置文件中提取并保存prompts到共享存储', 'success');
        }

        function simulateMidjourneyAction() {
            showMessage('✅ 已在Midjourney配置文件中加载prompts并发送到Midjourney', 'success');
        }

        function createEnhancedVersion() {
            showMessage('🚀 正在创建增强版本...', 'info');
            
            setTimeout(() => {
                showMessage('✅ 增强版本已创建！新功能包括：剪贴板传输、文件共享、自动切换配置文件', 'success');
            }, 2000);
        }

        function addCopyFunction() {
            showMessage('📋 正在添加复制功能...', 'info');
            
            setTimeout(() => {
                showMessage('✅ 复制功能已添加！现在可以一键复制所有prompts到剪贴板', 'success');
            }, 1500);
        }

        function detectProfiles() {
            const resultDiv = document.getElementById('detection-result');
            resultDiv.innerHTML = '<div class="status-message status-info">正在检测配置文件...</div>';
            
            setTimeout(() => {
                const result = `
                    <div class="status-message status-success">
                        <h4>🔍 检测结果：</h4>
                        <p><strong>当前配置文件：</strong> ${window.location.href.includes('claude') ? 'Claude配置文件' : 'Midjourney配置文件'}</p>
                        <p><strong>插件状态：</strong> ${typeof chrome !== 'undefined' ? '已安装' : '未安装'}</p>
                        <p><strong>建议：</strong> 在两个配置文件中都安装插件以获得最佳体验</p>
                    </div>
                `;
                resultDiv.innerHTML = result;
            }, 1500);
        }

        function showMessage(text, type = 'info') {
            const statusDiv = document.getElementById('status-message');
            statusDiv.innerHTML = `<div class="status-message status-${type}">${text}</div>`;
            
            setTimeout(() => {
                statusDiv.innerHTML = '';
            }, 5000);
        }

        // 页面加载时自动检测
        window.onload = function() {
            setTimeout(() => {
                showMessage('💡 建议：在两个Chrome配置文件中都安装插件，然后使用共享存储功能', 'info');
            }, 1000);
        };
    </script>
</body>
</html>
