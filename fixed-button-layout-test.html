<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>修复按钮重叠问题测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 100%);
            color: #333;
            min-height: 100vh;
        }
        .container {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 10px 40px rgba(0,0,0,0.1);
            border: 1px solid rgba(0,0,0,0.05);
        }
        h1 {
            color: #0f172a;
            text-align: center;
            margin-bottom: 40px;
            font-size: 3em;
            font-weight: 700;
        }
        .feature-section {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            padding: 30px;
            margin: 30px 0;
            border-radius: 12px;
        }
        .demo-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin: 30px 0;
        }
        .demo-block {
            background: white;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 4px 16px rgba(0,0,0,0.05);
            border: 1px solid #e2e8f0;
            position: relative;
            min-height: 120px;
        }
        .button-demo {
            position: absolute;
            right: -40px;
            top: 50%;
            transform: translateY(-50%);
            display: flex;
            gap: 8px;
            align-items: center;
        }
        .button-demo.with-restore {
            right: -84px;
        }
        .demo-button {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            opacity: 0.9;
        }
        .mj-button {
            background: linear-gradient(135deg, #7C3AED, #3B82F6);
            box-shadow: 0 3px 12px rgba(124, 58, 237, 0.4);
        }
        .restore-button {
            background: linear-gradient(135deg, #EF4444, #DC2626);
            box-shadow: 0 3px 12px rgba(239, 68, 68, 0.4);
        }
        .demo-button:hover {
            opacity: 1;
            transform: scale(1.1);
        }
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e2e8f0;
            border-radius: 6px;
            padding: 16px;
            font-family: 'SF Mono', Monaco, monospace;
            font-size: 14px;
            line-height: 1.5;
            margin: 15px 0;
            position: relative;
        }
        .problem-demo {
            background: linear-gradient(135deg, #fef2f2, #fee2e2);
            border: 2px solid #ef4444;
            border-radius: 12px;
            padding: 20px;
            margin: 20px 0;
        }
        .solution-demo {
            background: linear-gradient(135deg, #f0fdf4, #dcfce7);
            border: 2px solid #22c55e;
            border-radius: 12px;
            padding: 20px;
            margin: 20px 0;
        }
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 16px rgba(0,0,0,0.05);
        }
        .comparison-table th,
        .comparison-table td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid #e2e8f0;
        }
        .comparison-table th {
            background: #f1f5f9;
            color: #334155;
            font-weight: 600;
        }
        .problem {
            color: #ef4444;
            font-weight: 600;
        }
        .solution {
            color: #22c55e;
            font-weight: 600;
        }
        .workflow-steps {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        .step {
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 4px 16px rgba(0,0,0,0.05);
            text-align: center;
            border-left: 4px solid #0ea5e9;
        }
        .step-number {
            width: 40px;
            height: 40px;
            background: linear-gradient(135deg, #0ea5e9, #0284c7);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 15px;
            font-weight: bold;
            font-size: 18px;
        }
        .highlight-box {
            background: linear-gradient(135deg, #f0f9ff, #e0f2fe);
            border: 2px solid #0ea5e9;
            border-radius: 12px;
            padding: 25px;
            margin: 25px 0;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 修复按钮重叠问题</h1>
        
        <div class="feature-section">
            <h2>🎯 问题解决方案</h2>
            <p><strong>彻底解决了修改文本后按钮重叠的问题，现在复原图标会正确地出现在M按钮右边。</strong></p>
        </div>

        <div class="problem-demo">
            <h3>❌ 之前的问题</h3>
            <p>修改文本后出现重复的M按钮，导致按钮重叠在一起：</p>
            <ul>
                <li>创建按钮组时包含了M按钮和复原按钮</li>
                <li>修改检测时又创建了新的按钮</li>
                <li>结果：多个M按钮重叠显示</li>
            </ul>
        </div>

        <div class="solution-demo">
            <h3>✅ 现在的解决方案</h3>
            <p>采用动态创建复原按钮的方式，避免重复：</p>
            <ul>
                <li>初始只创建M按钮，不创建按钮组</li>
                <li>修改时动态创建复原按钮，放在M按钮右边</li>
                <li>结果：按钮布局清晰，不会重叠</li>
            </ul>
        </div>

        <div class="demo-container">
            <!-- 未修改状态 -->
            <div class="demo-block">
                <h3>📝 未修改状态</h3>
                <div class="code-block">
A mystical forest with glowing mushrooms, ethereal light filtering through ancient trees, magical atmosphere, fantasy landscape
                </div>
                <div class="button-demo">
                    <div class="demo-button mj-button">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="white">
                            <path d="M3,21V3H7L12,14L17,3H21V21H17V8L13,18H11L7,8V21H3Z"/>
                        </svg>
                    </div>
                </div>
                <p style="font-size: 12px; color: #666; margin-top: 50px;">
                    ↑ 只显示M按钮，位置：right: -40px
                </p>
            </div>

            <!-- 已修改状态 -->
            <div class="demo-block">
                <h3>✏️ 已修改状态</h3>
                <div class="code-block">
A mystical forest with glowing mushrooms, ethereal light filtering through ancient trees, magical atmosphere, fantasy landscape --ar 16:9 --v 6
                </div>
                <div class="button-demo with-restore">
                    <div class="demo-button mj-button">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="white">
                            <path d="M3,21V3H7L12,14L17,3H21V21H17V8L13,18H11L7,8V21H3Z"/>
                        </svg>
                    </div>
                    <div class="demo-button restore-button">
                        <svg width="18" height="18" viewBox="0 0 24 24" fill="white">
                            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                        </svg>
                    </div>
                </div>
                <p style="font-size: 12px; color: #666; margin-top: 50px;">
                    ↑ M按钮 + 复原按钮，位置：right: -84px
                </p>
            </div>
        </div>

        <div class="feature-section">
            <h2>🔧 技术实现</h2>
            <table class="comparison-table">
                <thead>
                    <tr>
                        <th>方面</th>
                        <th>旧实现</th>
                        <th>新实现</th>
                        <th>优势</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td><strong>按钮创建</strong></td>
                        <td class="problem">创建按钮组，包含M+复原按钮</td>
                        <td class="solution">只创建M按钮</td>
                        <td>避免预创建不需要的按钮</td>
                    </tr>
                    <tr>
                        <td><strong>复原按钮</strong></td>
                        <td class="problem">初始就存在，只是隐藏</td>
                        <td class="solution">修改时动态创建</td>
                        <td>按需创建，避免重复</td>
                    </tr>
                    <tr>
                        <td><strong>按钮位置</strong></td>
                        <td class="problem">按钮组统一管理位置</td>
                        <td class="solution">独立定位，精确控制</td>
                        <td>位置更精确，不会重叠</td>
                    </tr>
                    <tr>
                        <td><strong>清理机制</strong></td>
                        <td class="problem">清理按钮组</td>
                        <td class="solution">清理所有单个按钮</td>
                        <td>清理更彻底</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="feature-section">
            <h2>🔄 新的工作流程</h2>
            <div class="workflow-steps">
                <div class="step">
                    <div class="step-number">1</div>
                    <h4>创建M按钮</h4>
                    <p>只创建M按钮，位置 right: -40px</p>
                </div>
                
                <div class="step">
                    <div class="step-number">2</div>
                    <h4>检测修改</h4>
                    <p>监听输入事件，检测内容变化</p>
                </div>
                
                <div class="step">
                    <div class="step-number">3</div>
                    <h4>动态创建</h4>
                    <p>修改时创建复原按钮，位置 right: -84px</p>
                </div>
                
                <div class="step">
                    <div class="step-number">4</div>
                    <h4>智能管理</h4>
                    <p>恢复后隐藏复原按钮，保持界面简洁</p>
                </div>
            </div>
        </div>

        <div class="highlight-box">
            <h2>✨ 核心改进</h2>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-top: 20px;">
                <div>
                    <h4>🎯 精确定位</h4>
                    <p>M按钮：right: -40px<br>复原按钮：right: -84px</p>
                </div>
                <div>
                    <h4>🔄 动态创建</h4>
                    <p>按需创建复原按钮，避免预创建导致的重复</p>
                </div>
                <div>
                    <h4>🧹 彻底清理</h4>
                    <p>移除所有单个按钮，清理更彻底</p>
                </div>
                <div>
                    <h4>⚡ 性能优化</h4>
                    <p>减少不必要的DOM元素，提升性能</p>
                </div>
            </div>
        </div>

        <div class="feature-section">
            <h2>📝 代码关键点</h2>
            <div style="background: #f8fafc; padding: 20px; border-radius: 8px; font-family: monospace; font-size: 14px;">
                <h4>1. M按钮独立创建：</h4>
                <code>
                position: absolute;<br>
                right: -40px;  // 单个按钮位置<br>
                top: 50%;<br>
                transform: translateY(-50%);
                </code>
                
                <h4 style="margin-top: 20px;">2. 复原按钮动态创建：</h4>
                <code>
                position: absolute;<br>
                right: -84px;  // 为两个按钮预留空间<br>
                top: 50%;<br>
                transform: translateY(-50%);
                </code>
                
                <h4 style="margin-top: 20px;">3. 智能检测逻辑：</h4>
                <code>
                if (!promptElement.restoreButton) {<br>
                &nbsp;&nbsp;this.createRestoreButton(promptElement, mjButton);<br>
                } else {<br>
                &nbsp;&nbsp;promptElement.restoreButton.style.display = 'flex';<br>
                }
                </code>
            </div>
        </div>

        <div class="feature-section">
            <h2>🎊 最终效果</h2>
            <p style="font-size: 18px; line-height: 1.8;">
                现在的按钮系统完全解决了重叠问题：
            </p>
            <ul style="font-size: 16px; line-height: 1.8;">
                <li>✅ <strong>无重复按钮：</strong>M按钮只创建一次，不会重复</li>
                <li>✅ <strong>精确定位：</strong>复原按钮准确出现在M按钮右边</li>
                <li>✅ <strong>动态管理：</strong>按需创建和隐藏复原按钮</li>
                <li>✅ <strong>清理彻底：</strong>移除时清理所有相关元素</li>
                <li>✅ <strong>性能优化：</strong>减少不必要的DOM操作</li>
            </ul>
            
            <p style="background: rgba(14, 165, 233, 0.1); padding: 15px; border-radius: 8px; margin-top: 20px;">
                <strong>💡 完美解决：</strong>按钮重叠问题已彻底解决！现在的布局清晰、精确，提供了最佳的用户体验。
            </p>
        </div>
    </div>
</body>
</html>
