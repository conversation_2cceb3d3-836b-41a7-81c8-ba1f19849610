// popup.js - 插件弹窗的主要逻辑
class PopupManager {
    constructor() {
        this.selectedPrompts = new Set();
        this.allPrompts = [];
        this.sharedStorage = new SimpleStorage();
        this.init();
    }

    init() {
        this.bindEvents();
        this.loadPrompts();
        this.setupSharedStorageListener();
    }

    bindEvents() {
        document.getElementById('extract-prompts').addEventListener('click', () => {
            this.extractPrompts();
        });

        document.getElementById('send-to-midjourney').addEventListener('click', () => {
            this.sendSelectedPrompts();
        });

        document.getElementById('send-all').addEventListener('click', () => {
            this.sendAllPrompts();
        });

        // 添加共享存储相关按钮事件
        const saveToSharedBtn = document.createElement('button');
        saveToSharedBtn.textContent = '保存到共享存储';
        saveToSharedBtn.className = 'button button-secondary';
        saveToSharedBtn.addEventListener('click', () => {
            this.saveToSharedStorage();
        });

        const loadFromSharedBtn = document.createElement('button');
        loadFromSharedBtn.textContent = '从共享存储加载';
        loadFromSharedBtn.className = 'button button-secondary';
        loadFromSharedBtn.addEventListener('click', () => {
            this.loadFromSharedStorage();
        });

        // 添加浮标控制按钮
        const toggleFloatingBtn = document.createElement('button');
        toggleFloatingBtn.textContent = '显示浮标按钮';
        toggleFloatingBtn.className = 'button button-secondary';
        toggleFloatingBtn.id = 'toggle-floating-btn';
        toggleFloatingBtn.addEventListener('click', () => {
            this.toggleFloatingButtons();
        });

        // 添加页面管理按钮
        const manageTabsBtn = document.createElement('button');
        manageTabsBtn.textContent = '管理Midjourney页面';
        manageTabsBtn.className = 'button button-secondary';
        manageTabsBtn.addEventListener('click', () => {
            this.manageMidjourneyTabs();
        });

        // 将按钮添加到界面
        const promptsSection = document.querySelector('.section');
        promptsSection.appendChild(saveToSharedBtn);
        promptsSection.appendChild(loadFromSharedBtn);
        promptsSection.appendChild(toggleFloatingBtn);
        promptsSection.appendChild(manageTabsBtn);
    }

    async extractPrompts() {
        this.showStatus('正在提取prompts...', 'info');
        
        try {
            // 获取当前活动标签页
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
            
            if (!tab.url.includes('claude.ai')) {
                this.showStatus('请在Claude页面中使用此功能', 'error');
                return;
            }

            // 向content script发送消息提取prompts
            const response = await chrome.tabs.sendMessage(tab.id, { action: 'extractPrompts' });
            
            if (response && response.prompts && response.prompts.length > 0) {
                this.allPrompts = response.prompts;
                this.displayPrompts(response.prompts);
                this.showStatus(`成功提取到 ${response.prompts.length} 个prompts`, 'success');
                this.enableButtons();
            } else {
                this.showStatus('未找到prompts，请确保页面已加载完成', 'error');
            }
        } catch (error) {
            console.error('提取prompts失败:', error);
            this.showStatus('提取失败，请刷新页面后重试', 'error');
        }
    }

    displayPrompts(prompts) {
        const container = document.getElementById('prompts-container');
        container.innerHTML = '';

        prompts.forEach((prompt, index) => {
            const promptElement = document.createElement('div');
            promptElement.className = 'prompt-item';
            promptElement.textContent = prompt.length > 100 ? prompt.substring(0, 100) + '...' : prompt;
            promptElement.title = prompt; // 完整内容作为tooltip
            promptElement.dataset.index = index;

            promptElement.addEventListener('click', () => {
                this.togglePromptSelection(promptElement, index);
            });

            container.appendChild(promptElement);
        });
    }

    togglePromptSelection(element, index) {
        if (this.selectedPrompts.has(index)) {
            this.selectedPrompts.delete(index);
            element.classList.remove('selected');
        } else {
            this.selectedPrompts.add(index);
            element.classList.add('selected');
        }

        // 更新发送按钮状态
        const sendButton = document.getElementById('send-to-midjourney');
        sendButton.disabled = this.selectedPrompts.size === 0;
    }

    async sendSelectedPrompts() {
        if (this.selectedPrompts.size === 0) {
            this.showStatus('请先选择要发送的prompts', 'error');
            return;
        }

        const selectedPromptTexts = Array.from(this.selectedPrompts).map(index => this.allPrompts[index]);
        await this.sendPromptsToMidjourney(selectedPromptTexts);
    }

    async sendAllPrompts() {
        if (this.allPrompts.length === 0) {
            this.showStatus('没有可发送的prompts', 'error');
            return;
        }

        await this.sendPromptsToMidjourney(this.allPrompts);
    }

    async sendPromptsToMidjourney(prompts) {
        this.showStatus('正在发送到Midjourney...', 'info');

        try {
            // 查找Midjourney网页版标签页
            const tabs = await chrome.tabs.query({ url: '*://www.midjourney.com/*' });

            if (tabs.length === 0) {
                this.showStatus('请先打开Midjourney网页版 (www.midjourney.com)', 'error');
                return;
            }

            // 存储prompts到chrome storage
            await chrome.storage.local.set({ 
                pendingPrompts: prompts,
                currentPromptIndex: 0
            });

            // 向Midjourney页面发送消息
            const response = await chrome.tabs.sendMessage(tabs[0].id, { 
                action: 'sendPrompts',
                prompts: prompts
            });

            if (response && response.success) {
                this.showStatus(`成功发送 ${prompts.length} 个prompts`, 'success');
            } else {
                this.showStatus('发送失败，请确保在Midjourney网页版的imagine页面中', 'error');
            }
        } catch (error) {
            console.error('发送到Midjourney失败:', error);
            this.showStatus('发送失败，请检查Midjourney网页版是否正常', 'error');
        }
    }

    enableButtons() {
        document.getElementById('send-all').disabled = false;
    }

    showStatus(message, type) {
        const statusElement = document.getElementById('status');
        statusElement.textContent = message;
        statusElement.className = `status ${type}`;
        statusElement.style.display = 'block';

        // 3秒后自动隐藏
        setTimeout(() => {
            statusElement.style.display = 'none';
        }, 3000);
    }

    async loadPrompts() {
        // 尝试从storage加载之前提取的prompts
        try {
            const result = await chrome.storage.local.get(['cachedPrompts']);
            if (result.cachedPrompts && result.cachedPrompts.length > 0) {
                this.allPrompts = result.cachedPrompts;
                this.displayPrompts(result.cachedPrompts);
                this.enableButtons();
            }

            // 同时尝试从共享存储加载
            await this.loadFromSharedStorage();
        } catch (error) {
            console.error('加载缓存prompts失败:', error);
        }
    }

    async saveToSharedStorage() {
        if (this.allPrompts.length === 0) {
            this.showStatus('没有prompts可保存', 'error');
            return;
        }

        this.showStatus('正在保存到共享存储...', 'info');

        try {
            const result = await this.sharedStorage.savePrompts(this.allPrompts);
            if (result.success) {
                this.showStatus('已保存到共享存储，其他Chrome配置文件可以访问', 'success');
            } else {
                this.showStatus('保存失败: ' + result.error, 'error');
            }
        } catch (error) {
            console.error('保存到共享存储失败:', error);
            this.showStatus('保存失败，请重试', 'error');
        }
    }

    async loadFromSharedStorage() {
        this.showStatus('正在从共享存储加载...', 'info');

        try {
            const result = await this.sharedStorage.loadPrompts();
            if (result.success && result.prompts.length > 0) {
                this.allPrompts = result.prompts;
                this.displayPrompts(result.prompts);
                this.enableButtons();
                this.showStatus(`从共享存储加载了 ${result.prompts.length} 个prompts`, 'success');
            } else {
                this.showStatus('共享存储中没有找到prompts', 'info');
            }
        } catch (error) {
            console.error('从共享存储加载失败:', error);
            this.showStatus('加载失败，请重试', 'error');
        }
    }

    setupSharedStorageListener() {
        // 监听共享存储变化
        this.sharedStorage.onSharedDataChange((data) => {
            if (data && data.prompts) {
                console.log('检测到共享存储更新');
                this.showStatus('检测到新的共享prompts', 'info');
                // 可以选择自动加载或提示用户
            }
        });
    }

    async toggleFloatingButtons() {
        const toggleBtn = document.getElementById('toggle-floating-btn');

        try {
            // 获取当前活动标签页
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });

            if (!tab.url.includes('claude.ai')) {
                this.showStatus('请在Claude页面中使用浮标功能', 'error');
                return;
            }

            const isCurrentlyShowing = toggleBtn.textContent === '隐藏浮标按钮';

            if (isCurrentlyShowing) {
                // 隐藏浮标
                await chrome.tabs.sendMessage(tab.id, { action: 'removeFloatingButtons' });
                toggleBtn.textContent = '显示浮标按钮';
                this.showStatus('已隐藏浮标按钮', 'info');
            } else {
                // 显示浮标
                await chrome.tabs.sendMessage(tab.id, { action: 'addFloatingButtons' });
                toggleBtn.textContent = '隐藏浮标按钮';
                this.showStatus('已显示浮标按钮，点击浮标可直接发送到Midjourney', 'success');
            }
        } catch (error) {
            console.error('切换浮标按钮失败:', error);
            this.showStatus('操作失败，请刷新页面后重试', 'error');
        }
    }

    async manageMidjourneyTabs() {
        try {
            // 通过background script获取Midjourney标签页
            const response = await chrome.runtime.sendMessage({
                action: 'findMidjourneyTabs'
            });

            if (response.success) {
                // 显示页面管理界面
                this.showTabManagementUI(response.tabs);
            } else {
                this.showStatus('获取页面信息失败: ' + response.error, 'error');
            }

        } catch (error) {
            console.error('获取标签页失败:', error);
            this.showStatus('获取页面信息失败', 'error');
        }
    }

    showTabManagementUI(midjourneyTabs) {
        // 清空当前内容
        const container = document.querySelector('.container');
        container.innerHTML = `
            <div class="header">
                <h2>Midjourney页面管理</h2>
                <button id="back-btn" class="button">返回</button>
            </div>
            <div class="tab-management">
                <div class="section">
                    <h3>当前打开的Midjourney页面</h3>
                    <div id="tab-list"></div>
                </div>
                <div class="section">
                    <h3>快速打开</h3>
                    <button id="open-web" class="button">打开Midjourney网页版</button>
                    <button id="open-discord" class="button">打开Discord</button>
                </div>
                <div class="section">
                    <h3>测试连接</h3>
                    <button id="test-connection" class="button">测试页面连接</button>
                    <div id="test-result"></div>
                </div>
            </div>
        `;

        // 显示标签页列表
        const tabList = document.getElementById('tab-list');
        if (midjourneyTabs.length === 0) {
            tabList.innerHTML = '<p style="color: #666;">没有找到Midjourney相关页面</p>';
        } else {
            midjourneyTabs.forEach(tab => {
                const tabItem = document.createElement('div');
                tabItem.style.cssText = `
                    padding: 10px;
                    margin: 5px 0;
                    border: 1px solid #ddd;
                    border-radius: 5px;
                    background: #f9f9f9;
                    cursor: pointer;
                `;

                tabItem.innerHTML = `
                    <div style="font-weight: bold;">${tab.title}</div>
                    <div style="font-size: 12px; color: #666;">${tab.url}</div>
                `;

                tabItem.onclick = async () => {
                    try {
                        const response = await chrome.runtime.sendMessage({
                            action: 'switchToTab',
                            tabId: tab.id
                        });

                        if (response.success) {
                            window.close();
                        } else {
                            this.showStatus('切换页面失败: ' + response.error, 'error');
                        }
                    } catch (error) {
                        this.showStatus('切换页面失败', 'error');
                    }
                };

                tabList.appendChild(tabItem);
            });
        }

        // 绑定事件
        document.getElementById('back-btn').onclick = () => {
            location.reload();
        };

        document.getElementById('open-web').onclick = async () => {
            try {
                const response = await chrome.runtime.sendMessage({
                    action: 'openMidjourneyPage',
                    url: 'https://www.midjourney.com/imagine'
                });

                if (response.success) {
                    this.showStatus('已打开Midjourney网页版', 'success');
                } else {
                    this.showStatus('打开页面失败: ' + response.error, 'error');
                }
            } catch (error) {
                this.showStatus('打开页面失败', 'error');
            }
        };

        document.getElementById('open-discord').onclick = async () => {
            try {
                const response = await chrome.runtime.sendMessage({
                    action: 'openMidjourneyPage',
                    url: 'https://discord.com/channels/@me'
                });

                if (response.success) {
                    this.showStatus('已打开Discord', 'success');
                } else {
                    this.showStatus('打开页面失败: ' + response.error, 'error');
                }
            } catch (error) {
                this.showStatus('打开页面失败', 'error');
            }
        };

        document.getElementById('test-connection').onclick = async () => {
            await this.testMidjourneyConnection();
        };
    }

    async testMidjourneyConnection() {
        const resultDiv = document.getElementById('test-result');
        resultDiv.innerHTML = '<p>正在测试连接...</p>';

        try {
            // 通过background script查找Midjourney页面
            const response = await chrome.runtime.sendMessage({
                action: 'findMidjourneyTabs'
            });

            if (!response.success) {
                resultDiv.innerHTML = '<p style="color: red;">❌ 查找页面失败: ' + response.error + '</p>';
                return;
            }

            const midjourneyTabs = response.tabs.filter(tab =>
                tab.url && tab.url.includes('midjourney.com')
            );

            if (midjourneyTabs.length === 0) {
                resultDiv.innerHTML = '<p style="color: red;">❌ 没有找到Midjourney页面</p>';
                return;
            }

            // 测试发送消息
            const testPrompt = '测试连接';
            const sendResponse = await chrome.runtime.sendMessage({
                action: 'sendToMidjourneyTab',
                tabId: midjourneyTabs[0].id,
                prompt: testPrompt
            });

            if (sendResponse && sendResponse.success) {
                resultDiv.innerHTML = '<p style="color: green;">✅ 连接正常</p>';
            } else {
                resultDiv.innerHTML = '<p style="color: orange;">⚠️ 连接异常，请刷新Midjourney页面</p>';
            }
        } catch (error) {
            resultDiv.innerHTML = '<p style="color: red;">❌ 连接失败: ' + error.message + '</p>';
        }
    }
}

// 初始化popup管理器
document.addEventListener('DOMContentLoaded', () => {
    new PopupManager();
});
