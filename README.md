# Claude to Midjourney Assistant

一个Chrome插件，可以自动提取Claude中的prompts并发送到Midjourney网页版，无需手动复制粘贴。

## 功能特性

- 🤖 自动检测和提取Claude页面中的prompts
- 🎨 一键发送prompts到Midjourney
- 📝 支持批量发送多个prompts
- 💾 智能缓存提取的prompts
- 🎯 支持选择性发送特定prompts

## 安装方法

1. 下载或克隆此项目到本地
2. 打开Chrome浏览器，进入扩展程序管理页面 (`chrome://extensions/`)
3. 开启"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择项目文件夹
6. 插件安装完成！

## 使用方法

### 第一步：在Claude中生成prompts
1. 打开Claude.ai
2. 请求Claude生成Midjourney prompts
3. 等待Claude生成包含CINEMATIC STILL等关键词的prompts

### 第二步：提取prompts
1. 在Claude页面中点击插件图标
2. 点击"提取Prompts"按钮
3. 插件会自动识别页面中的prompts并显示

### 第三步：发送到Midjourney
1. 打开Midjourney网页版 (https://www.midjourney.com/imagine)
2. 确保已登录你的Midjourney账户
3. 回到插件弹窗
4. 选择要发送的prompts（或选择发送全部）
5. 点击"发送到Midjourney"
6. 插件会自动在Midjourney的输入框中填入prompt并提交

## 支持的Prompt格式

插件能够识别以下格式的prompts：
- 包含"CINEMATIC STILL"关键词的文本
- 包含"FILM BY Lana & Lilly Wachowski"的文本
- 包含cyberpunk、Matrix相关关键词的文本
- 括号内的详细描述文本

## 技术特性

- **智能识别**：使用多种策略识别不同格式的prompts
- **批量处理**：支持一次性发送多个prompts
- **防限制**：自动在发送间隔中添加延迟
- **错误处理**：完善的错误处理和用户反馈
- **缓存机制**：自动缓存提取的prompts

## 文件结构

```
├── manifest.json          # 插件配置文件
├── popup.html             # 插件弹窗界面
├── popup.js               # 弹窗逻辑
├── claude-extractor.js    # Claude页面prompt提取器
├── midjourney-sender.js   # Midjourney发送器
├── background.js          # 后台服务
├── icons/                 # 插件图标
└── README.md             # 说明文档
```

## 注意事项

1. **页面要求**：确保在正确的Claude和Discord页面中使用
2. **网络延迟**：发送prompts时会有2秒间隔，避免被Discord限制
3. **权限要求**：插件需要访问claude.ai和discord.com的权限
4. **浏览器兼容**：仅支持Chrome浏览器（Manifest V3）

## 故障排除

### 无法提取prompts
- 确保Claude页面完全加载
- 刷新页面后重试
- 检查prompts是否包含支持的关键词

### 无法发送到Midjourney
- 确保已打开Discord页面
- 确保在正确的Midjourney频道中
- 检查Discord输入框是否可用

### 插件无响应
- 重新加载插件
- 检查浏览器控制台是否有错误信息
- 确保插件权限已正确设置

## 开发说明

如需修改或扩展功能：

1. **修改prompt识别逻辑**：编辑`claude-extractor.js`
2. **调整发送逻辑**：编辑`midjourney-sender.js`
3. **更新界面**：修改`popup.html`和`popup.js`
4. **添加新功能**：在`background.js`中添加后台逻辑

## 版本历史

- v1.0 - 初始版本，支持基本的prompt提取和发送功能

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request来改进这个插件！
