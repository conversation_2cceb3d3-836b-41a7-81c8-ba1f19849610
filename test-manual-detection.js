// test-manual-detection.js - 测试手动检测功能的脚本

console.log('🧪 开始测试手动检测功能...');

// 模拟Chrome API
if (typeof chrome === 'undefined') {
    window.chrome = {
        runtime: {
            sendMessage: function(message, callback) {
                console.log('📤 模拟发送消息:', message);
                
                // 模拟不同的响应
                setTimeout(() => {
                    let response;
                    switch (message.action) {
                        case 'getDetectionMode':
                            response = { success: true, mode: 'auto' };
                            break;
                        case 'setDetectionMode':
                            response = { success: true, mode: message.mode };
                            break;
                        default:
                            response = { success: true };
                    }
                    
                    console.log('📥 模拟响应:', response);
                    if (callback) callback(response);
                }, 100);
                
                return true;
            },
            onMessage: {
                addListener: function(callback) {
                    console.log('📡 添加消息监听器');
                    window.chromeMessageListener = callback;
                }
            }
        },
        storage: {
            local: {
                get: function(keys, callback) {
                    console.log('📦 模拟获取存储:', keys);
                    const result = {};
                    if (keys.includes('detectionMode')) {
                        result.detectionMode = 'auto';
                    }
                    setTimeout(() => callback(result), 50);
                },
                set: function(data, callback) {
                    console.log('💾 模拟设置存储:', data);
                    if (callback) setTimeout(callback, 50);
                }
            }
        }
    };
}

// 创建测试环境
function createTestEnvironment() {
    console.log('🏗️ 创建测试环境...');
    
    // 创建一些模拟的代码块
    const testCodeBlocks = [
        '(CINEMATIC STILL) A majestic dragon soaring through cloudy skies, fantasy art style --ar 16:9',
        'Portrait of a wise wizard with a long beard, oil painting style --ar 4:5',
        'Futuristic cityscape at sunset, cyberpunk aesthetic --ar 21:9'
    ];
    
    testCodeBlocks.forEach((text, index) => {
        const codeBlock = document.createElement('pre');
        codeBlock.textContent = text;
        codeBlock.className = 'test-code-block';
        codeBlock.id = `test-block-${index}`;
        codeBlock.style.cssText = `
            background: #f4f4f4;
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            border-left: 4px solid #007bff;
            font-family: monospace;
            position: relative;
        `;
        document.body.appendChild(codeBlock);
    });
    
    console.log('✅ 测试环境创建完成');
}

// 测试检测模式切换
function testModeToggle() {
    console.log('🔄 测试检测模式切换...');
    
    if (window.chromeMessageListener) {
        // 模拟切换到手动模式
        window.chromeMessageListener({
            action: 'setDetectionMode',
            mode: 'manual'
        }, (response) => {
            console.log('✅ 模式切换响应:', response);
        });
        
        // 延迟后切换回自动模式
        setTimeout(() => {
            window.chromeMessageListener({
                action: 'setDetectionMode',
                mode: 'auto'
            }, (response) => {
                console.log('✅ 模式切换响应:', response);
            });
        }, 3000);
    }
}

// 测试手动检测
function testManualDetect() {
    console.log('🔍 测试手动检测...');
    
    if (window.chromeMessageListener) {
        window.chromeMessageListener({
            action: 'manualDetect'
        }, (response) => {
            console.log('✅ 手动检测响应:', response);
        });
    }
}

// 模拟快捷键事件
function simulateShortcuts() {
    console.log('⌨️ 模拟快捷键事件...');
    
    // 模拟 Ctrl+Shift+D (切换模式)
    setTimeout(() => {
        const toggleEvent = new KeyboardEvent('keydown', {
            key: 'D',
            code: 'KeyD',
            ctrlKey: true,
            shiftKey: true,
            bubbles: true
        });
        document.dispatchEvent(toggleEvent);
        console.log('📤 模拟 Ctrl+Shift+D 事件');
    }, 1000);
    
    // 模拟 Ctrl+Shift+M (手动检测)
    setTimeout(() => {
        const detectEvent = new KeyboardEvent('keydown', {
            key: 'M',
            code: 'KeyM',
            ctrlKey: true,
            shiftKey: true,
            bubbles: true
        });
        document.dispatchEvent(detectEvent);
        console.log('📤 模拟 Ctrl+Shift+M 事件');
    }, 2000);
}

// 检查M按钮是否正确添加
function checkMButtons() {
    console.log('🔍 检查M按钮状态...');
    
    const buttons = document.querySelectorAll('.claude-mj-floating-btn, .claude-mj-floating-button');
    console.log(`📊 找到 ${buttons.length} 个M按钮`);
    
    buttons.forEach((button, index) => {
        console.log(`🔘 按钮${index + 1}:`, {
            visible: button.style.display !== 'none',
            position: button.style.position,
            zIndex: button.style.zIndex
        });
    });
    
    return buttons.length;
}

// 运行测试
function runTests() {
    console.log('🚀 开始运行测试...');
    
    // 创建测试环境
    createTestEnvironment();
    
    // 等待一下让页面稳定
    setTimeout(() => {
        console.log('📊 初始状态检查...');
        checkMButtons();
        
        // 测试模式切换
        testModeToggle();
        
        // 测试手动检测
        setTimeout(() => testManualDetect(), 1500);
        
        // 模拟快捷键
        setTimeout(() => simulateShortcuts(), 2500);
        
        // 最终检查
        setTimeout(() => {
            console.log('📊 最终状态检查...');
            const buttonCount = checkMButtons();
            
            if (buttonCount > 0) {
                console.log('✅ 测试通过：M按钮已正确添加');
            } else {
                console.log('❌ 测试失败：未找到M按钮');
            }
        }, 5000);
        
    }, 1000);
}

// 页面加载完成后运行测试
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', runTests);
} else {
    runTests();
}

console.log('🧪 测试脚本已加载');
