<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON>结构调试测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .title {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 3px solid #007bff;
            padding-bottom: 15px;
        }
        .debug-section {
            margin: 25px 0;
            padding: 20px;
            border: 2px solid #17a2b8;
            border-radius: 8px;
            background-color: #d1ecf1;
        }
        .code-block {
            margin: 15px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #f8f9fa;
            font-family: monospace;
            position: relative;
        }
        .code-block__code {
            background: transparent;
            color: rgb(56, 58, 66);
            font-family: var(--font-mono);
            direction: ltr;
            text-align: left;
            white-space: pre;
            word-spacing: normal;
            word-break: normal;
            line-height: 1.5;
            tab-size: 2;
            hyphens: none;
            padding: 1em;
            margin: 0.5em 0px;
            overflow: auto;
            border-radius: 0.3em;
        }
        .debug-info {
            background: #e9ecef;
            border: 1px solid #ced4da;
            border-radius: 3px;
            padding: 10px;
            margin: 10px 0;
            font-size: 12px;
            font-family: monospace;
        }
        .btn {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background-color: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">🔍 Claude结构调试测试</h1>
        
        <div class="debug-section">
            <h2>🧪 模拟Claude的实际HTML结构</h2>
            <p>这些代码块模拟了Claude页面的实际结构，用于调试为什么只有前两个被识别：</p>
            
            <button class="btn" onclick="debugAllBlocks()">🔍 调试所有代码块</button>
            <button class="btn" onclick="testIsClaudeCodeBlock()">🧪 测试识别函数</button>
            <button class="btn" onclick="simulateAddButtons()">➕ 模拟添加按钮</button>
        </div>

        <div class="debug-section">
            <h2>📋 代码块1 - 应该被识别（有M按钮）</h2>
            <div class="debug-info">结构: pre.code-block__code</div>
            <pre class="code-block__code !my-0 !rounded-lg !text-sm !leading-relaxed" style="background: transparent; color: rgb(56, 58, 66); font-family: var(--font-mono); direction: ltr; text-align: left; white-space: pre; word-spacing: normal; word-break: normal; line-height: 1.5; tab-size: 2; hyphens: none; padding: 1em; margin: 0.5em 0px; overflow: auto; border-radius: 0.3em;">A floating island city with waterfalls cascading into clouds, golden hour lighting, cinematic composition --ar 16:9</pre>
        </div>

        <div class="debug-section">
            <h2>📋 代码块2 - 应该被识别（有M按钮）</h2>
            <div class="debug-info">结构: pre.code-block__code</div>
            <pre class="code-block__code !my-0 !rounded-lg !text-sm !leading-relaxed" style="background: transparent; color: rgb(56, 58, 66); font-family: var(--font-mono); direction: ltr; text-align: left; white-space: pre; word-spacing: normal; word-break: normal; line-height: 1.5; tab-size: 2; hyphens: none; padding: 1em; margin: 0.5em 0px; overflow: auto; border-radius: 0.3em;">Crystal cave filled with bioluminescent plants, ethereal blue glow, fantasy art style --v 6</pre>
        </div>

        <div class="debug-section">
            <h2>📋 代码块3 - 应该被识别（但没有M按钮）</h2>
            <div class="debug-info">结构: pre.code-block__code</div>
            <pre class="code-block__code !my-0 !rounded-lg !text-sm !leading-relaxed" style="background: transparent; color: rgb(56, 58, 66); font-family: var(--font-mono); direction: ltr; text-align: left; white-space: pre; word-spacing: normal; word-break: normal; line-height: 1.5; tab-size: 2; hyphens: none; padding: 1em; margin: 0.5em 0px; overflow: auto; border-radius: 0.3em;">Cyberpunk street market in Tokyo 2077, neon signs reflecting on wet pavement, atmospheric fog --ar 21:9</pre>
        </div>

        <div class="debug-section">
            <h2>📋 代码块4 - 应该被识别（但没有M按钮）</h2>
            <div class="debug-info">结构: pre.code-block__code</div>
            <pre class="code-block__code !my-0 !rounded-lg !text-sm !leading-relaxed" style="background: transparent; color: rgb(56, 58, 66); font-family: var(--font-mono); direction: ltr; text-align: left; white-space: pre; word-spacing: normal; word-break: normal; line-height: 1.5; tab-size: 2; hyphens: none; padding: 1em; margin: 0.5em 0px; overflow: auto; border-radius: 0.3em;">Space station greenhouse with Earth visible through glass dome, lush vegetation, sci-fi concept art</pre>
        </div>

        <div class="debug-section">
            <h2>📋 代码块5 - 简化结构测试</h2>
            <div class="debug-info">结构: 简单的pre标签</div>
            <pre>Portrait of a wise owl wearing Victorian clothing, oil painting style, dark academia aesthetic --ar 4:5</pre>
        </div>

        <div class="debug-section">
            <h2>📋 代码块6 - 另一种结构测试</h2>
            <div class="debug-info">结构: code标签</div>
            <code>Japanese zen garden in Studio Ghibli animation style, soft colors, peaceful atmosphere</code>
        </div>

        <div class="debug-section">
            <h2>🔍 调试输出</h2>
            <div id="debug-output" style="background: #f8f9fa; border: 1px solid #dee2e6; border-radius: 5px; padding: 15px; font-family: monospace; font-size: 12px; max-height: 400px; overflow-y: auto;">
                点击调试按钮查看输出...
            </div>
        </div>
    </div>

    <script>
        function debugAllBlocks() {
            const output = document.getElementById('debug-output');
            output.innerHTML = '';
            
            // 查找所有代码块
            const allPre = document.querySelectorAll('pre');
            const allCode = document.querySelectorAll('code');
            const allCodeBlocks = document.querySelectorAll('.code-block__code');
            
            output.innerHTML += `🔍 调试开始...\n`;
            output.innerHTML += `📊 统计: ${allPre.length} 个pre标签, ${allCode.length} 个code标签, ${allCodeBlocks.length} 个.code-block__code\n\n`;
            
            // 检查每个pre标签
            allPre.forEach((pre, index) => {
                const text = pre.textContent || pre.innerText;
                output.innerHTML += `📋 Pre${index + 1}:\n`;
                output.innerHTML += `  - 文本: ${text.substring(0, 50)}...\n`;
                output.innerHTML += `  - 类名: ${pre.className}\n`;
                output.innerHTML += `  - 标签: ${pre.tagName}\n`;
                output.innerHTML += `  - 文本长度: ${text.trim().length}\n`;
                output.innerHTML += `  - 包含code-block__code: ${pre.className.includes('code-block__code')}\n\n`;
            });
            
            // 检查每个code标签
            allCode.forEach((code, index) => {
                const text = code.textContent || code.innerText;
                output.innerHTML += `💻 Code${index + 1}:\n`;
                output.innerHTML += `  - 文本: ${text.substring(0, 50)}...\n`;
                output.innerHTML += `  - 类名: ${code.className}\n`;
                output.innerHTML += `  - 文本长度: ${text.trim().length}\n\n`;
            });
        }

        function testIsClaudeCodeBlock() {
            const output = document.getElementById('debug-output');
            output.innerHTML = '';
            
            output.innerHTML += `🧪 测试 isClaudeCodeBlock 函数...\n\n`;
            
            // 模拟isClaudeCodeBlock函数
            function isClaudeCodeBlock(element) {
                if (!element) return false;
                
                const tagName = element.tagName.toLowerCase();
                
                // 1. 直接是pre或code标签
                if (tagName === 'pre' || tagName === 'code') {
                    return true;
                }
                
                // 2. 检查Claude特有的代码块类名
                const className = element.className || '';
                if (className.includes('code-block__code')) {
                    return true;
                }
                
                // 3. 检查是否在代码块容器内
                const codeBlockParent = element.closest('pre.code-block__code, pre[class*="code-block"], pre, code');
                if (codeBlockParent) {
                    return true;
                }
                
                return false;
            }
            
            // 测试所有代码块
            const allElements = document.querySelectorAll('pre, code');
            allElements.forEach((element, index) => {
                const text = element.textContent || element.innerText;
                const isCodeBlock = isClaudeCodeBlock(element);
                
                output.innerHTML += `🧪 元素${index + 1}:\n`;
                output.innerHTML += `  - 标签: ${element.tagName}\n`;
                output.innerHTML += `  - 类名: ${element.className}\n`;
                output.innerHTML += `  - 文本: ${text.substring(0, 50)}...\n`;
                output.innerHTML += `  - isClaudeCodeBlock: ${isCodeBlock}\n`;
                output.innerHTML += `  - 文本长度: ${text.trim().length}\n`;
                output.innerHTML += `  - 长度检查: ${text.trim().length > 10 && text.trim().length < 2000}\n\n`;
            });
        }

        function simulateAddButtons() {
            const output = document.getElementById('debug-output');
            output.innerHTML = '';
            
            output.innerHTML += `➕ 模拟添加按钮过程...\n\n`;
            
            // 模拟插件的逻辑
            function isClaudeCodeBlock(element) {
                if (!element) return false;
                const tagName = element.tagName.toLowerCase();
                if (tagName === 'pre' || tagName === 'code') return true;
                const className = element.className || '';
                if (className.includes('code-block__code')) return true;
                return false;
            }
            
            const selectors = [
                'pre.code-block__code',
                'pre',
                'code'
            ];
            
            let totalFound = 0;
            let totalAdded = 0;
            
            selectors.forEach(selector => {
                const elements = document.querySelectorAll(selector);
                output.innerHTML += `🔍 选择器 "${selector}": 找到 ${elements.length} 个元素\n`;
                
                elements.forEach((element, index) => {
                    const text = element.textContent || element.innerText;
                    const isCodeBlock = isClaudeCodeBlock(element);
                    const lengthOk = text && text.trim().length > 10 && text.trim().length < 2000;
                    
                    totalFound++;
                    
                    output.innerHTML += `  📋 ${selector}-${index}:\n`;
                    output.innerHTML += `    - 文本: ${text.substring(0, 30)}...\n`;
                    output.innerHTML += `    - isClaudeCodeBlock: ${isCodeBlock}\n`;
                    output.innerHTML += `    - 长度检查: ${lengthOk}\n`;
                    
                    if (isCodeBlock && lengthOk) {
                        output.innerHTML += `    ✅ 应该添加M按钮\n`;
                        totalAdded++;
                    } else {
                        output.innerHTML += `    ❌ 不添加M按钮\n`;
                    }
                    output.innerHTML += `\n`;
                });
            });
            
            output.innerHTML += `📊 总结: 找到 ${totalFound} 个元素, 应该添加 ${totalAdded} 个M按钮\n`;
        }

        // 页面加载时显示状态
        window.onload = function() {
            setTimeout(() => {
                console.log('🔍 Claude结构调试测试页面已加载');
                console.log('请点击调试按钮查看详细信息');
            }, 1000);
        };
    </script>
</body>
</html>
