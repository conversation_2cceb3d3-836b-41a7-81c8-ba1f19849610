// claude-extractor.js - 在Claude页面中提取prompts的content script

class ClaudePromptExtractor {
    constructor() {
        this.promptElements = new Set(); // 存储已处理的prompt元素
        this.buttonsVisible = false; // 跟踪M按钮是否可见
        this.init();
    }

    // 安全的chrome API调用，带重试机制
    async safeChromeMessage(message, maxRetries = 2) {
        if (typeof chrome === 'undefined' || !chrome.runtime || !chrome.runtime.sendMessage) {
            console.log('Chrome runtime不可用，跳过API调用');
            return { success: false, error: 'Chrome runtime不可用' };
        }

        for (let attempt = 1; attempt <= maxRetries; attempt++) {
            try {
                console.log(`🔄 尝试发送消息 (${attempt}/${maxRetries}):`, message.action);

                const response = await new Promise((resolve, reject) => {
                    const timeout = setTimeout(() => {
                        reject(new Error('消息发送超时'));
                    }, 5000); // 5秒超时

                    chrome.runtime.sendMessage(message, (response) => {
                        clearTimeout(timeout);
                        if (chrome.runtime.lastError) {
                            reject(new Error(chrome.runtime.lastError.message));
                        } else {
                            resolve(response);
                        }
                    });
                });

                console.log(`✅ 消息发送成功 (尝试 ${attempt}):`, response);
                return response;

            } catch (error) {
                console.error(`❌ 消息发送失败 (尝试 ${attempt}/${maxRetries}):`, error.message);

                if (attempt === maxRetries) {
                    // 最后一次尝试失败
                    return { success: false, error: error.message };
                }

                // 等待一段时间后重试
                await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
            }
        }
    }

    init() {
        // 安全地监听来自popup的消息
        if (typeof chrome !== 'undefined' && chrome.runtime && chrome.runtime.onMessage) {
            chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
                try {
                    if (request.action === 'extractPrompts') {
                        this.extractPrompts().then(prompts => {
                            sendResponse({ prompts });
                        }).catch(error => {
                            console.error('提取prompts失败:', error);
                            sendResponse({ prompts: [], error: error.message });
                        });
                        return true; // 保持消息通道开放
                    } else if (request.action === 'addFloatingButtons') {
                        this.addFloatingButtons();
                        sendResponse({ success: true });
                        return true;
                    } else if (request.action === 'removeFloatingButtons') {
                        this.removeFloatingButtons();
                        sendResponse({ success: true });
                        return true;
                    } else if (request.action === 'manualDetect') {
                        this.manualDetect();
                        sendResponse({ success: true });
                        return true;
                    }
                } catch (error) {
                    console.error('处理消息失败:', error);
                    sendResponse({ success: false, error: error.message });
                }
            });
        } else {
            console.log('Chrome runtime不可用，跳过消息监听器');
        }

        // 页面加载完成后只提取一次，不自动添加浮标
        if (document.readyState === 'complete') {
            this.autoExtractAndCache();
        } else {
            window.addEventListener('load', () => {
                this.autoExtractAndCache();
            });
        }

        // 设置键盘快捷键监听
        this.setupKeyboardShortcuts();

        console.log('✅ Claude Prompt Extractor 已初始化 - 纯手动模式');
        console.log('💡 使用 Ctrl+Shift+Z 切换M按钮显示/隐藏');
    }

    async extractPrompts() {
        const prompts = [];
        
        try {
            // 等待页面完全加载
            await this.waitForPageLoad();

            // 方法1: 查找包含CINEMATIC STILL等关键词的文本
            const cinematicPrompts = this.extractCinematicPrompts();
            prompts.push(...cinematicPrompts);

            // 方法2: 查找代码块中的prompts
            const codeBlockPrompts = this.extractCodeBlockPrompts();
            prompts.push(...codeBlockPrompts);

            // 方法3: 查找特定格式的prompts
            const formattedPrompts = this.extractFormattedPrompts();
            prompts.push(...formattedPrompts);

            // 去重
            const uniquePrompts = [...new Set(prompts)];
            
            console.log('提取到的prompts:', uniquePrompts);
            return uniquePrompts;
        } catch (error) {
            console.error('提取prompts时出错:', error);
            return [];
        }
    }

    async waitForPageLoad() {
        return new Promise((resolve) => {
            if (document.readyState === 'complete') {
                resolve();
            } else {
                window.addEventListener('load', resolve);
            }
        });
    }

    extractCinematicPrompts() {
        const prompts = [];

        // 根据实际页面结构，查找包含CINEMATIC STILL的span元素
        const spanElements = document.querySelectorAll('span[class*="textContent"]');

        spanElements.forEach(span => {
            const text = span.textContent || span.innerText;
            if (text && text.includes('(CINEMATIC STILL)')) {
                // 提取完整的prompt文本
                const cleanPrompt = text.trim();
                if (cleanPrompt.length > 20) {
                    prompts.push(cleanPrompt);
                    console.log('找到CINEMATIC STILL prompt:', cleanPrompt.substring(0, 100) + '...');
                }
            }
        });

        // 备用方法：查找所有包含CINEMATIC STILL的元素
        if (prompts.length === 0) {
            const allElements = document.querySelectorAll('*');
            allElements.forEach(element => {
                const text = element.textContent || element.innerText;
                if (text && text.includes('(CINEMATIC STILL)') && text.length < 1000) {
                    // 确保不是包含多个prompts的大容器
                    const cleanPrompt = text.trim();
                    if (cleanPrompt.length > 20 && !prompts.includes(cleanPrompt)) {
                        prompts.push(cleanPrompt);
                        console.log('备用方法找到prompt:', cleanPrompt.substring(0, 100) + '...');
                    }
                }
            });
        }

        return prompts;
    }

    extractCodeBlockPrompts() {
        const prompts = [];
        
        // 查找代码块
        const codeBlocks = document.querySelectorAll('pre, code, .code-block, [class*="code"]');
        
        codeBlocks.forEach(block => {
            const text = block.textContent.trim();
            
            // 检查是否是prompt格式
            if (this.isPromptText(text)) {
                prompts.push(text);
            }
        });

        return prompts;
    }

    extractFormattedPrompts() {
        const prompts = [];
        
        // 查找特定的CSS选择器（根据Claude的页面结构调整）
        const selectors = [
            '[data-testid*="message"]',
            '.message-content',
            '.prose',
            '[class*="markdown"]',
            '.whitespace-pre-wrap'
        ];

        selectors.forEach(selector => {
            const elements = document.querySelectorAll(selector);
            elements.forEach(element => {
                const text = element.textContent.trim();
                if (this.isPromptText(text)) {
                    prompts.push(text);
                }
            });
        });

        return prompts;
    }

    isPromptText(text) {
        if (!text || text.length < 10) return false;

        const trimmedText = text.trim();

        // 检查是否以(CINEMATIC STILL)开头（优先匹配）
        if (trimmedText.startsWith('(CINEMATIC STILL)')) {
            return trimmedText.length > 50 && trimmedText.length < 2000;
        }

        // 排除明显不是prompt的内容
        const excludePatterns = [
            /^(console\.log|function|class|import|export)/i,  // 代码语句
            /^(<!DOCTYPE|<html|<head|<body)/i,                // HTML标签
            /^(\{|\[|"|\d+\.|#)/,                            // JSON、列表、注释
            /^(SELECT|INSERT|UPDATE|DELETE)/i,               // SQL语句
            /^(npm|yarn|pip|git)/i,                          // 命令行
        ];

        // 如果匹配排除模式，则不是prompt
        if (excludePatterns.some(pattern => pattern.test(trimmedText))) {
            return false;
        }

        // 长度在合理范围内就认为是prompt
        return trimmedText.length >= 10 && trimmedText.length <= 2000;
    }

    async autoExtractAndCache() {
        try {
            // 延迟执行，确保页面内容完全加载
            setTimeout(async () => {
                try {
                    const prompts = await this.extractPrompts();
                    if (prompts.length > 0) {
                        // 安全地缓存到chrome storage
                        if (typeof chrome !== 'undefined' && chrome.storage && chrome.storage.local) {
                            chrome.storage.local.set({ cachedPrompts: prompts });
                            console.log('自动提取并缓存了', prompts.length, '个prompts');
                        } else {
                            console.log('Chrome storage不可用，跳过缓存');
                        }
                    }
                } catch (error) {
                    console.error('自动提取prompts过程中出错:', error);
                }
            }, 2000);
        } catch (error) {
            console.error('自动提取prompts失败:', error);
            // 不再在这里调用addFloatingButtons，避免意外移除现有按钮
        }
    }

    addFloatingButtons() {
        console.log('🚀 开始添加浮标按钮...');
        console.trace('📍 addFloatingButtons 调用堆栈:'); // 添加堆栈跟踪

        // 移除现有的浮标
        this.removeFloatingButtons();

        let addedCount = 0;

        // 强制重新检查所有代码块，不依赖缓存
        this.promptElements.clear();
        console.log('🔄 清空缓存，强制重新检查所有代码块');

        // 首先确定主要内容区域，排除侧边栏
        const mainContentArea = this.getMainContentArea();
        if (!mainContentArea) {
            console.log('未找到主要内容区域，使用fallback方法');
            this.addFloatingButtonsFallback();
            return;
        }

        console.log('在主要内容区域中搜索代码块prompts');

        // 专门查找Claude页面中的代码块元素
        const codeBlockSelectors = [
            'pre.code-block__code',                  // Claude特有的代码块类
            'pre[class*="code-block"]',              // 包含code-block的pre
            'pre code',                              // pre标签内的code
            'pre',                                   // 标准代码块
            'code',                                  // 行内代码
            '[class*="code-block"]',                 // 包含code-block的元素
            '[class*="code"]',                       // 包含code的类名
            '[class*="highlight"]',                  // 高亮代码块
            '.whitespace-pre-wrap',                  // Claude特有的代码块类
            '[class*="prose"] pre',                  // prose容器中的pre
            '[class*="prose"] code',                 // prose容器中的code
        ];

        // 查找所有可能的代码块容器
        codeBlockSelectors.forEach(selector => {
            try {
                const elements = mainContentArea.querySelectorAll(selector);
                elements.forEach((element, index) => {
                    const text = element.textContent || element.innerText;

                    // 调试日志
                    if (text && text.trim().length > 50) {
                        console.log(`检查元素 ${selector}-${index}:`, {
                            text: text.substring(0, 100) + '...',
                            isCodeBlock: this.isCodeBlockElement(element),
                            isPrompt: this.isPromptText(text),
                            length: text.trim().length,
                            inMainContent: this.isInMainContent(element),
                            alreadyProcessed: this.promptElements.has(element)
                        });
                    }

                    // 更宽松的检查：只要是代码块格式且有合理内容
                    const isValidCodeBlock = this.isClaudeCodeBlock(element);
                    const hasValidText = text && text.trim().length > 10 && text.trim().length < 2000;
                    const inMainContent = this.isInMainContent(element);
                    const notProcessed = !this.promptElements.has(element);

                    console.log(`🔍 检查 ${selector}-${index}:`, {
                        text: text?.substring(0, 50) + '...',
                        isValidCodeBlock,
                        hasValidText,
                        inMainContent,
                        notProcessed,
                        textLength: text?.trim().length
                    });

                    if (isValidCodeBlock && hasValidText && inMainContent && notProcessed) {
                        console.log('✅ 找到Claude代码块，添加M按钮:', text.substring(0, 100) + '...');
                        try {
                            this.createFloatingButton(element, text.trim(), `${selector}-${index}`);
                            this.promptElements.add(element);
                            addedCount++;
                            console.log(`✅ 成功添加第${addedCount}个M按钮`);
                        } catch (error) {
                            console.error('❌ 创建浮标按钮时出错:', error);
                            console.error('错误详情:', {
                                element: element,
                                text: text.substring(0, 50),
                                selector: selector,
                                index: index
                            });
                        }
                    } else {
                        console.log('❌ 跳过元素:', {
                            text: text?.substring(0, 30) + '...',
                            reason: !isValidCodeBlock ? '不是代码块' :
                                   !hasValidText ? '文本无效' :
                                   !inMainContent ? '不在主内容区' : '已处理',
                            isValidCodeBlock,
                            hasValidText,
                            inMainContent,
                            notProcessed
                        });
                    }
                });
            } catch (error) {
                console.log(`选择器 ${selector} 查询失败:`, error);
            }
        });

        console.log(`添加了 ${addedCount} 个浮标按钮`);

        // 如果没有找到任何prompts，尝试备用方法
        if (addedCount === 0) {
            console.log('主方法未找到prompts，尝试备用方法');
            this.addFloatingButtonsFallback();
            return;
        }

        // 如果仍然没有找到任何prompts，输出调试信息
        if (addedCount === 0) {
            console.log('未找到prompts，开始调试...');
            this.debugPromptElements();
        }

        // 更新按钮可见状态
        this.buttonsVisible = addedCount > 0;
    }

    createFloatingButton(promptElement, promptText, index) {
        // 保存原始内容（保存纯文本用于比较，保存HTML用于恢复）
        if (!promptElement.dataset.originalContent) {
            promptElement.dataset.originalContent = promptElement.innerHTML;
            promptElement.dataset.originalText = promptElement.innerText || promptElement.textContent;
        }

        // 设置代码块为可编辑状态
        this.setupEditableCodeBlock(promptElement);

        // 创建M按钮（发送到Midjourney）
        const mjButton = document.createElement('div');
        mjButton.className = 'claude-mj-floating-button mj-send-btn';
        mjButton.style.cssText = `
            position: absolute;
            right: -40px;
            top: 50%;
            transform: translateY(-50%);
            width: 36px;
            height: 36px;
            background: linear-gradient(135deg, #7C3AED, #3B82F6);
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 3px 12px rgba(124, 58, 237, 0.4);
            transition: all 0.3s ease;
            opacity: 0.9;
            z-index: 10000;
        `;

        mjButton.innerHTML = `
            <svg width="20" height="20" viewBox="0 0 24 24" fill="white">
                <path d="M3,21V3H7L12,14L17,3H21V21H17V8L13,18H11L7,8V21H3Z" stroke="white" stroke-width="0.5"/>
            </svg>
        `;

        // M按钮悬停效果
        mjButton.addEventListener('mouseenter', () => {
            mjButton.style.opacity = '1';
            mjButton.style.transform = 'translateY(-50%) scale(1.1)';
            mjButton.style.boxShadow = '0 4px 16px rgba(124, 58, 237, 0.6)';
        });

        mjButton.addEventListener('mouseleave', () => {
            mjButton.style.opacity = '0.9';
            mjButton.style.transform = 'translateY(-50%) scale(1)';
            mjButton.style.boxShadow = '0 3px 12px rgba(124, 58, 237, 0.4)';
        });

        // M按钮点击事件
        mjButton.addEventListener('click', async (e) => {
            e.stopPropagation();
            // 获取当前代码块的内容（可能已被修改）
            // 使用innerText来获取用户编辑后的实际内容
            const currentText = promptElement.innerText || promptElement.textContent;
            console.log('发送的内容:', currentText);
            await this.sendPromptToMidjourney(currentText, mjButton);
        });

        // 确保父元素有相对定位
        const parentElement = promptElement.closest('div') || promptElement.parentElement;
        if (parentElement) {
            const originalPosition = window.getComputedStyle(parentElement).position;
            if (originalPosition === 'static') {
                parentElement.style.position = 'relative';
            }

            parentElement.appendChild(mjButton);

            // 保存M按钮的引用到代码块元素上，用于后续添加复原按钮
            promptElement.mjButton = mjButton;

            // 只记录元素已被处理，不需要存储按钮引用
            // this.promptElements.add(promptElement); // 这个在调用处已经添加了
        }
    }

    // 设置代码块为可编辑状态
    setupEditableCodeBlock(promptElement) {
        // 默认保持只读状态，不干扰Claude原生功能
        promptElement.contentEditable = 'false';

        // 添加点击事件，点击时才变为可编辑
        promptElement.addEventListener('click', () => {
            if (promptElement.contentEditable === 'false') {
                promptElement.contentEditable = 'true';
                promptElement.focus();
            }
        });

        // 添加失去焦点事件，失去焦点时恢复只读
        promptElement.addEventListener('blur', () => {
            promptElement.contentEditable = 'false';
        });

        // 添加输入监听器来检测修改
        promptElement.addEventListener('input', () => {
            this.checkForModifications(promptElement);
        });
    }

    // 检测内容是否被修改
    checkForModifications(promptElement) {
        // 使用innerText来比较用户实际编辑的文本内容
        const currentContent = promptElement.innerText || promptElement.textContent;
        const originalTextContent = promptElement.dataset.originalText;
        const mjButton = promptElement.mjButton;

        console.log('检测修改:', {
            current: currentContent.substring(0, 50) + '...',
            original: originalTextContent.substring(0, 50) + '...',
            modified: currentContent !== originalTextContent
        });

        if (currentContent !== originalTextContent) {
            // 内容被修改，创建或显示复原按钮
            if (!promptElement.restoreButton) {
                this.createRestoreButton(promptElement, mjButton);
            } else {
                promptElement.restoreButton.style.display = 'flex';
            }
        } else if (promptElement.restoreButton) {
            // 内容未修改或已恢复，隐藏复原按钮
            promptElement.restoreButton.style.display = 'none';
        }
    }

    // 创建复原按钮
    createRestoreButton(promptElement, mjButton) {
        const restoreButton = document.createElement('div');
        restoreButton.className = 'claude-mj-floating-button mj-restore-btn';
        restoreButton.style.cssText = `
            position: absolute;
            right: -84px;
            top: 50%;
            transform: translateY(-50%);
            width: 36px;
            height: 36px;
            background: linear-gradient(135deg, #EF4444, #DC2626);
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 3px 12px rgba(239, 68, 68, 0.4);
            transition: all 0.3s ease;
            opacity: 0.9;
            z-index: 10000;
        `;

        restoreButton.innerHTML = `
            <svg width="18" height="18" viewBox="0 0 24 24" fill="white">
                <path d="M12.5 8c-2.65 0-5.05.99-6.9 2.6L2 7v9h9l-3.62-3.62c1.39-1.16 3.16-1.88 5.12-1.88 3.54 0 6.55 2.31 7.6 5.5l2.37-.78C21.08 11.03 17.15 8 12.5 8z"/>
            </svg>
        `;

        restoreButton.title = '恢复原始内容';

        // 复原按钮悬停效果
        restoreButton.addEventListener('mouseenter', () => {
            restoreButton.style.opacity = '1';
            restoreButton.style.transform = 'translateY(-50%) scale(1.1)';
            restoreButton.style.boxShadow = '0 4px 16px rgba(239, 68, 68, 0.6)';
        });

        restoreButton.addEventListener('mouseleave', () => {
            restoreButton.style.opacity = '0.9';
            restoreButton.style.transform = 'translateY(-50%) scale(1)';
            restoreButton.style.boxShadow = '0 3px 12px rgba(239, 68, 68, 0.4)';
        });

        // 复原按钮点击事件
        restoreButton.addEventListener('click', (e) => {
            e.stopPropagation();
            this.restoreOriginalContent(promptElement, restoreButton);
        });

        // 添加到父元素
        const parentElement = promptElement.closest('div') || promptElement.parentElement;
        if (parentElement) {
            parentElement.appendChild(restoreButton);
            promptElement.restoreButton = restoreButton;
        }
    }

    // 恢复原始内容
    restoreOriginalContent(promptElement, restoreButton) {
        const originalContent = promptElement.dataset.originalContent;
        if (originalContent) {
            promptElement.innerHTML = originalContent;

            // 重新同步originalText，确保检测逻辑正确
            promptElement.dataset.originalText = promptElement.innerText || promptElement.textContent;

            // 设置为只读状态
            promptElement.contentEditable = 'false';

            // 隐藏复原按钮
            restoreButton.style.display = 'none';

            // 显示恢复通知
            this.showEditNotification('🔄 已恢复原始内容', '#7C3AED');
        }
    }

    // 显示编辑状态通知
    showEditNotification(message, color) {
        const notification = document.createElement('div');
        notification.style.cssText = `
            position: fixed;
            top: 80px;
            right: 20px;
            background: ${color};
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            z-index: 10001;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            font-size: 13px;
            font-weight: 500;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            animation: editSlideIn 0.3s ease-out;
            backdrop-filter: blur(10px);
        `;

        notification.textContent = message;

        // 添加动画样式
        if (!document.getElementById('edit-notification-styles')) {
            const style = document.createElement('style');
            style.id = 'edit-notification-styles';
            style.textContent = `
                @keyframes editSlideIn {
                    from { transform: translateX(100%); opacity: 0; }
                    to { transform: translateX(0); opacity: 1; }
                }
                @keyframes editSlideOut {
                    from { transform: translateX(0); opacity: 1; }
                    to { transform: translateX(100%); opacity: 0; }
                }
            `;
            document.head.appendChild(style);
        }

        document.body.appendChild(notification);

        // 2秒后移除通知
        setTimeout(() => {
            notification.style.animation = 'editSlideOut 0.3s ease-in';
            setTimeout(() => {
                notification.remove();
            }, 300);
        }, 2000);
    }

    async sendPromptToMidjourney(promptText, buttonElement) {
        console.log('发送prompt到Midjourney:', promptText.substring(0, 100) + '...');

        // 显示发送状态
        this.showSendingStatus(buttonElement);

        try {
            // 通过background script查找Midjourney标签页
            const response = await this.safeChromeMessage({
                action: 'findMidjourneyTabs'
            });

            if (!response.success) {
                this.showError(buttonElement, '查找页面失败');
                return;
            }

            const midjourneyTabs = response.tabs;

            if (midjourneyTabs.length === 0) {
                // 如果没有找到，显示页面选择器
                this.showPageSelector(promptText, buttonElement);
                return;
            }

            // 如果只有一个Midjourney页面，直接发送
            if (midjourneyTabs.length === 1) {
                await this.sendToSpecificTab(midjourneyTabs[0], promptText, buttonElement);
                return;
            }

            // 如果有多个页面，优先选择imagine页面
            const preferredTab = this.findPreferredMidjourneyTab(midjourneyTabs);
            if (preferredTab) {
                console.log('自动选择首选页面:', preferredTab.url);
                await this.sendToSpecificTab(preferredTab, promptText, buttonElement);
            } else {
                // 如果没有首选页面，让用户选择
                this.showTabSelector(midjourneyTabs, promptText, buttonElement);
            }

        } catch (error) {
            console.error('发送prompt失败:', error);
            this.showError(buttonElement, '发送失败');
        }
    }

    showPageSelector(promptText, buttonElement) {
        // 创建页面选择对话框
        const modal = document.createElement('div');
        modal.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.7);
            z-index: 10000;
            display: flex;
            align-items: center;
            justify-content: center;
        `;

        const dialog = document.createElement('div');
        dialog.style.cssText = `
            background: white;
            padding: 30px;
            border-radius: 15px;
            max-width: 500px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        `;

        dialog.innerHTML = `
            <h3 style="color: #7C3AED; margin-bottom: 20px;">选择Midjourney页面</h3>
            <p style="margin-bottom: 25px; color: #666;">请选择要发送prompt的目标页面：</p>
            <div style="margin-bottom: 25px;">
                <button id="select-web" style="
                    background: linear-gradient(135deg, #7C3AED, #3B82F6);
                    color: white;
                    border: none;
                    padding: 12px 24px;
                    border-radius: 8px;
                    margin: 5px;
                    cursor: pointer;
                    font-size: 16px;
                ">Midjourney网页版</button>
                <button id="select-discord" style="
                    background: linear-gradient(135deg, #5865F2, #7289DA);
                    color: white;
                    border: none;
                    padding: 12px 24px;
                    border-radius: 8px;
                    margin: 5px;
                    cursor: pointer;
                    font-size: 16px;
                ">Discord Midjourney</button>
            </div>
            <p style="font-size: 14px; color: #999; margin-bottom: 20px;">
                请确保目标页面已经打开并且可以接收prompt
            </p>
            <button id="cancel-select" style="
                background: #ccc;
                color: #666;
                border: none;
                padding: 8px 16px;
                border-radius: 5px;
                cursor: pointer;
            ">取消</button>
        `;

        modal.appendChild(dialog);
        document.body.appendChild(modal);

        // 绑定事件
        document.getElementById('select-web').onclick = async () => {
            document.body.removeChild(modal);
            await this.sendToMidjourneyWeb(promptText, buttonElement);
        };

        document.getElementById('select-discord').onclick = async () => {
            document.body.removeChild(modal);
            await this.sendToDiscord(promptText, buttonElement);
        };

        document.getElementById('cancel-select').onclick = () => {
            document.body.removeChild(modal);
            this.showError(buttonElement, '已取消发送');
        };

        // 点击背景关闭
        modal.onclick = (e) => {
            if (e.target === modal) {
                document.body.removeChild(modal);
                this.showError(buttonElement, '已取消发送');
            }
        };
    }

    async sendToMidjourneyWeb(promptText, buttonElement) {
        try {
            // 通过background script查找Midjourney网页版
            const response = await this.safeChromeMessage({
                action: 'findMidjourneyTabs'
            });

            if (!response.success) {
                this.showError(buttonElement, '查找页面失败');
                return;
            }

            const webTabs = response.tabs.filter(tab =>
                tab.url && tab.url.includes('midjourney.com')
            );

            if (webTabs.length === 0) {
                // 通过background script打开新的Midjourney页面
                const openResponse = await this.safeChromeMessage({
                    action: 'openMidjourneyPage',
                    url: 'https://www.midjourney.com/imagine'
                });

                if (openResponse.success) {
                    // 等待页面加载
                    setTimeout(async () => {
                        await this.sendToSpecificTab(openResponse.tab, promptText, buttonElement);
                    }, 3000);
                } else {
                    this.showError(buttonElement, '打开页面失败');
                }
            } else {
                await this.sendToSpecificTab(webTabs[0], promptText, buttonElement);
            }
        } catch (error) {
            console.error('发送到网页版失败:', error);
            this.showError(buttonElement, '发送失败');
        }
    }

    async sendToDiscord(promptText, buttonElement) {
        try {
            // 通过background script查找Discord页面
            const response = await this.safeChromeMessage({
                action: 'findMidjourneyTabs'
            });

            if (!response.success) {
                this.showError(buttonElement, '查找页面失败');
                return;
            }

            const discordTabs = response.tabs.filter(tab =>
                tab.url && tab.url.includes('discord.com')
            );

            if (discordTabs.length === 0) {
                this.showError(buttonElement, '请先打开Discord页面');
                return;
            }

            // 发送到Discord（需要特殊处理，添加/imagine命令）
            await this.sendToSpecificTab(discordTabs[0], '/imagine ' + promptText, buttonElement);
        } catch (error) {
            console.error('发送到Discord失败:', error);
            this.showError(buttonElement, '发送失败');
        }
    }

    async sendToSpecificTab(tab, promptText, buttonElement) {
        this.showSendingStatus(buttonElement);

        // 设置超时保护，防止按钮卡在加载状态
        const timeoutId = setTimeout(() => {
            console.warn('⚠️ 发送超时，恢复按钮状态');
            this.showError(buttonElement, '发送超时，请重试');
        }, 10000); // 10秒超时

        try {
            console.log('发送prompt到标签页:', tab.id, tab.title);

            // 通过background script发送到指定标签页
            const response = await Promise.race([
                this.safeChromeMessage({
                    action: 'sendToMidjourneyTab',
                    tabId: tab.id,
                    prompt: promptText
                }),
                // 添加超时Promise
                new Promise((_, reject) =>
                    setTimeout(() => reject(new Error('发送超时')), 8000)
                )
            ]);

            // 清除超时
            clearTimeout(timeoutId);

            console.log('发送响应:', response);

            if (response && response.success) {
                console.log('✅ 检测到成功响应，显示绿色勾号');
                this.showSuccess(buttonElement);
                console.log('prompt发送成功');
            } else {
                console.log('❌ 响应不成功:', { response, hasResponse: !!response, hasSuccess: response?.success });
                const errorMsg = response?.error || response?.message || '发送失败';
                console.error('发送失败:', errorMsg);
                this.showError(buttonElement, errorMsg);
            }
        } catch (error) {
            // 清除超时
            clearTimeout(timeoutId);

            console.error('发送到指定标签页失败:', error);

            // 根据错误类型提供更具体的错误信息
            let errorMessage = '发送失败';
            if (error.message) {
                if (error.message.includes('Extension context invalidated')) {
                    errorMessage = '扩展需要重新加载';
                } else if (error.message.includes('Tabs cannot be edited')) {
                    errorMessage = '标签页正忙，请稍后重试';
                } else if (error.message.includes('No tab with id')) {
                    errorMessage = '目标页面已关闭';
                } else if (error.message.includes('Could not establish connection')) {
                    errorMessage = 'Midjourney页面需要刷新';
                } else if (error.message.includes('发送超时')) {
                    errorMessage = '发送超时，请重试';
                } else {
                    errorMessage = '发送失败: ' + error.message;
                }
            }

            this.showError(buttonElement, errorMessage);
        }
    }

    findPreferredMidjourneyTab(midjourneyTabs) {
        console.log('查找首选Midjourney页面，总数:', midjourneyTabs.length);

        // 优先级列表（从高到低）
        const preferredUrls = [
            'https://www.midjourney.com/imagine',
            'https://www.midjourney.com/create',
            'https://midjourney.com/imagine',
            'https://midjourney.com/create'
        ];

        // 按优先级查找
        for (const preferredUrl of preferredUrls) {
            const foundTab = midjourneyTabs.find(tab =>
                tab.url && tab.url.toLowerCase().includes(preferredUrl.toLowerCase())
            );
            if (foundTab) {
                console.log('找到首选页面:', foundTab.url);
                return foundTab;
            }
        }

        // 如果没有找到首选页面，查找包含关键词的页面
        const keywordPriority = ['imagine', 'create', 'app'];
        for (const keyword of keywordPriority) {
            const foundTab = midjourneyTabs.find(tab =>
                tab.url && tab.url.toLowerCase().includes(keyword)
            );
            if (foundTab) {
                console.log('找到包含关键词的页面:', foundTab.url);
                return foundTab;
            }
        }

        console.log('未找到首选页面，将显示选择器');
        return null;
    }

    showTabSelector(midjourneyTabs, promptText, buttonElement) {
        console.log('显示标签页选择器，标签页数量:', midjourneyTabs.length);

        // 创建标签页选择对话框
        const modal = document.createElement('div');
        modal.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.7);
            z-index: 10000;
            display: flex;
            align-items: center;
            justify-content: center;
        `;

        const dialog = document.createElement('div');
        dialog.style.cssText = `
            background: white;
            padding: 30px;
            border-radius: 15px;
            max-width: 500px;
            text-align: center;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        `;

        // 生成标签页列表HTML
        let tabsHtml = '';
        midjourneyTabs.forEach((tab, index) => {
            const title = tab.title || 'Midjourney页面';
            const url = tab.url || '';
            tabsHtml += `
                <div class="tab-item" data-tab-id="${tab.id}" style="
                    padding: 15px;
                    margin: 10px 0;
                    border: 2px solid #ddd;
                    border-radius: 8px;
                    cursor: pointer;
                    transition: all 0.3s ease;
                    background: #f8f9fa;
                ">
                    <div style="font-weight: bold; margin-bottom: 5px;">${title}</div>
                    <div style="font-size: 12px; color: #666; word-break: break-all;">${url}</div>
                </div>
            `;
        });

        dialog.innerHTML = `
            <h3 style="color: #7C3AED; margin-bottom: 20px;">🎯 选择Midjourney页面</h3>
            <p style="margin-bottom: 25px; color: #666;">找到多个Midjourney页面，请选择要发送prompt的页面：</p>

            <div style="margin-bottom: 25px; max-height: 300px; overflow-y: auto;">
                ${tabsHtml}
            </div>

            <button id="cancel-tab-select" style="
                background: #ccc;
                color: #666;
                border: none;
                padding: 10px 20px;
                border-radius: 8px;
                cursor: pointer;
                font-size: 14px;
            ">
                取消
            </button>
        `;

        modal.appendChild(dialog);
        document.body.appendChild(modal);
        console.log('标签页选择器已添加到页面');

        // 绑定标签页点击事件
        const tabItems = dialog.querySelectorAll('.tab-item');
        tabItems.forEach(tabItem => {
            tabItem.addEventListener('mouseenter', () => {
                tabItem.style.borderColor = '#7C3AED';
                tabItem.style.background = '#f3f0ff';
            });

            tabItem.addEventListener('mouseleave', () => {
                tabItem.style.borderColor = '#ddd';
                tabItem.style.background = '#f8f9fa';
            });

            tabItem.onclick = async () => {
                const tabId = parseInt(tabItem.dataset.tabId);
                const selectedTab = midjourneyTabs.find(tab => tab.id === tabId);

                if (selectedTab) {
                    console.log('选择了标签页:', selectedTab.title);
                    document.body.removeChild(modal);
                    await this.sendToSpecificTab(selectedTab, promptText, buttonElement);
                }
            };
        });

        // 绑定取消按钮事件
        document.getElementById('cancel-tab-select').onclick = () => {
            document.body.removeChild(modal);
            this.showError(buttonElement, '已取消发送');
        };

        // 点击背景关闭
        modal.onclick = (e) => {
            if (e.target === modal) {
                document.body.removeChild(modal);
                this.showError(buttonElement, '已取消发送');
            }
        };
    }

    showSendingStatus(buttonElement) {
        buttonElement.style.background = 'linear-gradient(135deg, #FF9800, #F57C00)';
        buttonElement.innerHTML = `
            <svg width="16" height="16" viewBox="0 0 24 24" fill="white">
                <circle cx="12" cy="12" r="10" stroke="white" stroke-width="2" fill="none" stroke-dasharray="31.416" stroke-dashoffset="31.416">
                    <animate attributeName="stroke-dasharray" dur="2s" values="0 31.416;15.708 15.708;0 31.416" repeatCount="indefinite"/>
                    <animate attributeName="stroke-dashoffset" dur="2s" values="0;-15.708;-31.416" repeatCount="indefinite"/>
                </circle>
            </svg>
        `;

        // 添加提示文字
        buttonElement.title = '正在后台发送到Midjourney...';
    }

    showSuccess(buttonElement) {
        buttonElement.style.background = 'linear-gradient(135deg, #4CAF50, #388E3C)';
        buttonElement.innerHTML = `
            <svg width="16" height="16" viewBox="0 0 24 24" fill="white">
                <path d="M9,20.42L2.79,14.21L5.62,11.38L9,14.77L18.88,4.88L21.71,7.71L9,20.42Z"/>
            </svg>
        `;

        // 添加成功提示文字
        buttonElement.title = '已后台发送到Midjourney！';

        // 显示临时通知
        this.showBackgroundSendNotification();

        // 3秒后恢复原状
        setTimeout(() => {
            buttonElement.style.background = 'linear-gradient(135deg, #7C3AED, #3B82F6)';
            buttonElement.innerHTML = `
                <svg width="18" height="18" viewBox="0 0 24 24" fill="white">
                    <path d="M3,21V3H7L12,14L17,3H21V21H17V8L13,18H11L7,8V21H3Z" stroke="white" stroke-width="0.5"/>
                </svg>
            `;
            buttonElement.title = '发送到Midjourney';
        }, 3000);
    }

    showBackgroundSendNotification() {
        // 创建临时通知
        const notification = document.createElement('div');
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: linear-gradient(135deg, #4CAF50, #388E3C);
            color: white;
            padding: 12px 20px;
            border-radius: 8px;
            z-index: 10000;
            font-family: Arial, sans-serif;
            font-size: 14px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
            animation: slideIn 0.3s ease-out;
        `;

        notification.innerHTML = `
            <div style="display: flex; align-items: center; gap: 8px;">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="white">
                    <path d="M9,20.42L2.79,14.21L5.62,11.38L9,14.77L18.88,4.88L21.71,7.71L9,20.42Z"/>
                </svg>
                <span>已后台发送到Midjourney</span>
            </div>
        `;

        // 添加动画样式
        if (!document.getElementById('background-send-styles')) {
            const style = document.createElement('style');
            style.id = 'background-send-styles';
            style.textContent = `
                @keyframes slideIn {
                    from { transform: translateX(100%); opacity: 0; }
                    to { transform: translateX(0); opacity: 1; }
                }
                @keyframes slideOut {
                    from { transform: translateX(0); opacity: 1; }
                    to { transform: translateX(100%); opacity: 0; }
                }
            `;
            document.head.appendChild(style);
        }

        document.body.appendChild(notification);

        // 3秒后移除通知
        setTimeout(() => {
            notification.style.animation = 'slideOut 0.3s ease-in';
            setTimeout(() => {
                notification.remove();
            }, 300);
        }, 3000);
    }

    showError(buttonElement, message) {
        buttonElement.style.background = 'linear-gradient(135deg, #f44336, #d32f2f)';
        buttonElement.innerHTML = `
            <svg width="16" height="16" viewBox="0 0 24 24" fill="white">
                <path d="M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z"/>
            </svg>
        `;

        // 显示错误提示
        console.error('发送失败:', message);

        // 3秒后恢复原状
        setTimeout(() => {
            buttonElement.style.background = 'linear-gradient(135deg, #7C3AED, #3B82F6)';
            buttonElement.innerHTML = `
                <svg width="18" height="18" viewBox="0 0 24 24" fill="white">
                    <path d="M3,21V3H7L12,14L17,3H21V21H17V8L13,18H11L7,8V21H3Z" stroke="white" stroke-width="0.5"/>
                </svg>
            `;
        }, 3000);
    }

    removeFloatingButtons() {
        console.log('🗑️ 移除所有现有的浮标按钮');
        console.trace('📍 removeFloatingButtons 调用堆栈:'); // 添加堆栈跟踪

        // 移除所有现有的按钮组（向后兼容）
        const existingButtonGroups = document.querySelectorAll('.claude-mj-button-group');
        console.log(`🗑️ 找到 ${existingButtonGroups.length} 个按钮组，准备移除`);

        existingButtonGroups.forEach((buttonGroup, index) => {
            console.log(`🗑️ 移除按钮组 ${index + 1}`);
            buttonGroup.remove();
        });

        // 移除所有单个按钮（新版本和旧版本）
        const existingButtons = document.querySelectorAll('.claude-mj-floating-button');
        console.log(`🗑️ 找到 ${existingButtons.length} 个按钮，准备移除`);

        existingButtons.forEach((button, index) => {
            console.log(`🗑️ 移除按钮 ${index + 1}`);
            button.remove();
        });

        // 清理代码块的引用
        this.promptElements.forEach(element => {
            // 移除M按钮的引用
            if (element.mjButton) {
                delete element.mjButton;
            }
            // 移除复原按钮的引用
            if (element.restoreButton) {
                delete element.restoreButton;
            }
        });

        this.promptElements.clear();
    }

    debugPromptElements() {
        console.log('=== 调试信息 ===');
        console.log('页面URL:', window.location.href);

        // 查找所有包含CINEMATIC STILL的元素
        const allElements = document.querySelectorAll('*');
        const promptElements = [];

        allElements.forEach(element => {
            const text = element.textContent || element.innerText;
            if (text && text.includes('(CINEMATIC STILL)')) {
                promptElements.push({
                    element: element,
                    tagName: element.tagName,
                    className: element.className,
                    id: element.id,
                    textLength: text.length,
                    textPreview: text.substring(0, 100) + '...'
                });
            }
        });

        console.log('找到包含CINEMATIC STILL的元素:', promptElements);

        // 查找可能的容器元素
        const containers = document.querySelectorAll('div, pre, code, span, p');
        const darkContainers = [];

        containers.forEach(container => {
            const styles = window.getComputedStyle(container);
            const bgColor = styles.backgroundColor;
            const text = container.textContent || container.innerText;

            if (text && text.includes('(CINEMATIC STILL)') &&
                (bgColor.includes('rgb(') &&
                 (bgColor.includes('0, 0, 0') || bgColor.includes('33, 33, 33') ||
                  bgColor.includes('45, 45, 45') || bgColor.includes('55, 55, 55')))) {
                darkContainers.push({
                    element: container,
                    tagName: container.tagName,
                    className: container.className,
                    backgroundColor: bgColor,
                    textPreview: text.substring(0, 100) + '...'
                });
            }
        });

        console.log('找到深色背景的容器:', darkContainers);

        // 输出页面结构信息
        console.log('页面主要结构:');
        const mainContainers = document.querySelectorAll('main, article, section, [role="main"]');
        mainContainers.forEach(container => {
            console.log('主容器:', container.tagName, container.className);
        });
    }



    // 专门识别Claude代码块格式
    isClaudeCodeBlock(element) {
        if (!element) return false;

        // 检查标签名
        const tagName = element.tagName.toLowerCase();
        const className = element.className || '';

        // 1. 直接是pre或code标签
        if (tagName === 'pre' || tagName === 'code') {
            return true;
        }

        // 2. 检查Claude特有的代码块类名
        if (className.includes('code-block') ||
            className.includes('code') ||
            className.includes('highlight') ||
            className.includes('whitespace-pre-wrap')) {
            return true;
        }

        // 3. 检查是否在代码块容器内
        const codeBlockParent = element.closest('pre, code, [class*="code"]');
        if (codeBlockParent) {
            return true;
        }

        // 4. 检查样式特征（字体等）
        try {
            const styles = window.getComputedStyle(element);
            const fontFamily = styles.fontFamily.toLowerCase();
            if (fontFamily.includes('mono') || fontFamily.includes('courier') || fontFamily.includes('consolas')) {
                return true;
            }
        } catch (e) {
            // 忽略样式检查错误
        }

        return false;
    }

    // 判断元素是否是代码块样式（保留原方法作为备用）
    isCodeBlockElement(element) {
        return this.isClaudeCodeBlock(element);
    }



    // 监听键盘快捷键
    setupKeyboardShortcuts() {
        document.addEventListener('keydown', (event) => {
            // Ctrl+Shift+M 强制重新检查所有代码块
            if (event.ctrlKey && event.shiftKey && event.key === 'M') {
                event.preventDefault();
                console.log('🔄 手动触发强制重新检查所有代码块...');
                this.forceRecheck();
            }
        });

        console.log('✅ 键盘快捷键监听器已启动');
    }



    // 智能添加缺失的按钮，不影响现有按钮状态
    addMissingButtons() {
        console.log('🎯 智能添加缺失的M按钮，保护现有按钮状态');

        let addedCount = 0;

        // 获取主要内容区域
        const mainContentArea = this.getMainContentArea();
        if (!mainContentArea) {
            console.log('未找到主要内容区域');
            return;
        }

        // 查找代码块选择器
        const codeBlockSelectors = [
            'pre.code-block__code',
            'pre[class*="code-block"]',
            'pre code',
            'pre',
            'code',
            '[class*="code-block"]',
            '[class*="code"]',
            '[class*="highlight"]',
            '.whitespace-pre-wrap',
            '[class*="prose"] pre',
            '[class*="prose"] code',
        ];

        codeBlockSelectors.forEach(selector => {
            try {
                const elements = mainContentArea.querySelectorAll(selector);
                elements.forEach((element, index) => {
                    const text = element.textContent || element.innerText;

                    // 检查是否已经有按钮
                    const hasButton = element.querySelector('.claude-mj-floating-btn') ||
                                    element.parentElement?.querySelector('.claude-mj-floating-btn') ||
                                    element.closest('.claude-mj-floating-btn') ||
                                    document.querySelector(`[data-prompt-index="${index}"]`);

                    // 只为没有按钮的代码块添加按钮
                    if (!hasButton &&
                        this.isClaudeCodeBlock(element) &&
                        text &&
                        text.trim().length > 10 &&
                        text.trim().length < 2000 &&
                        this.isInMainContent(element) &&
                        !this.promptElements.has(element)) {

                        console.log(`➕ 为缺失按钮的代码块添加M按钮: ${text.substring(0, 30)}...`);
                        this.createFloatingButton(element, text.trim(), `missing-${selector}-${index}`);
                        this.promptElements.add(element);
                        addedCount++;
                    }
                });
            } catch (error) {
                console.log(`选择器 ${selector} 查询失败:`, error);
            }
        });

        console.log(`✅ 智能添加了 ${addedCount} 个缺失的M按钮`);
    }

    // 查找所有代码块的辅助方法
    findAllCodeBlocks() {
        const codeBlocks = [];

        // 查找所有可能的代码块
        const selectors = [
            'pre.code-block__code',
            'pre',
            'code',
            '[class*="code-block"]',
            '[class*="highlight"]'
        ];

        selectors.forEach(selector => {
            const elements = document.querySelectorAll(selector);
            elements.forEach(element => {
                if (this.isClaudeCodeBlock(element)) {
                    const text = element.textContent || element.innerText;
                    if (text && text.trim().length > 10 && text.trim().length < 2000) {
                        codeBlocks.push(element);
                    }
                }
            });
        });

        // 去重
        return [...new Set(codeBlocks)];
    }

    // 强制重新检查所有代码块
    forceRecheck() {
        console.log('🔄 强制重新检查开始...');

        // 清空所有缓存
        this.promptElements.clear();

        // 移除所有现有按钮
        this.removeFloatingButtons();

        // 重新添加按钮
        setTimeout(() => {
            console.log('🔄 开始重新添加所有按钮...');
            this.addFloatingButtons();
        }, 500);

        // 显示提示
        const notification = document.createElement('div');
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: #007bff;
            color: white;
            padding: 10px 20px;
            border-radius: 5px;
            z-index: 10000;
            font-family: Arial, sans-serif;
            font-size: 14px;
        `;
        notification.textContent = '🔄 正在重新检查所有代码块...';
        document.body.appendChild(notification);

        setTimeout(() => {
            notification.remove();
        }, 3000);
    }

    // 获取主要内容区域，排除侧边栏
    getMainContentArea() {
        // 尝试多种方式找到主要内容区域
        const selectors = [
            'main',
            '[role="main"]',
            '.main-content',
            '.conversation',
            '.chat-content',
            '.messages',
            'div[class*="conversation"]',
            'div[class*="chat"]',
            'div[class*="main"]'
        ];

        for (const selector of selectors) {
            const element = document.querySelector(selector);
            if (element) {
                console.log('找到主要内容区域:', selector);
                return element;
            }
        }

        // 如果没有找到特定的主内容区域，使用整个document
        console.log('未找到特定主内容区域，使用整个document');
        return document;
    }

    // 检查元素是否在主要内容区域（不在侧边栏）
    isInMainContent(element) {
        // 检查元素是否在明确的侧边栏导航中
        const sidebar = element.closest('nav, aside, [class*="sidebar"], [class*="nav"], [role="navigation"]');
        if (sidebar) {
            console.log('元素在侧边栏导航中，跳过:', element.textContent?.substring(0, 30));
            return false;
        }

        // 检查元素是否在聊天历史列表中
        const chatList = element.closest('[class*="chat"], [class*="conversation"], [class*="history"], [class*="list"]');
        if (chatList) {
            // 进一步检查是否真的是聊天列表（而不是聊天内容）
            const rect = chatList.getBoundingClientRect();
            if (rect.left < 300) { // 如果在左侧，很可能是聊天列表
                console.log('元素在聊天列表中，跳过:', element.textContent?.substring(0, 30));
                return false;
            }
        }

        // 检查元素的位置 - 加强侧边栏检测
        const rect = element.getBoundingClientRect();
        const windowWidth = window.innerWidth;

        // 如果元素在左侧300px内，很可能是侧边栏
        if (rect.left < 300 && rect.left + rect.width < windowWidth * 0.4) {
            console.log('元素位置在侧边栏区域，跳过:', element.textContent?.substring(0, 30));
            return false;
        }

        // 检查元素的父级是否有侧边栏特征
        let parent = element.parentElement;
        while (parent && parent !== document.body) {
            const parentRect = parent.getBoundingClientRect();
            // 如果父级元素在左侧且宽度较小，可能是侧边栏
            if (parentRect.left < 50 && parentRect.width < 350) {
                console.log('元素的父级疑似侧边栏，跳过:', element.textContent?.substring(0, 30));
                return false;
            }
            parent = parent.parentElement;
        }

        return true;
    }

    // 备用方法：如果主内容区域检测失败，使用原来的简单方法
    addFloatingButtonsFallback() {
        console.log('使用备用方法添加浮标按钮（仅代码块）...');

        let addedCount = 0;

        // 备用方法也只搜索代码块样式的元素
        const codeBlockSelectors = [
            'pre', 'code',
            '[class*="code"]', '[class*="highlight"]',
            '.whitespace-pre-wrap',
            'div[style*="background"]', 'div[style*="border"]'
        ];

        codeBlockSelectors.forEach(selector => {
            try {
                const elements = document.querySelectorAll(selector);
                elements.forEach((element, index) => {
                    const text = element.textContent || element.innerText;

                    // 简化检查：只要是Claude代码块格式
                    if (this.isClaudeCodeBlock(element) &&
                        text &&
                        text.trim().length > 10 &&
                        text.trim().length < 2000 &&
                        !this.promptElements.has(element)) {

                        // 加强的侧边栏检测
                        const rect = element.getBoundingClientRect();
                        const windowWidth = window.innerWidth;

                        // 检查是否在侧边栏区域
                        const isLikelySidebar = rect.left < 300 && rect.left + rect.width < windowWidth * 0.4;

                        // 检查是否在导航容器中
                        const inNavigation = element.closest('nav, aside, [class*="sidebar"], [class*="nav"], [role="navigation"]');

                        if (!isLikelySidebar && !inNavigation) {
                            console.log('✅ 备用方法找到Claude代码块:', text.substring(0, 50) + '...');
                            this.createFloatingButton(element, text.trim(), `fallback-${selector}-${index}`);
                            this.promptElements.add(element);
                            addedCount++;
                        } else {
                            console.log('备用方法跳过侧边栏代码块:', text.substring(0, 30));
                        }
                    }
                });
            } catch (error) {
                console.log(`备用方法选择器 ${selector} 查询失败:`, error);
            }
        });

        console.log(`备用方法添加了 ${addedCount} 个浮标按钮`);
    }



    // 手动检测/切换
    manualDetect() {
        if (this.buttonsVisible) {
            // 如果按钮可见，则清除它们
            console.log('🧹 清除M按钮');
            this.removeFloatingButtons();
            this.buttonsVisible = false;
            this.showManualDetectNotification(false);
        } else {
            // 如果按钮不可见，则添加它们
            console.log('🔍 手动检测触发');
            this.showManualDetectNotification(true);
            this.addFloatingButtons();
            this.autoExtractAndCache();
            this.buttonsVisible = true;
        }
    }



    // 显示手动检测通知
    showManualDetectNotification(isDetecting = true) {
        if (isDetecting) {
            this.showMidjourneyNotification('🔍 手动检测', '正在检测页面中的prompts...', '#4285f4');
        } else {
            this.showMidjourneyNotification('🧹 清除M按钮', '已清除所有M按钮', '#f44336');
        }
    }

    // Midjourney风格通知显示方法
    showMidjourneyNotification(title, message, color = '#4285f4') {
        const notification = document.createElement('div');
        notification.className = 'midjourney-notification';
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${color};
            color: white;
            padding: 0;
            border-radius: 12px;
            z-index: 10000;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            box-shadow: 0 8px 32px rgba(0,0,0,0.24);
            animation: mjSlideIn 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
            max-width: 320px;
            min-width: 280px;
            overflow: hidden;
            backdrop-filter: blur(10px);
        `;

        // 创建渐变背景
        const gradient = color === '#4285f4' ?
            'linear-gradient(135deg, #4285f4 0%, #34a853 100%)' :
            'linear-gradient(135deg, #f44336 0%, #e91e63 100%)';

        notification.innerHTML = `
            <div style="
                background: ${gradient};
                padding: 16px 20px;
                position: relative;
                overflow: hidden;
            ">
                <div style="
                    position: absolute;
                    top: -50%;
                    right: -50%;
                    width: 100px;
                    height: 100px;
                    background: rgba(255,255,255,0.1);
                    border-radius: 50%;
                    animation: mjPulse 2s infinite;
                "></div>
                <div style="
                    display: flex;
                    align-items: center;
                    gap: 12px;
                    position: relative;
                    z-index: 1;
                ">
                    <div style="
                        width: 32px;
                        height: 32px;
                        background: rgba(255,255,255,0.2);
                        border-radius: 8px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        font-size: 16px;
                        backdrop-filter: blur(10px);
                    ">${title.split(' ')[0]}</div>
                    <div>
                        <div style="
                            font-weight: 600;
                            font-size: 15px;
                            margin-bottom: 2px;
                            letter-spacing: -0.01em;
                        ">${title.split(' ').slice(1).join(' ')}</div>
                        <div style="
                            font-size: 13px;
                            opacity: 0.9;
                            font-weight: 400;
                        ">${message}</div>
                    </div>
                </div>
            </div>
            <div style="
                background: rgba(255,255,255,0.1);
                height: 3px;
                width: 100%;
                animation: mjProgress 3s linear;
            "></div>
        `;

        // 添加Midjourney风格动画样式
        if (!document.getElementById('midjourney-notification-styles')) {
            const style = document.createElement('style');
            style.id = 'midjourney-notification-styles';
            style.textContent = `
                @keyframes mjSlideIn {
                    from {
                        transform: translateX(100%) scale(0.8);
                        opacity: 0;
                    }
                    to {
                        transform: translateX(0) scale(1);
                        opacity: 1;
                    }
                }
                @keyframes mjSlideOut {
                    from {
                        transform: translateX(0) scale(1);
                        opacity: 1;
                    }
                    to {
                        transform: translateX(100%) scale(0.8);
                        opacity: 0;
                    }
                }
                @keyframes mjPulse {
                    0%, 100% {
                        transform: scale(1);
                        opacity: 0.1;
                    }
                    50% {
                        transform: scale(1.2);
                        opacity: 0.2;
                    }
                }
                @keyframes mjProgress {
                    from { width: 100%; }
                    to { width: 0%; }
                }
                .midjourney-notification:hover {
                    transform: translateY(-2px);
                    transition: transform 0.2s ease;
                }
            `;
            document.head.appendChild(style);
        }

        document.body.appendChild(notification);

        // 3秒后移除通知
        setTimeout(() => {
            notification.style.animation = 'mjSlideOut 0.3s cubic-bezier(0.55, 0.085, 0.68, 0.53)';
            setTimeout(() => {
                notification.remove();
            }, 300);
        }, 3000);
    }

    // 通用通知显示方法（保留作为备用）
    showNotification(title, message, color = '#4CAF50') {
        // 使用新的Midjourney风格通知
        this.showMidjourneyNotification(title, message, color);
    }
}

// 初始化提取器
new ClaudePromptExtractor();
