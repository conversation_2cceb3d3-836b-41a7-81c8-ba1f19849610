<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>插件测试页面</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .title {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 3px solid #4CAF50;
            padding-bottom: 15px;
        }
        .test-section {
            margin: 25px 0;
            padding: 20px;
            border: 2px solid #ddd;
            border-radius: 8px;
        }
        .claude-test {
            border-color: #4CAF50;
            background-color: #f8fff8;
        }
        .midjourney-test {
            border-color: #FF9800;
            background-color: #fff8f0;
        }
        .button {
            background-color: #4CAF50;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        .button:hover {
            background-color: #45a049;
        }
        .button-orange {
            background-color: #FF9800;
        }
        .button-orange:hover {
            background-color: #e68900;
        }
        .result {
            margin: 15px 0;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .prompt-preview {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-size: 12px;
            max-height: 100px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">Claude to Midjourney 插件测试</h1>
        
        <div class="test-section claude-test">
            <h2>📝 Claude页面测试</h2>
            <p><strong>当前页面：</strong><span id="current-url">检测中...</span></p>
            <p><strong>说明：</strong>在Claude页面中测试prompt提取功能</p>
            
            <button class="button" onclick="testClaudeExtraction()">测试提取Prompts</button>
            <button class="button" onclick="testSaveToShared()">测试保存到共享存储</button>
            
            <div id="claude-result" class="result info" style="display: none;"></div>
            <div id="prompts-preview"></div>
        </div>

        <div class="test-section midjourney-test">
            <h2>🎨 Midjourney页面测试</h2>
            <p><strong>当前页面：</strong><span id="current-url-mj">检测中...</span></p>
            <p><strong>说明：</strong>在Midjourney页面中测试输入框识别和发送功能</p>
            
            <button class="button button-orange" onclick="testMidjourneyInputBox()">测试识别输入框</button>
            <button class="button button-orange" onclick="testLoadFromShared()">测试从共享存储加载</button>
            <button class="button button-orange" onclick="testSendPrompt()">测试发送Prompt</button>
            
            <div id="midjourney-result" class="result info" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h2>🔄 跨配置文件测试</h2>
            <p><strong>说明：</strong>测试在不同Chrome配置文件之间的数据传输</p>
            
            <button class="button" onclick="testCrossProfile()">测试跨配置文件通信</button>
            <button class="button" onclick="clearTestData()">清除测试数据</button>
            
            <div id="cross-profile-result" class="result info" style="display: none;"></div>
        </div>
    </div>

    <script>
        // 页面加载时检测当前环境
        window.onload = function() {
            document.getElementById('current-url').textContent = window.location.href;
            document.getElementById('current-url-mj').textContent = window.location.href;
            
            // 检测是否在正确的页面
            if (window.location.href.includes('claude.ai')) {
                document.querySelector('.claude-test').style.borderColor = '#4CAF50';
                document.querySelector('.claude-test').style.backgroundColor = '#e8f5e8';
            } else if (window.location.href.includes('midjourney.com')) {
                document.querySelector('.midjourney-test').style.borderColor = '#4CAF50';
                document.querySelector('.midjourney-test').style.backgroundColor = '#e8f5e8';
            }
        };

        // 测试Claude页面的prompt提取
        async function testClaudeExtraction() {
            const resultDiv = document.getElementById('claude-result');
            const previewDiv = document.getElementById('prompts-preview');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result info';
            resultDiv.textContent = '正在测试prompt提取...';

            try {
                // 模拟插件的提取逻辑
                const spanElements = document.querySelectorAll('span[class*="textContent"]');
                const prompts = [];
                
                spanElements.forEach(span => {
                    const text = span.textContent || span.innerText;
                    if (text && text.includes('(CINEMATIC STILL)')) {
                        prompts.push(text.trim());
                    }
                });

                if (prompts.length > 0) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ 成功提取到 ${prompts.length} 个prompts！`;
                    
                    // 显示预览
                    previewDiv.innerHTML = '<h4>提取到的Prompts:</h4>';
                    prompts.forEach((prompt, index) => {
                        const div = document.createElement('div');
                        div.className = 'prompt-preview';
                        div.textContent = `${index + 1}. ${prompt.substring(0, 200)}${prompt.length > 200 ? '...' : ''}`;
                        previewDiv.appendChild(div);
                    });
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = '❌ 未找到包含(CINEMATIC STILL)的prompts';
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = '❌ 提取失败: ' + error.message;
            }
        }

        // 测试保存到共享存储
        async function testSaveToShared() {
            const resultDiv = document.getElementById('claude-result');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result info';
            resultDiv.textContent = '正在测试保存到共享存储...';

            try {
                const testData = {
                    prompts: ['测试prompt 1', '测试prompt 2'],
                    timestamp: Date.now(),
                    source: 'test'
                };

                localStorage.setItem('claude_mj_shared_prompts', JSON.stringify(testData));
                
                resultDiv.className = 'result success';
                resultDiv.textContent = '✅ 成功保存到共享存储！';
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = '❌ 保存失败: ' + error.message;
            }
        }

        // 测试Midjourney输入框识别
        async function testMidjourneyInputBox() {
            const resultDiv = document.getElementById('midjourney-result');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result info';
            resultDiv.textContent = '正在测试输入框识别...';

            try {
                // 查找输入框
                const selectors = [
                    'textarea[placeholder="What will you imagine?"]',
                    'textarea.w-full.ml-1.bg-transparent',
                    'textarea[placeholder*="imagine"]',
                    'input[placeholder*="imagine"]'
                ];

                let foundInput = null;
                for (const selector of selectors) {
                    const element = document.querySelector(selector);
                    if (element) {
                        foundInput = element;
                        break;
                    }
                }

                if (foundInput) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ 成功找到输入框！\n标签: ${foundInput.tagName}\n占位符: ${foundInput.placeholder}\n类名: ${foundInput.className}`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = '❌ 未找到Midjourney输入框';
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = '❌ 测试失败: ' + error.message;
            }
        }

        // 测试从共享存储加载
        async function testLoadFromShared() {
            const resultDiv = document.getElementById('midjourney-result');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result info';
            resultDiv.textContent = '正在测试从共享存储加载...';

            try {
                const data = localStorage.getItem('claude_mj_shared_prompts');
                if (data) {
                    const parsed = JSON.parse(data);
                    resultDiv.className = 'result success';
                    resultDiv.textContent = `✅ 成功加载共享数据！\n包含 ${parsed.prompts ? parsed.prompts.length : 0} 个prompts\n时间戳: ${new Date(parsed.timestamp).toLocaleString()}`;
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = '❌ 共享存储中没有数据';
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = '❌ 加载失败: ' + error.message;
            }
        }

        // 测试发送prompt
        async function testSendPrompt() {
            const resultDiv = document.getElementById('midjourney-result');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result info';
            resultDiv.textContent = '正在测试发送prompt...';

            try {
                const inputBox = document.querySelector('textarea[placeholder="What will you imagine?"]') ||
                                document.querySelector('textarea.w-full.ml-1.bg-transparent');
                
                if (inputBox) {
                    const testPrompt = '(CINEMATIC STILL), (FILM BY Test), (测试prompt内容)';
                    inputBox.focus();
                    inputBox.value = testPrompt;
                    
                    // 触发事件
                    inputBox.dispatchEvent(new Event('input', { bubbles: true }));
                    
                    resultDiv.className = 'result success';
                    resultDiv.textContent = '✅ 成功在输入框中填入测试prompt！';
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = '❌ 未找到输入框';
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = '❌ 发送失败: ' + error.message;
            }
        }

        // 测试跨配置文件通信
        async function testCrossProfile() {
            const resultDiv = document.getElementById('cross-profile-result');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result info';
            resultDiv.textContent = '正在测试跨配置文件通信...';

            try {
                // 检查localStorage是否可用
                const testKey = 'cross_profile_test';
                const testValue = { test: true, timestamp: Date.now() };
                
                localStorage.setItem(testKey, JSON.stringify(testValue));
                const retrieved = JSON.parse(localStorage.getItem(testKey));
                
                if (retrieved && retrieved.test) {
                    resultDiv.className = 'result success';
                    resultDiv.textContent = '✅ 跨配置文件通信测试成功！\nLocalStorage可以正常读写';
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = '❌ 跨配置文件通信测试失败';
                }
                
                localStorage.removeItem(testKey);
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = '❌ 测试失败: ' + error.message;
            }
        }

        // 清除测试数据
        function clearTestData() {
            const resultDiv = document.getElementById('cross-profile-result');
            resultDiv.style.display = 'block';
            resultDiv.className = 'result info';
            
            try {
                localStorage.removeItem('claude_mj_shared_prompts');
                localStorage.removeItem('cross_profile_test');
                
                resultDiv.className = 'result success';
                resultDiv.textContent = '✅ 测试数据已清除';
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = '❌ 清除失败: ' + error.message;
            }
        }
    </script>
</body>
</html>
