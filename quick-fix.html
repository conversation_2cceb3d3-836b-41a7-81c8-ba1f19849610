<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>插件快速修复</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .title {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 3px solid #4CAF50;
            padding-bottom: 15px;
        }
        .fix-section {
            margin: 25px 0;
            padding: 20px;
            border: 2px solid #ddd;
            border-radius: 8px;
            background-color: #f8f9fa;
        }
        .success {
            border-color: #4CAF50;
            background-color: #e8f5e8;
        }
        .error {
            border-color: #f44336;
            background-color: #ffebee;
        }
        .button {
            background-color: #4CAF50;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        .button:hover {
            background-color: #45a049;
        }
        .code {
            background-color: #f4f4f4;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
            white-space: pre-wrap;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">插件快速修复工具</h1>
        
        <div class="fix-section">
            <h2>🔧 问题诊断</h2>
            <p>检测并修复常见的插件问题</p>
            <button class="button" onclick="diagnoseIssues()">开始诊断</button>
            <div id="diagnosis-result"></div>
        </div>

        <div class="fix-section">
            <h2>🚀 快速测试</h2>
            <p>测试插件的核心功能</p>
            <button class="button" onclick="testCoreFeatures()">测试核心功能</button>
            <div id="test-result"></div>
        </div>

        <div class="fix-section">
            <h2>💾 共享存储测试</h2>
            <p>测试跨配置文件的数据共享</p>
            <button class="button" onclick="testSharedStorage()">测试共享存储</button>
            <button class="button" onclick="clearAllData()">清除所有数据</button>
            <div id="storage-result"></div>
        </div>

        <div class="fix-section">
            <h2>📋 安装检查清单</h2>
            <div class="code">
✅ 检查项目：
1. 确保所有文件都在同一个文件夹中
2. 在Chrome中进入 chrome://extensions/
3. 开启"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择插件文件夹
6. 确保插件状态为"已启用"
7. 在两个Chrome配置文件中都安装插件
            </div>
        </div>
    </div>

    <script>
        function diagnoseIssues() {
            const resultDiv = document.getElementById('diagnosis-result');
            resultDiv.innerHTML = '<div class="status" style="background-color: #d1ecf1;">正在诊断...</div>';
            
            const issues = [];
            const fixes = [];
            
            // 检查浏览器兼容性
            if (typeof chrome === 'undefined') {
                issues.push('❌ Chrome扩展API不可用');
                fixes.push('请在Chrome浏览器中运行此插件');
            } else {
                fixes.push('✅ Chrome扩展API可用');
            }
            
            // 检查localStorage
            try {
                localStorage.setItem('test', 'test');
                localStorage.removeItem('test');
                fixes.push('✅ localStorage可用');
            } catch (error) {
                issues.push('❌ localStorage不可用: ' + error.message);
            }
            
            // 检查IndexedDB
            if (typeof indexedDB !== 'undefined') {
                fixes.push('✅ IndexedDB可用');
            } else {
                issues.push('❌ IndexedDB不可用');
            }
            
            // 检查File API
            if (typeof FileReader !== 'undefined') {
                fixes.push('✅ File API可用');
            } else {
                issues.push('❌ File API不可用');
            }
            
            // 显示结果
            let html = '';
            if (issues.length === 0) {
                html = '<div class="status success">🎉 所有检查都通过了！</div>';
            } else {
                html = '<div class="status error">发现以下问题：</div>';
                html += '<div class="code">' + issues.join('\n') + '</div>';
            }
            
            html += '<div class="status success">可用功能：</div>';
            html += '<div class="code">' + fixes.join('\n') + '</div>';
            
            resultDiv.innerHTML = html;
        }
        
        function testCoreFeatures() {
            const resultDiv = document.getElementById('test-result');
            resultDiv.innerHTML = '<div class="status" style="background-color: #d1ecf1;">正在测试...</div>';
            
            const tests = [];
            
            // 测试DOM查询
            try {
                const spans = document.querySelectorAll('span');
                tests.push(`✅ DOM查询正常 (找到${spans.length}个span元素)`);
            } catch (error) {
                tests.push('❌ DOM查询失败: ' + error.message);
            }
            
            // 测试事件处理
            try {
                const testEvent = new Event('test');
                document.dispatchEvent(testEvent);
                tests.push('✅ 事件处理正常');
            } catch (error) {
                tests.push('❌ 事件处理失败: ' + error.message);
            }
            
            // 测试JSON处理
            try {
                const testData = { test: true, prompts: ['test1', 'test2'] };
                const json = JSON.stringify(testData);
                const parsed = JSON.parse(json);
                tests.push('✅ JSON处理正常');
            } catch (error) {
                tests.push('❌ JSON处理失败: ' + error.message);
            }
            
            resultDiv.innerHTML = '<div class="code">' + tests.join('\n') + '</div>';
        }
        
        function testSharedStorage() {
            const resultDiv = document.getElementById('storage-result');
            resultDiv.innerHTML = '<div class="status" style="background-color: #d1ecf1;">正在测试共享存储...</div>';
            
            const tests = [];
            
            try {
                // 测试localStorage
                const testData = {
                    prompts: ['测试prompt 1', '测试prompt 2'],
                    timestamp: Date.now(),
                    source: 'test'
                };
                
                localStorage.setItem('claude_midjourney_prompts', JSON.stringify(testData));
                const retrieved = JSON.parse(localStorage.getItem('claude_midjourney_prompts'));
                
                if (retrieved && retrieved.prompts.length === 2) {
                    tests.push('✅ localStorage读写正常');
                    tests.push(`✅ 成功存储${retrieved.prompts.length}个测试prompts`);
                } else {
                    tests.push('❌ localStorage数据不完整');
                }
                
                // 测试文件下载
                try {
                    const blob = new Blob([JSON.stringify(testData)], { type: 'application/json' });
                    const url = URL.createObjectURL(blob);
                    URL.revokeObjectURL(url);
                    tests.push('✅ 文件下载功能正常');
                } catch (error) {
                    tests.push('❌ 文件下载功能异常: ' + error.message);
                }
                
                // 测试IndexedDB
                const request = indexedDB.open('TestDB', 1);
                request.onsuccess = () => {
                    tests.push('✅ IndexedDB连接正常');
                    request.result.close();
                    indexedDB.deleteDatabase('TestDB');
                    updateStorageResult();
                };
                request.onerror = () => {
                    tests.push('❌ IndexedDB连接失败');
                    updateStorageResult();
                };
                
                function updateStorageResult() {
                    resultDiv.innerHTML = '<div class="code">' + tests.join('\n') + '</div>';
                }
                
                // 如果IndexedDB测试没有在2秒内完成，就显示当前结果
                setTimeout(updateStorageResult, 2000);
                
            } catch (error) {
                tests.push('❌ 共享存储测试失败: ' + error.message);
                resultDiv.innerHTML = '<div class="code">' + tests.join('\n') + '</div>';
            }
        }
        
        function clearAllData() {
            const resultDiv = document.getElementById('storage-result');
            
            try {
                // 清除localStorage
                localStorage.removeItem('claude_midjourney_prompts');
                localStorage.removeItem('claude_mj_shared_prompts');
                
                // 清除IndexedDB
                indexedDB.deleteDatabase('ClaudeMidjourneyDB');
                
                resultDiv.innerHTML = '<div class="status success">✅ 所有测试数据已清除</div>';
            } catch (error) {
                resultDiv.innerHTML = '<div class="status error">❌ 清除数据失败: ' + error.message + '</div>';
            }
        }
        
        // 页面加载时自动运行诊断
        window.onload = function() {
            setTimeout(diagnoseIssues, 1000);
        };
    </script>
</body>
</html>
