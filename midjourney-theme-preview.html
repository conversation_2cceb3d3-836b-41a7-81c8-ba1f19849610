<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Midjourney主题预览</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 30px;
            border-radius: 20px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }
        .title {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 3px solid #7C3AED;
            padding-bottom: 15px;
            background: linear-gradient(135deg, #7C3AED, #3B82F6);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        .theme-section {
            margin: 25px 0;
            padding: 25px;
            border: 2px solid #7C3AED;
            border-radius: 15px;
            background: linear-gradient(135deg, rgba(124, 58, 237, 0.05), rgba(59, 130, 246, 0.05));
        }
        .floating-button {
            width: 48px;
            height: 48px;
            background: linear-gradient(135deg, #7C3AED, #3B82F6);
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 8px 25px rgba(124, 58, 237, 0.4);
            transition: all 0.3s ease;
            margin: 10px;
            position: relative;
            overflow: hidden;
        }
        .floating-button::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            transition: left 0.5s;
        }
        .floating-button:hover {
            transform: scale(1.1) rotate(5deg);
            box-shadow: 0 12px 35px rgba(124, 58, 237, 0.6);
        }
        .floating-button:hover::before {
            left: 100%;
        }
        .floating-button.large {
            width: 64px;
            height: 64px;
        }
        .floating-button.small {
            width: 32px;
            height: 32px;
        }
        .demo-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .demo-card {
            background: rgba(255, 255, 255, 0.8);
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            border: 1px solid rgba(124, 58, 237, 0.2);
            transition: all 0.3s ease;
        }
        .demo-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(124, 58, 237, 0.2);
        }
        .prompt-container {
            position: relative;
            margin: 20px 0;
            padding: 20px;
            background: linear-gradient(135deg, rgba(124, 58, 237, 0.1), rgba(59, 130, 246, 0.1));
            border-radius: 15px;
            border-left: 4px solid #7C3AED;
        }
        .prompt-text {
            font-family: 'Courier New', monospace;
            font-size: 14px;
            line-height: 1.6;
            color: #333;
        }
        .midjourney-logo {
            display: inline-block;
            font-weight: bold;
            font-size: 24px;
            background: linear-gradient(135deg, #7C3AED, #3B82F6);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        .feature-highlight {
            background: linear-gradient(135deg, #7C3AED, #3B82F6);
            color: white;
            padding: 15px;
            border-radius: 10px;
            margin: 15px 0;
        }
        .color-palette {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin: 20px 0;
        }
        .color-swatch {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 12px;
            text-align: center;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }
        .interactive-demo {
            text-align: center;
            padding: 30px;
            background: linear-gradient(135deg, rgba(124, 58, 237, 0.05), rgba(59, 130, 246, 0.05));
            border-radius: 15px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">🎨 <span class="midjourney-logo">Midjourney</span> 主题浮标设计</h1>
        
        <div class="theme-section">
            <h2>🌈 Midjourney品牌色彩</h2>
            <p>采用Midjourney官方的紫蓝色渐变主题，营造专业的AI艺术创作氛围</p>
            
            <div class="color-palette">
                <div class="color-swatch" style="background: #7C3AED;">
                    紫色
                    #7C3AED
                </div>
                <div class="color-swatch" style="background: linear-gradient(135deg, #7C3AED, #3B82F6);">
                    渐变
                    主色
                </div>
                <div class="color-swatch" style="background: #3B82F6;">
                    蓝色
                    #3B82F6
                </div>
            </div>
        </div>

        <div class="theme-section">
            <h2>✨ 浮标效果展示</h2>
            <div class="demo-grid">
                <div class="demo-card">
                    <div class="floating-button small">
                        <svg width="14" height="14" viewBox="0 0 24 24" fill="white">
                            <path d="M3,21V3H7L12,14L17,3H21V21H17V8L13,18H11L7,8V21H3Z" stroke="white" stroke-width="0.5"/>
                        </svg>
                    </div>
                    <h4>小尺寸 (32px)</h4>
                    <p>适合紧凑布局</p>
                </div>
                
                <div class="demo-card">
                    <div class="floating-button">
                        <svg width="18" height="18" viewBox="0 0 24 24" fill="white">
                            <path d="M3,21V3H7L12,14L17,3H21V21H17V8L13,18H11L7,8V21H3Z" stroke="white" stroke-width="0.5"/>
                        </svg>
                    </div>
                    <h4>标准尺寸 (48px)</h4>
                    <p>默认使用尺寸</p>
                </div>
                
                <div class="demo-card">
                    <div class="floating-button large">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="white">
                            <path d="M3,21V3H7L12,14L17,3H21V21H17V8L13,18H11L7,8V21H3Z" stroke="white" stroke-width="0.5"/>
                        </svg>
                    </div>
                    <h4>大尺寸 (64px)</h4>
                    <p>突出显示</p>
                </div>
            </div>
        </div>

        <div class="theme-section">
            <h2>🎯 实际使用效果</h2>
            <p>在Claude页面中的prompt旁边显示效果：</p>
            
            <div class="prompt-container">
                <div class="prompt-text">
                    <span>(CINEMATIC STILL), (FILM BY Lana & Lilly Wachowski), (person trapped in towering digital maze walls made of flowing binary code, deep ocean blue-grey atmosphere with electric green data streams), (Matrix-inspired cyberpunk aesthetic), (wide establishing shot)</span>
                </div>
                <div class="floating-button" style="position: absolute; right: -50px; top: 50%;">
                    <svg width="18" height="18" viewBox="0 0 24 24" fill="white">
                        <path d="M3,21V3H7L12,14L17,3H21V21H17V8L13,18H11L7,8V21H3Z" stroke="white" stroke-width="0.5"/>
                    </svg>
                </div>
            </div>

            <div class="prompt-container">
                <div class="prompt-text">
                    <span>(CINEMATIC STILL), (FILM BY Denis Villeneuve), (vast alien landscape with crystalline structures reflecting aurora lights, ethereal purple and blue atmosphere), (sci-fi epic cinematography), (ultra-wide establishing shot)</span>
                </div>
                <div class="floating-button" style="position: absolute; right: -50px; top: 50%;">
                    <svg width="18" height="18" viewBox="0 0 24 24" fill="white">
                        <path d="M3,21V3H7L12,14L17,3H21V21H17V8L13,18H11L7,8V21H3Z" stroke="white" stroke-width="0.5"/>
                    </svg>
                </div>
            </div>
        </div>

        <div class="feature-highlight">
            <h3>🚀 Midjourney主题特色</h3>
            <ul style="text-align: left; margin: 0; padding-left: 20px;">
                <li><strong>品牌一致性：</strong>与Midjourney官方色彩保持一致</li>
                <li><strong>专业感：</strong>紫蓝渐变营造AI艺术创作的专业氛围</li>
                <li><strong>视觉冲击：</strong>渐变色彩更具现代感和科技感</li>
                <li><strong>用户识别：</strong>一眼就能识别这是Midjourney相关功能</li>
                <li><strong>悬停效果：</strong>光泽动画和缩放效果增强交互体验</li>
            </ul>
        </div>

        <div class="interactive-demo">
            <h3>🎮 交互演示</h3>
            <p>悬停在下面的浮标上体验交互效果：</p>
            
            <div style="display: flex; justify-content: center; gap: 30px; margin: 30px 0;">
                <div class="floating-button" onclick="showMessage('发送到Midjourney!')">
                    <svg width="18" height="18" viewBox="0 0 24 24" fill="white">
                        <path d="M3,21V3H7L12,14L17,3H21V21H17V8L13,18H11L7,8V21H3Z" stroke="white" stroke-width="0.5"/>
                    </svg>
                </div>
                
                <div class="floating-button" onclick="showMessage('创意无限!')">
                    <svg width="18" height="18" viewBox="0 0 24 24" fill="white">
                        <path d="M3,21V3H7L12,14L17,3H21V21H17V8L13,18H11L7,8V21H3Z" stroke="white" stroke-width="0.5"/>
                    </svg>
                </div>
                
                <div class="floating-button" onclick="showMessage('AI艺术之旅!')">
                    <svg width="18" height="18" viewBox="0 0 24 24" fill="white">
                        <path d="M3,21V3H7L12,14L17,3H21V21H17V8L13,18H11L7,8V21H3Z" stroke="white" stroke-width="0.5"/>
                    </svg>
                </div>
            </div>
            
            <div id="message" style="margin-top: 20px; font-size: 18px; font-weight: bold; color: #7C3AED; min-height: 25px;"></div>
        </div>

        <div class="theme-section">
            <h2>🔧 技术实现</h2>
            <div style="background: #f8f9fa; padding: 20px; border-radius: 10px; font-family: monospace; font-size: 14px;">
                <strong>CSS渐变代码：</strong><br>
                background: linear-gradient(135deg, #7C3AED, #3B82F6);<br><br>
                
                <strong>阴影效果：</strong><br>
                box-shadow: 0 8px 25px rgba(124, 58, 237, 0.4);<br><br>
                
                <strong>悬停动画：</strong><br>
                transform: scale(1.1) rotate(5deg);<br>
                box-shadow: 0 12px 35px rgba(124, 58, 237, 0.6);
            </div>
        </div>

        <div class="theme-section">
            <h2>🎨 设计理念</h2>
            <p>这个Midjourney主题的设计灵感来源于：</p>
            <ul>
                <li><strong>AI创作的神秘感：</strong>紫色代表创意和想象力</li>
                <li><strong>科技的未来感：</strong>蓝色象征技术和创新</li>
                <li><strong>品牌的专业性：</strong>与Midjourney官方保持视觉一致</li>
                <li><strong>用户的直觉性：</strong>一看就知道是Midjourney功能</li>
            </ul>
        </div>
    </div>

    <script>
        function showMessage(text) {
            const messageDiv = document.getElementById('message');
            messageDiv.textContent = text;
            messageDiv.style.transform = 'scale(1.2)';
            messageDiv.style.transition = 'transform 0.3s ease';
            
            setTimeout(() => {
                messageDiv.style.transform = 'scale(1)';
            }, 300);
            
            setTimeout(() => {
                messageDiv.textContent = '';
            }, 2000);
        }
        
        // 添加页面加载动画
        window.addEventListener('load', () => {
            const buttons = document.querySelectorAll('.floating-button');
            buttons.forEach((button, index) => {
                button.style.opacity = '0';
                button.style.transform = 'scale(0)';
                
                setTimeout(() => {
                    button.style.transition = 'all 0.5s ease';
                    button.style.opacity = '1';
                    button.style.transform = 'scale(1)';
                }, index * 200);
            });
        });
    </script>
</body>
</html>
