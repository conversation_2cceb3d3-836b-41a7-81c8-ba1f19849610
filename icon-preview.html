<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Midjourney图标预览</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .title {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 3px solid #4CAF50;
            padding-bottom: 15px;
        }
        .icon-section {
            margin: 25px 0;
            padding: 20px;
            border: 2px solid #ddd;
            border-radius: 8px;
            background-color: #f8f9fa;
        }
        .icon-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .icon-demo {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 20px;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            background: white;
        }
        .floating-button {
            width: 48px;
            height: 48px;
            background: linear-gradient(135deg, #7C3AED, #3B82F6);
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
            transition: all 0.3s ease;
            margin-bottom: 15px;
        }
        .floating-button:hover {
            transform: scale(1.1);
            box-shadow: 0 6px 16px rgba(0,0,0,0.3);
        }
        .floating-button.small {
            width: 32px;
            height: 32px;
        }
        .floating-button.large {
            width: 64px;
            height: 64px;
        }
        .floating-button.sending {
            background: linear-gradient(135deg, #FF9800, #F57C00);
        }
        .floating-button.success {
            background: linear-gradient(135deg, #4CAF50, #388E3C);
        }
        .floating-button.error {
            background: linear-gradient(135deg, #f44336, #d32f2f);
        }
        .floating-button.midjourney-theme {
            background: linear-gradient(135deg, #7C3AED, #3B82F6);
        }
        .floating-button.dark-theme {
            background: linear-gradient(135deg, #374151, #1F2937);
        }
        .icon-label {
            font-size: 14px;
            color: #666;
            text-align: center;
            margin-top: 10px;
        }
        .comparison {
            display: flex;
            justify-content: space-around;
            align-items: center;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            margin: 20px 0;
        }
        .comparison-item {
            text-align: center;
        }
        .vs {
            font-size: 24px;
            font-weight: bold;
            color: #666;
        }
        .code-block {
            background: #f4f4f4;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            margin: 10px 0;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">🎨 Midjourney图标设计预览</h1>
        
        <div class="icon-section">
            <h2>📊 新旧图标对比</h2>
            <div class="comparison">
                <div class="comparison-item">
                    <div class="floating-button" style="background: linear-gradient(135deg, #4CAF50, #2196F3);">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="white">
                            <path d="M2,21L23,12L2,3V10L17,12L2,14V21Z"/>
                        </svg>
                    </div>
                    <div class="icon-label">旧图标 (绿蓝主题)</div>
                </div>
                
                <div class="vs">VS</div>
                
                <div class="comparison-item">
                    <div class="floating-button">
                        <svg width="18" height="18" viewBox="0 0 24 24" fill="white">
                            <path d="M3,21V3H7L12,14L17,3H21V21H17V8L13,18H11L7,8V21H3Z" stroke="white" stroke-width="0.5"/>
                        </svg>
                    </div>
                    <div class="icon-label">新图标 (Midjourney主题)</div>
                </div>
            </div>
        </div>

        <div class="icon-section">
            <h2>🎯 不同尺寸预览</h2>
            <div class="icon-grid">
                <div class="icon-demo">
                    <div class="floating-button small">
                        <svg width="14" height="14" viewBox="0 0 24 24" fill="white">
                            <path d="M3,21V3H7L12,14L17,3H21V21H17V8L13,18H11L7,8V21H3Z" stroke="white" stroke-width="0.5"/>
                        </svg>
                    </div>
                    <div class="icon-label">小尺寸 (32px)</div>
                </div>
                
                <div class="icon-demo">
                    <div class="floating-button">
                        <svg width="18" height="18" viewBox="0 0 24 24" fill="white">
                            <path d="M3,21V3H7L12,14L17,3H21V21H17V8L13,18H11L7,8V21H3Z" stroke="white" stroke-width="0.5"/>
                        </svg>
                    </div>
                    <div class="icon-label">标准尺寸 (48px)</div>
                </div>
                
                <div class="icon-demo">
                    <div class="floating-button large">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="white">
                            <path d="M3,21V3H7L12,14L17,3H21V21H17V8L13,18H11L7,8V21H3Z" stroke="white" stroke-width="0.5"/>
                        </svg>
                    </div>
                    <div class="icon-label">大尺寸 (64px)</div>
                </div>
            </div>
        </div>

        <div class="icon-section">
            <h2>🌈 不同状态预览</h2>
            <div class="icon-grid">
                <div class="icon-demo">
                    <div class="floating-button">
                        <svg width="18" height="18" viewBox="0 0 24 24" fill="white">
                            <path d="M3,21V3H7L12,14L17,3H21V21H17V8L13,18H11L7,8V21H3Z" stroke="white" stroke-width="0.5"/>
                        </svg>
                    </div>
                    <div class="icon-label">Midjourney主题</div>
                </div>
                
                <div class="icon-demo">
                    <div class="floating-button sending">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="white">
                            <circle cx="12" cy="12" r="10" stroke="white" stroke-width="2" fill="none" stroke-dasharray="31.416" stroke-dashoffset="31.416">
                                <animate attributeName="stroke-dasharray" dur="2s" values="0 31.416;15.708 15.708;0 31.416" repeatCount="indefinite"/>
                                <animate attributeName="stroke-dashoffset" dur="2s" values="0;-15.708;-31.416" repeatCount="indefinite"/>
                            </circle>
                        </svg>
                    </div>
                    <div class="icon-label">发送中</div>
                </div>
                
                <div class="icon-demo">
                    <div class="floating-button success">
                        <svg width="18" height="18" viewBox="0 0 24 24" fill="white">
                            <path d="M9,20.42L2.79,14.21L5.62,11.38L9,14.77L18.88,4.88L21.71,7.71L9,20.42Z"/>
                        </svg>
                    </div>
                    <div class="icon-label">发送成功</div>
                </div>
                
                <div class="icon-demo">
                    <div class="floating-button error">
                        <svg width="18" height="18" viewBox="0 0 24 24" fill="white">
                            <path d="M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z"/>
                        </svg>
                    </div>
                    <div class="icon-label">发送失败</div>
                </div>
            </div>
        </div>

        <div class="icon-section">
            <h2>🎨 不同主题预览</h2>
            <div class="icon-grid">
                <div class="icon-demo">
                    <div class="floating-button" style="background: linear-gradient(135deg, #4CAF50, #2196F3);">
                        <svg width="18" height="18" viewBox="0 0 24 24" fill="white">
                            <path d="M3,21V3H7L12,14L17,3H21V21H17V8L13,18H11L7,8V21H3Z" stroke="white" stroke-width="0.5"/>
                        </svg>
                    </div>
                    <div class="icon-label">绿蓝主题</div>
                </div>
                
                <div class="icon-demo">
                    <div class="floating-button midjourney-theme">
                        <svg width="18" height="18" viewBox="0 0 24 24" fill="white">
                            <path d="M3,21V3H7L12,14L17,3H21V21H17V8L13,18H11L7,8V21H3Z" stroke="white" stroke-width="0.5"/>
                        </svg>
                    </div>
                    <div class="icon-label">Midjourney主题</div>
                </div>
                
                <div class="icon-demo">
                    <div class="floating-button dark-theme">
                        <svg width="18" height="18" viewBox="0 0 24 24" fill="white">
                            <path d="M3,21V3H7L12,14L17,3H21V21H17V8L13,18H11L7,8V21H3Z" stroke="white" stroke-width="0.5"/>
                        </svg>
                    </div>
                    <div class="icon-label">深色主题</div>
                </div>
            </div>
        </div>

        <div class="icon-section">
            <h2>💻 SVG代码</h2>
            <p>新的Midjourney M图标的SVG代码：</p>
            <div class="code-block">
&lt;svg width="18" height="18" viewBox="0 0 24 24" fill="white"&gt;
    &lt;path d="M3,21V3H7L12,14L17,3H21V21H17V8L13,18H11L7,8V21H3Z" 
          stroke="white" 
          stroke-width="0.5"/&gt;
&lt;/svg&gt;
            </div>
            
            <h3>🎯 设计特点：</h3>
            <ul>
                <li><strong>清晰的M字形：</strong>经典的字母M设计，立即识别为Midjourney</li>
                <li><strong>现代感：</strong>简洁的线条和比例，符合现代UI设计</li>
                <li><strong>高对比度：</strong>白色填充和描边，在彩色背景上清晰可见</li>
                <li><strong>可缩放：</strong>SVG格式，在任何尺寸下都保持清晰</li>
                <li><strong>品牌识别：</strong>直接关联Midjourney品牌，用户一眼就知道功能</li>
            </ul>
        </div>

        <div class="icon-section">
            <h2>🚀 交互测试</h2>
            <p>点击下面的按钮测试不同的状态变化：</p>
            
            <div style="text-align: center; margin: 20px 0;">
                <div class="floating-button" id="test-button" onclick="testStates()">
                    <svg width="18" height="18" viewBox="0 0 24 24" fill="white">
                        <path d="M3,21V3H7L12,14L17,3H21V21H17V8L13,18H11L7,8V21H3Z" stroke="white" stroke-width="0.5"/>
                    </svg>
                </div>
                <div class="icon-label">点击测试状态变化</div>
            </div>
            
            <div style="text-align: center;">
                <button onclick="resetTestButton()" style="padding: 10px 20px; margin: 10px; border: none; border-radius: 5px; background: #4CAF50; color: white; cursor: pointer;">重置</button>
            </div>
        </div>
    </div>

    <script>
        let currentState = 0;
        const states = ['default', 'sending', 'success', 'error'];
        
        function testStates() {
            const button = document.getElementById('test-button');
            currentState = (currentState + 1) % states.length;
            
            switch(states[currentState]) {
                case 'sending':
                    button.className = 'floating-button sending';
                    button.innerHTML = `
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="white">
                            <circle cx="12" cy="12" r="10" stroke="white" stroke-width="2" fill="none" stroke-dasharray="31.416" stroke-dashoffset="31.416">
                                <animate attributeName="stroke-dasharray" dur="2s" values="0 31.416;15.708 15.708;0 31.416" repeatCount="indefinite"/>
                                <animate attributeName="stroke-dashoffset" dur="2s" values="0;-15.708;-31.416" repeatCount="indefinite"/>
                            </circle>
                        </svg>
                    `;
                    break;
                case 'success':
                    button.className = 'floating-button success';
                    button.innerHTML = `
                        <svg width="18" height="18" viewBox="0 0 24 24" fill="white">
                            <path d="M9,20.42L2.79,14.21L5.62,11.38L9,14.77L18.88,4.88L21.71,7.71L9,20.42Z"/>
                        </svg>
                    `;
                    break;
                case 'error':
                    button.className = 'floating-button error';
                    button.innerHTML = `
                        <svg width="18" height="18" viewBox="0 0 24 24" fill="white">
                            <path d="M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z"/>
                        </svg>
                    `;
                    break;
                default:
                    resetTestButton();
                    break;
            }
        }
        
        function resetTestButton() {
            const button = document.getElementById('test-button');
            button.className = 'floating-button';
            button.innerHTML = `
                <svg width="18" height="18" viewBox="0 0 24 24" fill="white">
                    <path d="M3,21V3H7L12,14L17,3H21V21H17V8L13,18H11L7,8V21H3Z" stroke="white" stroke-width="0.5"/>
                </svg>
            `;
            currentState = 0;
        }
    </script>
</body>
</html>
