<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>紫绿配色方案测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #7C3AED 0%, #3B82F6 50%, #10B981 100%);
            color: #333;
            min-height: 100vh;
        }
        .container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.2);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }
        h1 {
            color: #7C3AED;
            text-align: center;
            margin-bottom: 40px;
            font-size: 3em;
            font-weight: 700;
            background: linear-gradient(135deg, #7C3AED, #10B981);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        .color-showcase {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 30px;
            margin: 40px 0;
        }
        .color-card {
            background: white;
            border-radius: 16px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            border: 2px solid rgba(124, 58, 237, 0.1);
            position: relative;
            overflow: hidden;
        }
        .color-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: var(--card-color);
        }
        .color-card.purple { --card-color: linear-gradient(135deg, #7C3AED, #3B82F6); }
        .color-card.green { --card-color: linear-gradient(135deg, #10B981, #059669); }
        .color-card.orange { --card-color: linear-gradient(135deg, #F59E0B, #D97706); }
        .button-demo {
            display: flex;
            gap: 12px;
            align-items: center;
            margin: 20px 0;
            justify-content: center;
        }
        .demo-button {
            width: 48px;
            height: 48px;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            opacity: 0.9;
            position: relative;
        }
        .mj-button {
            background: linear-gradient(135deg, #7C3AED, #3B82F6);
            box-shadow: 0 4px 16px rgba(124, 58, 237, 0.4);
        }
        .edit-button {
            background: linear-gradient(135deg, #10B981, #059669);
            box-shadow: 0 4px 16px rgba(16, 185, 129, 0.4);
        }
        .edit-button.editing {
            background: linear-gradient(135deg, #F59E0B, #D97706);
            box-shadow: 0 4px 16px rgba(245, 158, 11, 0.4);
        }
        .demo-button:hover {
            opacity: 1;
            transform: scale(1.1);
        }
        .mj-button:hover {
            box-shadow: 0 6px 20px rgba(124, 58, 237, 0.6);
        }
        .edit-button:hover {
            box-shadow: 0 6px 20px rgba(16, 185, 129, 0.6);
        }
        .edit-button.editing:hover {
            box-shadow: 0 6px 20px rgba(245, 158, 11, 0.6);
        }
        .color-info {
            text-align: center;
            margin-top: 15px;
        }
        .color-name {
            font-weight: 600;
            font-size: 18px;
            margin-bottom: 8px;
        }
        .color-code {
            font-family: 'SF Mono', Monaco, monospace;
            font-size: 12px;
            color: #666;
            background: #f8f9fa;
            padding: 4px 8px;
            border-radius: 4px;
            display: inline-block;
            margin: 2px;
        }
        .feature-section {
            background: linear-gradient(135deg, rgba(124, 58, 237, 0.05), rgba(16, 185, 129, 0.05));
            border: 2px solid rgba(124, 58, 237, 0.2);
            padding: 30px;
            margin: 30px 0;
            border-radius: 16px;
        }
        .comparison-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin: 30px 0;
        }
        .comparison-item {
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 4px 16px rgba(0,0,0,0.1);
        }
        .comparison-item h4 {
            margin-top: 0;
            color: #7C3AED;
        }
        .notification-demo {
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 13px;
            font-weight: 500;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            display: inline-block;
            margin: 5px;
            color: white;
        }
        .notif-save { background: #10B981; }
        .notif-edit { background: #F59E0B; }
        .notif-restore { background: #7C3AED; }
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 16px;
            font-family: 'SF Mono', Monaco, monospace;
            font-size: 14px;
            line-height: 1.5;
            margin: 15px 0;
            position: relative;
        }
        .code-block.editable {
            outline: 2px solid #10B981;
            background-color: rgba(16, 185, 129, 0.05);
            border: 1px solid rgba(16, 185, 129, 0.2);
        }
        .harmony-demo {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 20px;
            padding: 30px;
            background: linear-gradient(135deg, rgba(124, 58, 237, 0.1), rgba(16, 185, 129, 0.1));
            border-radius: 16px;
            margin: 20px 0;
        }
        .color-circle {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 24px;
        }
        .purple-circle { background: linear-gradient(135deg, #7C3AED, #3B82F6); }
        .green-circle { background: linear-gradient(135deg, #10B981, #059669); }
        .orange-circle { background: linear-gradient(135deg, #F59E0B, #D97706); }
        .plus-sign {
            font-size: 24px;
            color: #666;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>💜💚 紫绿配色方案</h1>
        
        <div class="feature-section">
            <h2>🎨 完美的色彩搭配</h2>
            <p><strong>M按钮的经典紫蓝色 + E按钮的清新绿色 = 和谐而专业的视觉体验</strong></p>
            
            <div class="harmony-demo">
                <div class="color-circle purple-circle">M</div>
                <div class="plus-sign">+</div>
                <div class="color-circle green-circle">E</div>
                <div class="plus-sign">=</div>
                <div class="color-circle orange-circle">✏️</div>
            </div>
        </div>

        <div class="color-showcase">
            <!-- M按钮配色 -->
            <div class="color-card purple">
                <div class="button-demo">
                    <div class="demo-button mj-button">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="white">
                            <path d="M3,21V3H7L12,14L17,3H21V21H17V8L13,18H11L7,8V21H3Z"/>
                        </svg>
                    </div>
                </div>
                <div class="color-info">
                    <div class="color-name">M按钮 (发送)</div>
                    <div class="color-code">#7C3AED</div>
                    <div class="color-code">#3B82F6</div>
                    <p style="font-size: 14px; color: #666; margin-top: 10px;">
                        经典的紫蓝色渐变，优雅而专业
                    </p>
                </div>
            </div>

            <!-- E按钮配色 -->
            <div class="color-card green">
                <div class="button-demo">
                    <div class="demo-button edit-button">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="white">
                            <path d="M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z"/>
                        </svg>
                    </div>
                </div>
                <div class="color-info">
                    <div class="color-name">E按钮 (编辑)</div>
                    <div class="color-code">#10B981</div>
                    <div class="color-code">#059669</div>
                    <p style="font-size: 14px; color: #666; margin-top: 10px;">
                        清新的绿色渐变，与紫色形成完美对比
                    </p>
                </div>
            </div>

            <!-- E按钮编辑状态 -->
            <div class="color-card orange">
                <div class="button-demo">
                    <div class="demo-button edit-button editing">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="white">
                            <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41z"/>
                        </svg>
                    </div>
                </div>
                <div class="color-info">
                    <div class="color-name">E按钮 (编辑中)</div>
                    <div class="color-code">#F59E0B</div>
                    <div class="color-code">#D97706</div>
                    <p style="font-size: 14px; color: #666; margin-top: 10px;">
                        温暖的橙色渐变，表示活跃的编辑状态
                    </p>
                </div>
            </div>
        </div>

        <div class="feature-section">
            <h2>🎯 色彩心理学</h2>
            <div class="comparison-grid">
                <div class="comparison-item">
                    <h4>💜 紫色 (M按钮)</h4>
                    <ul>
                        <li><strong>创造力：</strong>激发想象力和艺术灵感</li>
                        <li><strong>专业性：</strong>传达高端和专业的品质</li>
                        <li><strong>神秘感：</strong>符合AI生成艺术的神秘特质</li>
                        <li><strong>品牌识别：</strong>与Midjourney的品牌色调一致</li>
                    </ul>
                </div>
                <div class="comparison-item">
                    <h4>💚 绿色 (E按钮)</h4>
                    <ul>
                        <li><strong>成长：</strong>代表编辑和改进的过程</li>
                        <li><strong>和谐：</strong>与紫色形成互补色对比</li>
                        <li><strong>安全感：</strong>让用户放心进行编辑操作</li>
                        <li><strong>清新感：</strong>提供清晰的功能区分</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="feature-section">
            <h2>🔔 状态通知</h2>
            <div style="text-align: center; margin: 20px 0;">
                <div class="notification-demo notif-save">💾 已保存修改</div>
                <div class="notification-demo notif-edit">✏️ 编辑模式</div>
                <div class="notification-demo notif-restore">🔄 已恢复原始内容</div>
            </div>
            <p style="text-align: center; color: #666; font-size: 14px;">
                每个通知都使用对应功能的主题色，保持视觉一致性
            </p>
        </div>

        <div class="feature-section">
            <h2>📝 编辑模式演示</h2>
            
            <h3>普通状态：</h3>
            <div class="code-block">
A majestic dragon soaring through stormy clouds, lightning illuminating its scales, fantasy art style, dramatic lighting, epic composition
            </div>
            
            <h3>编辑模式（绿色边框与E按钮呼应）：</h3>
            <div class="code-block editable">
A majestic dragon soaring through stormy clouds, lightning illuminating its scales, fantasy art style, dramatic lighting, epic composition --ar 16:9 --v 6 --style raw
            </div>
        </div>

        <div class="feature-section">
            <h2>✨ 设计优势</h2>
            <ul style="font-size: 16px; line-height: 1.8;">
                <li>🎨 <strong>经典配色：</strong>紫色和绿色是经典的互补色组合</li>
                <li>👁️ <strong>视觉层次：</strong>不同颜色清晰区分不同功能</li>
                <li>🔄 <strong>状态反馈：</strong>橙色编辑状态提供明确的视觉提示</li>
                <li>💎 <strong>品质感：</strong>渐变效果增强按钮的精致感</li>
                <li>🎯 <strong>用户友好：</strong>色彩搭配符合用户的视觉习惯</li>
                <li>🌟 <strong>品牌一致：</strong>保持M按钮的经典紫蓝色调</li>
            </ul>
        </div>

        <div class="feature-section">
            <h2>🎊 最终效果</h2>
            <div style="text-align: center; padding: 20px;">
                <div class="button-demo" style="justify-content: center; gap: 20px;">
                    <div class="demo-button mj-button">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="white">
                            <path d="M3,21V3H7L12,14L17,3H21V21H17V8L13,18H11L7,8V21H3Z"/>
                        </svg>
                    </div>
                    <div class="demo-button edit-button">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="white">
                            <path d="M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z"/>
                        </svg>
                    </div>
                </div>
                <p style="margin-top: 20px; font-size: 18px; color: #7C3AED; font-weight: 600;">
                    完美的紫绿搭配，既保持了M按钮的经典魅力，又为E按钮提供了清晰的功能识别！
                </p>
            </div>
        </div>
    </div>
</body>
</html>
